import type { <PERSON><PERSON><PERSON>roller } from '@/decorator/controller'
import type { App, Router, Request, Components, Dict, Core, Utils } from '@/core'
import DynFormV3 from './DynFormV3.vue'
import { h, type Component, reactive, ref, computed } from 'vue'
import { tableBff } from '@/bff/table'
import { useRequest } from '@/hooks'
import { diffObjDep, goback } from '@/utils'
import { CompareEnum, LinkEnum } from '@/constants'
import { usePerm } from '@/hooks/usePerm'
import { router } from '@/router'
import { FormSchema } from '../common/MarFormType'

type State = 'check' | 'edit' | 'copy' | 'new'

interface Meta {
  dynCode: string
  subDynCode?: string
  momKey?: string
  subMomKey?: string
  title: string
  state: State
  api?: {
    info?: string
    save?: string
    update?: string
    subPage?: string
  }
  actionHideList?: string[]
  menuId: string
}

class DynFormPage implements IController {
  app: App;
  router: Router;
  request: Request;
  components: Components;
  dict: Dict;
  utils: Utils;

  public view: Component

  private form = reactive<any>({
    sonDetail: []
  })
  private state: any
  private formConfigs: any
  private child: any
  private meta: Meta
  private rawForm = {}

  protected getFormComponent(): Component {
    return DynFormV3
  }

  private formOptions: Record<string, string> = Reflect.getMetadata('Reflect.OverrideFormOptions', this.constructor)
  private subFormOptions: Record<string, string> = Reflect.getMetadata('Reflect.OverrideSubFormOptions', this.constructor)

  constructor(core: Core) {
    this.app = core.app
    this.router = core.router
    this.request = core.request
    this.components = core.components
    this.dict = core.dict
    this.utils = core.utils

    this.initializeState()
  }

  onValidate(form: any) {
    return true
  }

  private async initializeState() {
    const route = router.currentRoute.value

    let localMeta: Meta;
    let localStateVal: State;

    if (route && route.meta && typeof (route.meta as any).dynCode === 'string') {
        localMeta = route.meta as unknown as Meta;
        localStateVal = localMeta.state || 'new'; // 如果 state 未定义，默认为 'new'
    } else {
        // 提供最小化的 Meta 对象以避免后续错误
        localMeta = { dynCode: '', title: 'Loading...', state: 'new', menuId: '', api: {}, actionHideList: [] } as Meta;
        localStateVal = 'new';
    }

    this.meta = localMeta;
    this.state = ref<State>(localStateVal);

    // 初始化 formConfigs 和 child，以防在 computedProps 中访问时出错
    this.formConfigs = this.formConfigs || [];
    this.child = this.child || null;
    // this.form 已经在类属性中初始化为 reactive<any>({ sonDetail: [] })

    const id = (route && route.query && typeof route.query.id === 'string') ? route.query.id : undefined;

    // 仅当拥有足够的 meta 信息时才尝试加载表单配置和数据
    if (this.meta && this.meta.dynCode && this.meta.menuId) {
        const { formConfigs, child } = await tableBff(this.meta.dynCode, this.meta.menuId);
        this.formConfigs = formConfigs;
        this.child = child;

        // 初始化表单数据
        if (this.state.value === 'check' && id) {
            await this.initializeCheckState(id);
        } else if (this.state.value === 'new') {
            // 如果 route.query 可用，则传递 route；否则传递带空 query 的对象
            this.initializeNewState(route && route.query ? route : { query: {} } as any);
        }
        // 在数据初始化后（如果发生）更新 rawForm
        this.rawForm = JSON.parse(JSON.stringify(this.form));
    } else {
        // 即使跳过，也确保 rawForm 被初始化
        this.rawForm = JSON.parse(JSON.stringify(this.form));
    }
    
    const { hasPerm } = usePerm(); // 假设 usePerm() 在这里调用是安全的

    // this.formOptions 实际上是个map，key是列名，value是class上的方法名 
    // 遍历this.formOptions，并获取方法的引用
    const formOptions = {}
    for (const key in this.formOptions) {
      const methodName = this.formOptions[key]
      formOptions[key] = this[methodName].bind(this)
    }
    const subFormOptions = {}
    for (const key in this.subFormOptions) {
      const methodName = this.subFormOptions[key]
      subFormOptions[key] = this[methodName].bind(this)
    }


    // 计算权限和可见性,这些是给自定义form组件使用的
    const computedProps = {
      submitVisible: computed(() => {
        if (this.state.value === 'check') return false;
        // 根据需要添加权限检查: e.g. && hasPerm('some:perm')
        return ['edit', 'new', 'copy'].includes(this.state.value);
      }).value,
      toggleEditVisible: computed(() => {
        // 根据需要添加权限检查
        return this.state.value === 'check';
      }).value,
      state: this.state.value,
      form: this.form,
      formConfigs: this.formConfigs,
      child: this.child,
      meta: this.meta,
      onSubmit: this.handleSubmit.bind(this),
      onCancel: this.handleCancel.bind(this),
      onToggleEdit: this.handleToggle.bind(this),
      onCopy: this.handleCopy.bind(this),
      onValidate: this.onValidate.bind(this),
      beforeSubmit: this.onBeforeSubmit.bind(this),
      beforeMount: this.onBeforeMount.bind(this),
      mounted: this.onMounted.bind(this),
      activated: this.onActivated.bind(this),
      deactivated: this.onDeactivated.bind(this),
      overrideFormOptions: formOptions,
      overrideSubFormOptions: subFormOptions,
      renderForm: this._renderForm.bind(this),
    };
    console.log('input computedProps', computedProps)

    const formComponentToRender = this.getFormComponent();
    this.view = h(formComponentToRender, computedProps, {
      'ext-form': () => h('div', {}, [
        this._appendForm()
      ])
    });
  }

  private async initializeCheckState(id: string) {
    // 获取主表数据
    const { data: mainData } = await useRequest(
      this.meta.api?.info 
        ? `${this.meta.api.info}/${id}` 
        : `/virtual/${this.meta.dynCode}/info/${id}`
    )
    Object.assign(this.form, mainData.value)

    // 获取子表数据
    if (this.child) {
      const args = {
        filterInfo: [{
          link: LinkEnum.AND,
          data: [{
            colkey: this.meta.subMomKey,
            op: CompareEnum.EQUAL,
            opval: id,
            link: LinkEnum.AND,
          }]
        }],
        page: 1,
        limit: 1000,
        orders: [{ column: 'createDate', asc: false }],
      }
      const { data: childData } = await useRequest<{ list: any[] }>(
        this.meta.api?.subPage 
          ? this.meta.api.subPage 
          : `/virtual/${this.child.dynCode}/page`
      ).post(args)
      
      this.form.sonDetail = childData.value?.list || []
    }
    this.rawForm = JSON.parse(JSON.stringify(this.form))
  }

  private initializeNewState(route: any) {
    const formConfig = this.formConfigs.find((item: any) => item.key === 'new')?.config
    const query = route.query
    const defaultFormValue = formConfig?.formSchema.reduce((acc: any, item: any) => {
      if (query[item.prop]) {
        acc[item.prop] = query[item.prop]
      }
      else if (item.default) {
        acc[item.prop] = !isNaN(Number(item.default)) ? Number(item.default) : item.default
      }
      else if (item.type === 'input') {
        acc[item.prop] = ''
      }
      else if (item.type === 'input-number') {
        acc[item.prop] = 0
      }
      else {
        acc[item.prop] = null
      }
      return acc
    }, {})
    Object.assign(this.form, defaultFormValue)
  }

  private async handleSubmit() {
    const cpForm = JSON.parse(JSON.stringify(this.form))
    const api = this.state.value === 'edit' ? this.meta.api?.update : this.meta.api?.save
    if (!api) return

    const diffData = this.state.value === 'edit'
      ? diffObjDep(cpForm, this.rawForm)
      : diffObjDep(cpForm, {})
    
    const { error } = await useRequest(api).post(diffData)
    if (!error.value) {
      window.$message.success('操作成功')
      goback(true)
    }
  }

  private handleCancel() {
    goback(true)
  }

  private handleToggle() {
    this.state.value = 'edit'
  }

  private handleCopy() {
    if ('id' in this.form) {
      delete this.form.id
    }
    if (this.child) {
      this.form.sonDetail = this.form.sonDetail.map((item: any) => {
        const { id, ...rest } = item
        return rest
      })
    }
    this.state.value = 'copy'
  }

  _renderForm(formValue: any, onUpdateValue: (prop: string, value: any) => void): Record<string, Component> {
    return {}
  }

  // 追加子表单
  _appendForm() {
  }

  onBeforeMount() {
  }
  onMounted() {
  }
  onActivated() {
  }
  onDeactivated() {
  }
  onBeforeSubmit(form: any) {
    return form
  }
}

export default DynFormPage