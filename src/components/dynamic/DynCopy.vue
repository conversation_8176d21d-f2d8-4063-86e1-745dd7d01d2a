<script setup lang="tsx">
import { API } from './DynTableType';
import type { DynFormV2Props } from './DynFormType';
import { useLayout, useRequest } from '@/hooks';
import { tableBff } from '@/bff/table';
import { diffObj, diffObjDep, goback } from '@/utils';
import { t } from '@/modules/i18n';
import MarControlledEditTable from '../common/MarControlledEditTable.vue';
import MarControlledForm from '../common/MarControlledForm.vue';
import { CompareEnum, LinkEnum } from '@/constants';
import { useSlots } from 'vue';

const props = defineProps<DynFormV2Props>()
interface Meta {
  dynCode: string
  subDynCode?: string
  momKey?: string
  subMomKey?: string
  title: string
  state: string
  api?: API
  menuId: string
}

const form = reactive<any>({
  sonDetail: []
})
defineExpose({
  form
})

const route = useRoute()
const { source } = route.query
const meta = route.meta as unknown as Meta
const isEdit = computed(() => meta.state === 'edit')

// 主表监听
const mainFormHideList = ref<string[]>([])
let oldFormValue = {} // 创建一个变量保存旧值

watch(form, (newVal) => {
  if (props.watchMainForm) {
    const diff = diffObj(newVal, oldFormValue)
    props.watchMainForm(form, diff)
    // 更新旧值
    oldFormValue = JSON.parse(JSON.stringify(newVal))
  }
  if (props.hideMainForm) {
    mainFormHideList.value = props.hideMainForm(form)
  }
}, {
  deep: true,
})

const dynCode = meta.dynCode as string
const { url, columns, formConfigs, child } = await tableBff(dynCode, meta.menuId)
const hasChild = !!child

const formConfig = computed(() => formConfigs.find(item => item.key === (isEdit.value ? 'edit' : 'new'))?.config)
// 初始化数据
// 如果是新增模式，需要初始化默认值
if (!isEdit.value) {
  const defaultFormValue = formConfig.value?.formSchema.reduce((acc, item) => {
    if (item.default) {
      acc[item.prop] = !isNaN(Number(item.default)) ? Number(item.default) : item.default
    }
    else if (item.type === 'input') {
      acc[item.prop] = ''
    }
    else if (item.type === 'input-number') {
      acc[item.prop] = 0
    }
    else {
      acc[item.prop] = null
    }
    return acc
  }, {} as Record<string, any>)
  Object.assign(form, defaultFormValue)
}
// 如果是编辑模式，需要初始化数据
if (isEdit.value) {
  // 主表
  const { data: mainData } = await useRequest(meta.api?.info ? `${meta.api.info}/${source}` : `/virtual/${meta.dynCode}/info/${source}`)
  Object.assign(form, mainData.value)
  // 子表
  if (hasChild) {
    let args = {
      filterInfo: [{
        link: LinkEnum.AND,
        data: [{
          colkey: meta.subMomKey,
          op: CompareEnum.EQUAL,
          opval: source,
          link: LinkEnum.AND,
        }]
      }],
      page: 1,
      limit: 1000,
    }
    const { data: childData } = await useRequest<{ list: any[] }>(meta.api?.subPage ? meta.api.subPage : `/virtual/${child.dynCode}/page`).post(args)
    form.sonDetail = []
    if (childData.value) {
      if (Array.isArray(childData.value.list) && childData.value.list.length > 0) {
        form.sonDetail.push(...childData.value.list.map(item => {
          delete item.id
          return item
        }))
      }
    }
  }
}

const { mainContentHeight } = useLayout();
const cardContentHeight = computed(() => {
  return mainContentHeight.value - 32 - 66 - 28;
});

const mainFormRef = ref<any>(null)
const childFormRef = ref<any>(null)
async function handleSubmit() {
  const submitForm = JSON.parse(JSON.stringify(form))
  delete submitForm.id
  const api = meta.api?.save || formConfigs.find(item => item.key === 'new')?.config.api
  const mainFormValid = await mainFormRef.value?.validate()
  const childFormValid = child ? await childFormRef.value?.validate() : true
  if (!mainFormValid || !childFormValid) {
    window.$message.error(t('common.validateFailed'))
    return
  }
  const formData = props.beforeSubmit ? await props.beforeSubmit(submitForm) : submitForm
  const diffData = diffObjDep(formData, {})
  if (typeof api === 'string') {
    const { error } = await useRequest(api).post(diffData)
    if (!error.value) {
      window.$message.success(t('common.operateSuccess'))
      goback(true)
    }
  }
}

const childFormConfig = computed(() => child?.formConfigs.find(item => item.key === (isEdit.value ? 'edit' : 'new'))?.config)

function handleCancel() {
  goback(true)
}
</script>

<template>
  <NCard>
    <main :style="{ height: `${cardContentHeight}px` }" class="overflow-y-auto no-scrollbar relative">
      <section class="flex justify-between pb-4 sticky top-0 left-0 right-0 z-10">
        <div class="flex gap-2">
          <NButton type="primary" @click="handleSubmit">
            <template #icon>
              <div class="i-icon-park-outline-save"></div>
            </template>
            {{ $t('common.submit') }}
          </NButton>
          <NButton @click="handleCancel">
            <template #icon>
              <div class="i-icon-park-outline-close"></div>
            </template>
            {{ $t('common.cancel') }}
          </NButton>
          <slot name="actions" />
        </div>
      </section>

      <slot name="free-zone" />

      <div class="space-y-4">
        <NCard class="dark:bg-transparent bg-#fafafa">
          <MarControlledForm ref="mainFormRef" v-if="formConfig" v-bind="formConfig" v-model:model-value="form" :hide-list="mainFormHideList" />
          <div v-else class="error-tip">
            {{ $t('tip.fetchConfigError') }}
          </div>
        </NCard>

        <NCard class="dark:bg-transparent bg-#fafafa" v-if="childFormConfig">
          <MarControlledEditTable ref="childFormRef" v-bind="childFormConfig" v-model:model-value="form.sonDetail">
            <template #sub-actions>
              <slot name="sub-actions" />
            </template>
          </MarControlledEditTable>
        </NCard>
      </div>
    </main>
  </NCard>
</template>

<style scoped>
/* 隐藏滚动条但保留滚动功能 */
.no-scrollbar {
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
</style>