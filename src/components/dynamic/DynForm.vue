<script setup lang="tsx">
import { NButton } from 'naive-ui';
import { tableBff } from '@/bff/table';
import { MarForm } from '@/components';
import { goback } from '@/utils';
import { useFormLifecycle } from './hooks/useFormLifecycle';
import { API } from './DynTableType';
import type { Ref } from 'vue';
import type { Component } from 'vue';

const props = defineProps<{
  watchForm?: (
    changedProps: Record<string, any>,
    form: Ref<Record<string, any>>,
    initializing: boolean
  ) => void
  hideForm?: (form: Record<string, any>) => string[]
  beforeSubmit?: (form: Record<string, any>) =>
    | Promise<Array<any>>
    | Array<any>
  breakLine?: string[]
  renderForm?: (formValue: any, onUpdateValue: (prop: string, value: any) => void) => Record<string, Component>
}>()

interface Meta {
  dynCode: string
  subDynCode?: string
  momKey?: string
  subMomKey?: string
  title: string
  state: string
  api?: API
  actionHideList?: string[]
  menuId: string
}

const route = useRoute()
const meta = route.meta as unknown as Meta
const dynCode = meta.dynCode as string

const { url, columns, formConfigs } = await tableBff(dynCode, meta.menuId)
const mainFormRef = ref<InstanceType<typeof MarForm> | null>(null)
const { state } = useFormLifecycle(meta)

async function handleSubmit() {
  const data = await mainFormRef.value?.submit()
  if (data) {
    await goback(true)
  }
}

function resetState() {
  state.value = 'new'
  mainFormRef.value?.reset()
}

async function handleCancel() {
  resetState()
  await goback(true)
}

const formConfig = computed(() => formConfigs.find(item => item.key === state.value)?.config)
const computedApi = computed(() => {
  if (state.value === 'edit') {
    return meta.api?.update || formConfig.value?.api
  }
  return meta.api?.save || formConfig.value?.api
})
</script>

<template>
  <main class="form-container">
    <NButton @click="handleCancel" strong secondary class="back-button">
      <template #icon>
        <div class="i-icon-park-outline-arrow-left h-1.2rem w-1.2rem" />
      </template>
      {{ $t('common.exit') }}
    </NButton>

    <NCard class="form-card">
      <template #header>
        <div class="flex p-1 px-3 flex-col justify-center">
          <h2 class="text-1.2rem font-450 mr-3 opacity-80 mb-2">{{ $t('components.dynForm.title') }}</h2>
          <div class="flex items-center">
            <NButton @click="handleSubmit" size="small" strong secondary type="primary" class="mr-1">
              <span>{{ $t('common.submit') }}</span>
            </NButton>
          </div>
        </div>
      </template>
      <MarForm ref="mainFormRef" v-if="formConfig" v-bind="formConfig" :api="computedApi || ''" :col="4" actions="none"
        class="main-form" :watch-form="props.watchForm" :hide-form="props.hideForm" :before-submit="props.beforeSubmit"
        :break-line="props.breakLine" :render-form="props.renderForm" />
      <div v-else class="error-tip">
        {{ $t('tip.fetchConfigError') }}
      </div>
    </NCard>
  </main>
</template>

<style scoped>
.form-container {
  @apply min-h-60vh bg-white dark:bg-[#18181C] rounded-sm overflow-hidden p-6;
}

.back-button {
  @apply mb-4 transition-all duration-300 hover:translate-x-1;
}

.form-card {
  @apply mb-6 transition-all duration-300 hover:shadow-md;
}

.form-card:deep(.n-card-header) {
  @apply p-0 mb-3;
}

.form-card:deep(.n-card) {
  @apply border-none;
}

.main-form {
  @apply p-4;
}

.error-tip {
  @apply text-red-500 text-center p-4;
}

/* 深色模式适配 */
:deep(.dark) {
  .form-card {
    @apply bg-[#1a1a1a] border-[#333];
  }
}
</style>
