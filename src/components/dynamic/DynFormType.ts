import type { Component, Ref, VNode } from 'vue';

type ColumnKey = string
type PropKey = string

interface DynFormV2Props {
  watchMainForm?: (
    form: Record<string, any>,
    changed: Record<string, any>
  ) => void
  // 当主表值发生变化时，会触发该方法，返回需要隐藏的字段
  hideMainForm?: (form: Record<string, any>) => PropKey[]
  renderMainForm?: (formValue: any, onUpdateValue: (prop: string, value: any) => void) => Record<string, Component>
  
  watchSubForm?: (
  ) => void
  hideSubForm?: (form: Record<string, any>) => string[]
  renderSubForm?: (formValue: any, onUpdateValue: (prop: string, value: any) => void) => Record<string, Component>

  rowActions?: (rowData: any, index: number | string) => VNode[]
  breakLine?: string[]
  // 提交前对表单数据进行预处理
  beforeSubmit?: (form: Record<string, any>) => Promise<Record<string, any>>
}

interface DynFormV3Props {
  watchMainForm?: (
    form: Record<string, any>,
    changed: Record<string, any>
  ) => void
  // 当主表值发生变化时，会触发该方法，返回需要隐藏的字段
  hideMainForm?: (form: Record<string, any>) => PropKey[]
  renderMainForm?: (formValue: any, onUpdateValue: (prop: string, value: any) => void) => Record<string, Component>
  
  watchSubForm?: (
  ) => void
  hideSubForm?: (form: Record<string, any>) => string[]
  renderSubForm?: (formValue: any, onUpdateValue: (prop: string, value: any) => void) => Record<string, Component>

  rowActions?: (rowData: any, index: number | string) => VNode[]
  breakLine?: string[]
  // 提交前对表单数据进行预处理
  beforeSubmit?: (form: Record<string, any>) => Promise<Record<string, any>>

  permission?: {
    edit?: string
    save?: string
  }
}

export type {
  DynFormV2Props,
  DynFormV3Props
}