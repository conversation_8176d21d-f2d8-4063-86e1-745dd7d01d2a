import { RowActionType, UseTableReturn } from '@/hooks'
import type { Component, Ref } from 'vue'
import type { ButtonProps } from 'naive-ui'
import { Columns } from '@/utils'

/**
 * 用于覆写动态表中默认的请求地址
 * 比如默认的更新地址是 /virtual/${dynCode}/update/${id}
 * 你可以覆写为自己想要的
 */
interface API {
  // 列表地址
  page?: string
  // 详情接口
  info?: string
  // 新增地址
  save?: string
  // 更新地址
  update?: string
  // 导入模板地址
  importTemplate?: string
  // 导入地址
  importTable?: string
  // 导出地址
  exportTable?: string
  // 删除地址
  delete?: string

  // 子列表地址
  subPage?: string
  // 子列表新增地址
  subSave?: string
  // 子列表更新地址
  subUpdate?: string
  // 子列表删除地址
  subDelete?: string
  // 子列表详情地址
  subInfo?: string
}

type ColumnKey = string

/**
 * 用于指定列的渲染内容
 * 可以是组件或者一个返回组件的函数
 * 如果是组件，则将当前行的数据作为props传递给组件
 * 如果是函数，则将当前行的数据作为参数传递给函数
 * 
 * @example 比如你要在username列中显示用户头像，可以这样写
 * 其中 UserAvatar 是你自己定义的.vue组件
 * {
 *   username: (row) => UserAvatar
 * }
 */
type ColumnRender = Record<ColumnKey, Component | ((row: any) => Component)>

/**
 * 用于指定工具栏的按钮
 * 
 * @example 比如你要在工具栏中添加一个「入库」按钮，可以这样写
 * {
 *   label: '入库',
 *   icon: 'i-icon-park-outline-inbound',
 *   classStr: 'bg-blue-500',
 *   perm: 'inbound',
 *   onClick: () => {
 *   }
 * }
 */
type ActionBar = Array<{
  label: string
  // 按钮图标，参数是 hero icons 的 key
  icon?: string
  // 按钮样式类名
  classStr?: string
  // 按钮权限
  perm?: string
  // 按钮点击触发的事件
  onClick: () => void | Promise<void>
  // 按钮类型
  type?: ButtonProps['type']
  // 按钮计数源
  countSource?: Ref<Array<any>>
  // 按钮是否锁定
  lock?: boolean
}>

type BatchOperate = Array<{
  perm?: string
  label: string
  key: string
  props: Record<string, any>
}>

/**
 * 页面默认会显示「保存」、「编辑」、「删除」、「导入」、「导出」、「高级查询」按钮
 * 如果你不想显示这些按钮，可以在这里指定
 * 也可以通过权限控制
 * 
 * @example 比如你要隐藏「编辑」和「删除」按钮，可以这样写
 * ['edit', 'delete']
 */
type ButtonHideList = Array<
  'save'
  | 'edit'
  | 'delete'
  | 'deleteMul'
  | 'importTable'
  | 'exportTable'
  | 'advancedQuery'
  | 'config'
>

/**
 * 其他配置
 */
interface OtherOptions {
  buttonHideList?: ButtonHideList
  subButtonHideList?: ButtonHideList
  // 请求前处理请求参数
  beforeRequest?: (form: any) => any
  // 请求后处理返回数据
  afterRequest?: (data: any) => any
  /**
   * 是否进入页面立即请求
   * 
   * @default true
   */
  immediate?: 'on' | 'off'
  defaultSort?: {
    columnKey?: string
    order?: 'ascend' | 'descend'
  },
}

interface TablePageOptions {
  rawMeta?: any
  api?: API
  columnsRender?: ColumnRender
  subColumnsRender?: ColumnRender
  rowOperate?: RowActionType
  actionBar?: (r: UseTableReturn) => ActionBar
  batchOperate?: (r: UseTableReturn) => BatchOperate
  otherOptions?: OtherOptions
  hide?: {
    rowOperate?: boolean
    columns?: string[]
  }
  multiLine?: boolean
  defaultQueryValue?: Record<string, any>,
  extQuery?: Columns[]
  pageSizeOptions?: number[]
  defaultPageSize?: number
  rowProps?: (rowData: object, rowIndex: number) => Record<string, any>
}

export type {
  API,
  ColumnRender,
  ActionBar,
  BatchOperate,
  OtherOptions,
  TablePageOptions,
  RowActionType
}