<script setup lang="ts">
import type { FormConfig } from '../custom/Dialog/FormDialogV2';
import FormDialogV2 from '../custom/Dialog/FormDialogV2';
import type { Columns } from '@/utils/entity';
import { useDelDialog, useRequest, useTable } from '@/hooks';
import type { QueryForm, URL } from '@/hooks';
import { CompareEnum, LinkEnum } from '@/constants';
import type { ChildExt } from '@/bff/table';
import TablePageSettingDialog from '@/components/custom/Dialog/TablePageSettingDialog.vue';
import { t } from '@/modules/i18n';

const visible = ref(false)
let row = {}

const props = defineProps<{
  url: URL
  columns: Columns[]
  formConfigs: FormConfig[]
  bff: any
  buttonHideList: string[]
  defaultSort?: { columnKey: string, order: 'ascend' | 'descend' }
} & ChildExt>()

watch(() => props.buttonHideList, () => {
  console.log(props.buttonHideList, 'xxx')
}, {
  immediate: true
})

const tablePageSettingDialogRef = ref<InstanceType<typeof TablePageSettingDialog>>()
const { state, refresh, on, checked } = useTable(props.url, props.columns, {
  beforeRequest: (params) => {
    return [
      ...params,
      {
        link: LinkEnum.AND,
        data: [
          {
            colkey: props.subMomKey,
            op: CompareEnum.EQUAL,
            opval: row[props.momKey],
            link: LinkEnum.AND,
          },
        ],
      },
    ] satisfies QueryForm
  },
  immediate: 'off',
  checkable: true,
  actionHideList: [...props.buttonHideList, 'importTable', 'exportTable'] as any,
  defaultSort: props.defaultSort,
})
const computedConfigs = computed(() => {
  return props.formConfigs.map((item) => {
    return {
      ...item,
      config: {
        onSuccess: () => {
          refresh()
        },
        ...item.config,
      },
    }
  })
})
const formDialogRef = ref<InstanceType<typeof FormDialogV2>>()
on((e, row) => {
  if (e === 'new') {
    formDialogRef.value?.open('new')
  }
  if (e === 'edit') {
    formDialogRef.value?.open('edit', {}, {
      overrideFetchData: async () => {
        const { data } = await useRequest(`/virtual/${props.dynCode}/info/${row.id}`)
          .get()
        if (data.value) {
          return data.value
        }
        return {}
      },
    })
  }
  if (e === 'delete') {
    useDelDialog({
      api: `/virtual/${props.dynCode}/deleteMul`,
      params: [row.id],
      afterSuccess: () => {
        refresh()
      },
    })
  }
  if (e === 'deleteMul') {
    if (checked.value.length === 0) {
      window.$message.error(t('system.tips.pleaseAtLeastSelectOne'))
      return
    }
    useDelDialog({
      api: `/virtual/${props.dynCode}/deleteMul`,
      params: checked.value.map((item: any) => item.id),
      afterSuccess: () => {
        window.$message.success(t('common.operationSuccess'))
        refresh()
      },
    })
  }
  if (e === 'config') {
    tablePageSettingDialogRef.value?.open()
  }
})

defineExpose({
  open: (r: Record<string, any>) => {
    row = r
    refresh()
    visible.value = true
  },
})
</script>

<template>
  <n-drawer v-model:show="visible" width="80%" placement="right">
    <n-drawer-content>
      <FormDialogV2 ref="formDialogRef" :form-configs="computedConfigs" label="" />
      <MarWrapper v-model="state">
        <template #free-zone>
          <TablePageSettingDialog ref="tablePageSettingDialogRef" :dyn-code="dynCode" :bff="bff" />
        </template>
      </MarWrapper>
    </n-drawer-content>
  </n-drawer>
</template>
