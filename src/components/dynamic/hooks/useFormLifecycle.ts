import { MarEditTable, MarForm } from '@/components';
import { CompareEnum, LinkEnum } from '@/constants/query';
import { useRequest } from '@/hooks';
import { router } from '@/router';
import { RowData } from '@/types';

function useFormLifecycle(meta: any) {
  const state = ref<'new' | 'edit'>(meta.state as 'new' | 'edit')
  const infoParam = ref<string>('')
  const dynCode = meta.dynCode as string
  const mainFormData = ref<any>({})
  const subDynCode = meta.subDynCode as string
  const mainFormRef = useTemplateRef<InstanceType<typeof MarForm> | null>('mainFormRef')
  const subFormRef = useTemplateRef<InstanceType<typeof MarEditTable> | null>('subFormRef')
  watch(state, async (newVal) => {
    if (newVal === 'edit') {
      while (!mainFormRef.value) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      mainFormRef.value?.init({}, {
        overrideFetchData: async () => {
          const { data } = await useRequest(meta.api?.info ? `${meta.api.info}/${infoParam.value}` : `/virtual/${dynCode}/info/${infoParam.value}`)
            .get()
          if (data.value) {
            return data.value
          }
          return {}
        },
      })
    }
  })

  onMounted(async () => {

    while (!mainFormRef.value) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    if (state.value !== 'edit') {
      mainFormRef.value.init()
      return
    }

    mainFormRef.value?.init({}, {
      overrideFetchData: async () => {
        const row = router.currentRoute.value.query
        const { data } = await useRequest(meta.api?.info ? `${meta.api.info}/${row?.id}` : `/virtual/${dynCode}/info/${row?.id}`)
          .get()
        if (data.value) {
          mainFormData.value = data.value
          return data.value
        }
        return {}
      },
    })
    while (!subFormRef.value) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    let args = {
      filterInfo: [{
        link: LinkEnum.AND,
        data: [{
          colkey: meta.subMomKey,
          op: CompareEnum.EQUAL,
          opval: router.currentRoute.value.query['id'],
          link: LinkEnum.AND,
        }]
      }],
      page: 1,
      limit: 1000,
    }
    const { data } = await useRequest<{ list: RowData[] }>(meta.api?.subPage || `/virtual/${subDynCode}/page`).post(args)
    if (data.value) {
      subFormRef.value?.init(data.value.list)
    }
  })

  return {
    state,
    infoParam,
    mainFormData,
  }
}

export {
  useFormLifecycle
}