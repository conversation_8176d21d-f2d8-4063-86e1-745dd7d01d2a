<script lang="ts">
import { defineComponent, ref, computed, provide, h } from 'vue'
import { useRoute } from 'vue-router'
import SubComp from './SubComp.vue'
import SubCompDrawer from './SubCompDrawer.vue'
import { FormDialogV2 } from '@/components/index'
import { metaTableBff, tableBff } from '@/bff/table'
import { useTable } from '@/hooks/useTable'
import { getRefresh, RowActionType, RowAction, useDelDialog, usePerm } from '@/hooks'
import { goto, isPromiseLike } from '@/utils'
import { t } from '@/modules/i18n'
import TablePageSettingDialog from '@/components/custom/Dialog/TablePageSettingDialog.vue'
import { router } from '@/router'
import { pageBucket } from '@/views/bucket'
import DynFormV2 from './DynFormV2.vue'
import { systemConfig } from '@/config'
import { NPopover } from 'naive-ui'
import { uid } from 'radash'

export default defineComponent({
  name: 'DynTable',
  components: {
    MarWrapper: () => import('@/components/index').then(m => m.MarWrapper),
    NButton: () => import('naive-ui').then(m => m.NButton),
    FormDialogV2,
    SubCompDrawer,
    TablePageSettingDialog
  },
  props: {
    api: Object,
    columnsRender: Object,
    subColumnsRender: Object,
    rowOperate: [Array, Function],
    actionBar: Function,
    batchOperate: Function,
    otherOptions: Object,
    hide: Object,
    rawMeta: Object,
    dynCode: String,
    rowProps: Function
  },
  async setup(props) {
    // get route meta
    const route = useRoute()
    const meta = route.meta
    let dynCode = meta.dynCode as string
    if (!dynCode) {
      dynCode = props.dynCode as string
    }
    const subCompDrawerRef = ref<InstanceType<typeof SubCompDrawer>>()

    let bff
    if (props.rawMeta) {
      bff = await metaTableBff(props.rawMeta, dynCode, handleCheck)
    } else {
      bff = await tableBff(dynCode, meta.menuId as string, handleCheck)
    }
    let { columns, formConfigs, child } = bff
    if (systemConfig.table?.firstColumnRouter) {
      const firstColumnRender = columns[0].render
      columns[0].render = (row: any) => h(NPopover, {
        trigger: 'hover',
      }, {
        trigger: () => h('a', {
          class: 'underline text-blue-500 cursor-pointer',
          onClick: () => handleCheck?.(row)
        }, typeof firstColumnRender === 'function' ? firstColumnRender(row) : row[columns[0].key]),
        default: () => h('div', typeof firstColumnRender === 'function' ? firstColumnRender(row) : row[columns[0].key])
      })
    }
    if (props.columnsRender) {
      Object.keys(props.columnsRender).forEach(key => {
        for (let i = 0; i < columns.length; i++) {
          if (columns[i].key === key) {
            columns[i].render = props.columnsRender?.[key]
            break
          }
        }
      })
    }

    // 处理子表的列渲染配置
    if (child && props.subColumnsRender) {
      Object.keys(props.subColumnsRender).forEach(key => {
        for (let i = 0; i < child.columns.length; i++) {
          if (child.columns[i].key === key) {
            child.columns[i].render = props.subColumnsRender?.[key]
            break
          }
        }
      })
    }

    // 使用 props.api 覆盖 bff 返回的 url
    const url = computed(() => {
      if (typeof bff.url === 'string') {
        return bff.url
      }
      if (!props.api) {
        return {
          delete: `/virtual/${dynCode}/deleteMul`,
          save: `/virtual/${dynCode}/save`,
          update: `/virtual/${dynCode}/update`,
          ...bff.url
        }
      }
      return {
        page: props.api.page || bff.url.page,
        exportTable: props.api.exportTable || bff.url.exportTable,
        importTable: props.api.importTable || bff.url.importTable,
        importTemplate: props.api.importTemplate || bff.url.importTemplate,
        delete: props.api.delete || `/virtual/${dynCode}/deleteMul`,
        save: props.api.save || `/virtual/${dynCode}/save`,
        update: props.api.update || `/virtual/${dynCode}/update`
      }
    })

    // 添加子表 URL 的计算属性
    const childUrl = computed(() => {
      if (!child) return { page: '', save: '', update: '', delete: '', info: '' }
      if (typeof child.url === 'string') {
        return { 
          page: child.url,
          save: `/virtual/${child.dynCode}/save`,
          update: `/virtual/${child.dynCode}/update`,
          delete: `/virtual/${child.dynCode}/deleteMul`,
          info: `/virtual/${child.dynCode}/info`
        }
      }
      if (!props.api || !child.url) return {
        page: `/virtual/${child.dynCode}/page`,
        save: `/virtual/${child.dynCode}/save`,
        update: `/virtual/${child.dynCode}/update`,
        delete: `/virtual/${child.dynCode}/deleteMul`,
        info: `/virtual/${child.dynCode}/info`
      }
      
      return {
        page: props.api.subPage || child.url.page || `/virtual/${child.dynCode}/page`,
        save: props.api.subSave || child.url.save || `/virtual/${child.dynCode}/save`,
        update: props.api.subUpdate || child.url.update || `/virtual/${child.dynCode}/update`,
        delete: props.api.subDelete || child.url.delete || `/virtual/${child.dynCode}/deleteMul`,
        info: props.api.subInfo || child.url.info || `/virtual/${child.dynCode}/info`
      }
    })

    const { hasPerm } = usePerm()
    const permSet = [
      'save',
      'deleteMul',
      'exportTable',
      'importTable',
      'edit',
    ]
    const permHideList = typeof url.value === 'string' ? [] : permSet.filter(item => {
      switch (item) {
        case 'save':
        case 'exportTable':
        case 'importTable': {
          return !hasPerm(url.value[item].slice(1).replaceAll('/', ':'))
        }
        case 'edit':
          return !hasPerm(url.value.update.slice(1).replaceAll('/', ':'))
        case 'deleteMul':
          return !hasPerm(url.value.delete.slice(1).replaceAll('/', ':'))
      }
    })
    if (permHideList.includes('deleteMul')) {
      permHideList.push('delete')
    }
    // 合并 props.otherOptions?.actionHideList 和 permHideList
    const actionHideList = computed(() => {
      const hideList = new Set([...(props.otherOptions?.buttonHideList || []), ...permHideList])
      return Array.from(hideList)
    })
    const subButtonHideList = computed(() => {
      const hideList = new Set([...(props.otherOptions?.subButtonHideList || [])])
      return Array.from(hideList)
    })

    function handleCheck(row: any) {
      const currentPath = router.currentRoute.value.fullPath.split('?')[0]
      const formPath = `${currentPath}/form`
      const formPage = pageBucket[formPath]
      goto(`/dynamic/${dynCode}/edit/${uid(4)}-${row.id.slice(-4)}`, {
        component: formPage ? formPage : DynFormV2,
        meta: {
          dynCode,
          subDynCode: child?.dynCode,
          momKey: child?.momKey,
          subMomKey: child?.subMomKey,
          title: `${t(meta.title as string)}-${t('common.edit')}-${row.id.slice(-4)}`,
          state: 'edit',
          api: props.api,
          actionHideList: actionHideList.value
        },
        query: {
          ...row
        }
      })
    }
    const rowOperate = (row: any) => {
      let operate: RowActionType = []
      if (child) {
        operate.push({
          label: t('common.subTableDetail'),
          color: '#2080F0',
          click: (row) => {
            subCompDrawerRef.value?.open(row)
          },
        } as RowAction)
      }
      if (props.rowOperate) {
        if (typeof props.rowOperate === 'function') {
          operate.push(...props.rowOperate(row))
        } else {
          operate.push(...(props.rowOperate as RowAction[]))
        }
      }
      operate.push({
        label: t('common.check'),
        click: () => {
          handleCheck(row)
        }
      })
      return operate
    }
    const formDialogRef = ref<InstanceType<typeof FormDialogV2>>()
    // 使用计算后的 url
    const useTableReturn = useTable(url.value, columns, {
      checkable: true,
      actionHideList: [...actionHideList.value, 'edit'] as ("save" | "deleteMul" | "exportTable" | "importTable" | "edit" | "delete" | "advancedQuery" | "config")[],
      expandRender: child ? ({ row }) => {
        if (child) {
          return h('div', {
            style: {
              width: '80vw',
              position: 'sticky',
              left: 0,
            }
          }, [
            h(SubComp, {
              url: childUrl.value,
              columns: child.columns,
              row,
              subMomKey: child.subMomKey,
              momKey: child.momKey,
              dynCode: child.dynCode,
              defaultSort: {
                columnKey: 'id',
                order: 'descend',
              }
            })
          ])
        }
        return null
      } : undefined,
      rowActions: rowOperate,
      defaultSort: props.otherOptions?.defaultSort,
      immediate: props.otherOptions?.immediate,
      hide: {
        rowAction: props.hide?.rowOperate
      },
      rowProps: props.rowProps as (rowData: object, rowIndex: number) => Record<string, any>
    })
    const { state, on, refresh, checked } = useTableReturn
    const actionBar = props.actionBar?.(useTableReturn)
    const batchOperate = props.batchOperate?.(useTableReturn).filter(item => item.perm ? hasPerm(item.perm) : true)
    on((e, row) => {
      const currentPath = router.currentRoute.value.fullPath.split('?')[0]
      const formPath = `${currentPath}/form`
      const formPage = pageBucket[formPath]
      if (e === 'new') {
        goto(`/dynamic/${dynCode}/new`, {
          component: formPage ? formPage : DynFormV2,
          meta: {
            dynCode,
            title: `${t(meta.title as string)}-${t('common.save')}`,
            state: 'new',
            momKey: child?.momKey,
            subMomKey: child?.subMomKey,
            api: props.api
          },
        })
      }
      if (e === 'delete') {
        useDelDialog({
          api: props.api?.delete || `/virtual/${dynCode}/deleteMul`,
          params: [row.id],
          afterSuccess: () => {
            refresh()
          },
        })
      }
      if (e === 'deleteMul') {
        if (checked.value.length === 0) {
          window.$message.error(t('system.tips.pleaseAtLeastSelectOne'))
          return
        }
        useDelDialog({
          api: props.api?.delete || `/virtual/${dynCode}/deleteMul`,
          params: checked.value.map(item => item.id),
          afterSuccess: () => {
            refresh()
          },
        })
      }
      if (e === 'drawer') {
        subCompDrawerRef.value?.open(row)
      }
      if (e === 'config') {
        tablePageSettingDialogRef.value?.open()
      }
    })

    const tablePageSettingDialogRef = ref<InstanceType<typeof TablePageSettingDialog>>()

    const lockArr = ref<string[]>([])
    watch(lockArr, (newVal) => {
      console.log('lockArr', newVal)
    })

    const handleActionBarClick = async (item: any) => {
      if (lockArr.value.includes(item.label)) {
        return
      }
      if (item.lock) {
        lockArr.value.push(item.label)
      }
      if (isPromiseLike(item.onClick)) {
        item.onClick().finally(() => {
          lockArr.value = lockArr.value.filter(i => i !== item.label)
        })
      } else {
        try {
          item.onClick()
        } catch (e) {
          console.error(e)
        } finally {
          lockArr.value = lockArr.value.filter(i => i !== item.label)
        }
      }
    }

    return {
      getExpose: () => ({
        state,
        refresh,
        on,
        checked,
      }),
      state,
      actionBar,
      batchOperate,
      hasPerm,
      formConfigs,
      child,
      formDialogRef,
      subCompDrawerRef,
      tablePageSettingDialogRef,
      dynCode,
      bff,
      refresh,
      t,
      childUrl,
      subButtonHideList,
      lockArr,
      isPromiseLike,
      handleActionBarClick,
    }
  },
  activated() {
    if (getRefresh(router.currentRoute.value.fullPath.split('?')[0])) {
      this.refresh()
    }
  },
})
</script>

<template>
  <MarWrapper v-model="state">

    <template #toolbarExt>
      <template v-for="item in actionBar" :key="item.label">
        <NButton size="small" :loading="lockArr.includes(item.label)" tertiary :type="item.type" strong v-if="item.perm ? hasPerm(item.perm) : true"
          @click="() => handleActionBarClick(item)" :class="item.classStr">
          <template #icon v-if="item.icon">
            <div :class="item.icon" />
          </template>
          {{ lockArr.includes(item.label) ? t('common.loading') : item.label }}
        </NButton>
      </template>
    </template>

    <template #toolbarLeft>
      <NDropdown v-if="batchOperate && batchOperate.length > 0" :options="batchOperate" trigger="hover">
        <NButton size="small" strong secondary>
          <template #icon>
            <div class="i-mdi-dots-vertical"></div>
          </template>
          {{ t('common.batchOperate') }}
        </NButton>
      </NDropdown>
    </template>

    <template #free-zone>
      <slot name="free-zone" />
      <FormDialogV2 ref="formDialogRef" :form-configs="formConfigs" label="" />
      <SubCompDrawer ref="subCompDrawerRef" v-if="child" :url="childUrl" :columns="child.columns"
        :dyn-code="child.dynCode" :sub-mom-key="child.subMomKey" :mom-key="child.momKey"
        :form-configs="child.formConfigs" :bff="bff" :button-hide-list="subButtonHideList" :default-sort="{
          columnKey: 'id',
          order: 'descend',
        }" />
      <TablePageSettingDialog ref="tablePageSettingDialogRef" :dyn-code="dynCode" :bff="bff" />
    </template>
  </MarWrapper>
</template>
