<script setup lang="ts">
import type { Columns } from '@/utils/entity';
import { useTable } from '@/hooks';
import type { QueryForm, URL } from '@/hooks';
import { CompareEnum, LinkEnum } from '@/constants';
import type { ChildExt } from '@/bff/table';

const props = defineProps<{
  url: URL
  columns: Columns[]
  row: Record<string, any>
  defaultSort?: { columnKey: string, order: 'ascend' | 'descend' }
} & ChildExt>()

const { state } = useTable(props.url, props.columns, {
  pageSizeOptions: [5, 10, 50],
  actionHideList: ['advancedQuery', 'delete', 'deleteMul', 'edit', 'exportTable', 'importTable', 'save', 'config', 'groupSearch'],
  inner: true,
  beforeRequest: (params) => {
    return [
      ...params,
      {
        link: LinkEnum.AND,
        data: [
          {
            colkey: props.subMomKey,
            op: CompareEnum.EQUAL,
            opval: props.row.id,
            link: LinkEnum.AND,
          },
        ],
      },
    ] satisfies QueryForm
  },
  defaultSort: props.defaultSort,
})
</script>

<template>
  <MarWrapper v-model="state" />
</template>
