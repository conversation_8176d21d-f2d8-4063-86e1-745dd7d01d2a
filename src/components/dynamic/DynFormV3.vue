<script setup lang="tsx">
import { API } from './DynTableType';
import type { DynFormV3Props } from './DynFormType';
import { useLayout, useRequest } from '@/hooks';
import { tableBff } from '@/bff/table';
import { diffObj, diffObjDep, goback } from '@/utils';
import { t } from '@/modules/i18n';
import MarControlledEditTable from '../common/MarControlledEditTable.vue';
import MarControlledForm from '../common/MarControlledForm.vue';
import { CompareEnum, LinkEnum } from '@/constants';
import { router } from '@/router';
import { usePerm } from '@/hooks/usePerm'
import { MarFormProps } from '../common/MarFormType';
import type { Component } from 'vue';

/**
 * 第三版的 表单组件
 * 组件会在四个状态之间切换：查看、编辑、复制、新增
 * 查看: 所有区域只读，同时复制按钮置灰
 * 编辑: 所有区域可编辑，同时不展示复制按钮
 * 复制: 所有区域可编辑，但是此时需要将来源的 id 项删除
 * 新增: 所有区域可编辑，同时不展示复制按钮
 * 
 * 编辑状态只可由查看状态进入
 * 复制状态只可由编辑状态进入
 * 
 * 进入这个组件时，初试状态只有两个：查看、新增
 */
const props = defineProps<DynFormV3Props & {
  // 额外的校验步骤（发生在主表和子表的校验之前)
  onValidate?: (form: any) => Promise<boolean>
  beforeMount?: () => void
  mounted?: () => void
  activated?: () => void
  deactivated?: () => void
  beforeSubmit?: (form: any) => Promise<any>
  overrideFormOptions?: Record<string, () => Promise<Record<string, any>>>
  overrideSubFormOptions?: Record<string, () => Promise<Record<string, any>>>
  renderForm?: (form: any, onUpdateValue: (prop: string, value: any) => void) => Record<string, Component>
}>()

type State = 'check' | 'edit' | 'copy' | 'new'

interface Meta {
  dynCode: string
  subDynCode?: string
  momKey?: string
  subMomKey?: string
  title: string
  state: State
  api?: API
  actionHideList?: string[]
  menuId: string
}

const form = reactive<any>({
  sonDetail: []
})
let rawForm = {}
defineExpose({
  form
})

const route = useRoute()
const { id } = route.query
const meta = route.meta as unknown as Meta
const state = ref<State>(meta.state)

// 主表监听
const mainFormHideList = ref<string[]>([])
let oldFormValue = {} // 创建一个变量保存旧值

watch(form, (newVal) => {
  if (props.watchMainForm) {
    const diff = diffObj(newVal, oldFormValue)
    props.watchMainForm(form, diff)
    // 更新旧值
    oldFormValue = JSON.parse(JSON.stringify(newVal))
  }
  if (props.hideMainForm) {
    mainFormHideList.value = props.hideMainForm(form)
  }
}, {
  deep: true,
})

const dynCode = meta.dynCode as string
const { formConfigs, child } = await tableBff(dynCode, meta.menuId)
const hasChild = !!child

// const overrideFormOptions = props.overrideFormOptions
// const overrideSubFormOptions = props.overrideSubFormOptions
let overrideFormOptions = {}
let overrideSubFormOptions = {}
for (const key in props.overrideFormOptions) {
  overrideFormOptions[key] = await props.overrideFormOptions[key]()
}
for (const key in props.overrideSubFormOptions) {
  overrideSubFormOptions[key] = await props.overrideSubFormOptions[key]()
}

function overrideOptionsHelper(formConfig: MarFormProps | undefined, options?: Record<string, any>) {
  if (!formConfig || !options) {
    return formConfig
  }
  Object.keys(options).forEach(key => {
    if (!formConfig.formSchema.find(item => item.prop === key)) {
      return
    }
    formConfig.formSchema = formConfig.formSchema.map(item => {
      if (item.prop === key) {
        item.options = options[key]
      }
      return item
    })
  })
  console.log('formConfig', formConfig)
  return formConfig
}
const formConfig = computed(() => {
  switch (state.value) {
    case 'check':
      return overrideOptionsHelper(formConfigs.find(item => item.key === 'edit')?.config, overrideFormOptions)
    case 'edit':
      return overrideOptionsHelper(formConfigs.find(item => item.key === 'edit')?.config, overrideFormOptions)
    case 'copy':
      return overrideOptionsHelper(formConfigs.find(item => item.key === 'edit')?.config, overrideFormOptions)
    case 'new':
      return overrideOptionsHelper(formConfigs.find(item => item.key === 'new')?.config, overrideFormOptions)
  }
})
// 初始化数据
// 如果是新增模式，需要初始化默认值
if (state.value === 'new') {
  const query = route.query
  const defaultFormValue = formConfig.value?.formSchema.reduce((acc, item) => {
    if (query[item.prop]) {
      acc[item.prop] = query[item.prop]
    }
    else if (item.default) {
      acc[item.prop] = !isNaN(Number(item.default)) ? Number(item.default) : item.default
    }
    else if (item.type === 'input') {
      acc[item.prop] = ''
    }
    else if (item.type === 'input-number') {
      acc[item.prop] = 0
    }
    else {
      acc[item.prop] = null
    }
    return acc
  }, {} as Record<string, any>)
  Object.assign(form, defaultFormValue)
}
// 如果是查看状态，需要初始化数据
if (state.value === 'check') {
  // 主表
  const { data: mainData } = await useRequest(meta.api?.info ? `${meta.api.info}/${id}` : `/virtual/${meta.dynCode}/info/${id}`)
  Object.assign(form, mainData.value)
  // 子表
  if (hasChild) {
    let args = {
      filterInfo: [{
        link: LinkEnum.AND,
        data: [{
          colkey: meta.subMomKey,
          op: CompareEnum.EQUAL,
          opval: router.currentRoute.value.query['id'],
          link: LinkEnum.AND,
        }]
      }],
      page: 1,
      limit: 1000,
      orders: [
        {
          column: 'createDate',
          asc: false
        }
      ],
    }
    const { data: childData } = await useRequest<{ list: any[] }>(meta.api?.subPage ? meta.api.subPage : `/virtual/${child.dynCode}/page`).post(args)
    form.sonDetail = []
    if (childData.value) {
      if (Array.isArray(childData.value.list) && childData.value.list.length > 0) {
        form.sonDetail.push(...childData.value.list)
      }
    }
  }
  rawForm = JSON.parse(JSON.stringify(form))
}

const { mainContentHeight } = useLayout();
const cardContentHeight = computed(() => {
  return mainContentHeight.value - 32 - 66 - 28;
});

const computedApi = computed(() => {
  if (state.value === 'edit') {
    return meta.api?.update || formConfig.value?.api
  }
  return meta.api?.save || formConfig.value?.api
})
const mainFormRef = ref<any>(null)
const childFormRef = ref<any>(null)
async function handleSubmit() {
  const cpForm = JSON.parse(JSON.stringify(form))
  if (!computedApi.value) {
    return
  }
  if (props.onValidate) {
    const valid = await props.onValidate(cpForm)
    if (!valid) {
      window.$message.error(t('common.validateFailed'))
      return
    }
  }
  const mainFormValid = await mainFormRef.value?.validate()
  const childFormValid = child ? await childFormRef.value?.validate() : true
  if (!mainFormValid || !childFormValid) {
    window.$message.error(t('common.validateFailed'))
    return
  }
  const formData = props.beforeSubmit ? await props.beforeSubmit(cpForm) : cpForm
  const diffData = state.value === 'edit'
    ? diffObjDep(formData, rawForm)
    : diffObjDep(formData, {})
  const { error } = await useRequest(computedApi.value).post(diffData)
  if (!error.value) {
    window.$message.success(t('common.operateSuccess'))
    goback(true)
  }
}

const childFormConfig = computed(() => overrideOptionsHelper(child?.formConfigs.find(item => item.key === (state.value === 'new' ? 'new' : 'edit'))?.config, overrideSubFormOptions))

function handleCancel() {
  goback(true)
}

function handleToggle() {
  state.value = 'edit'
}
function handleCopy() {
  // 删除主表的id
  if ('id' in form) {
    delete form.id
  }
  // 遍历子表，删除id
  if (hasChild) {
    form.sonDetail = form.sonDetail.map(item => {
      const { id, ...rest } = item
      return rest
    })
  }
  state.value = 'copy'
}

const { hasPerm } = usePerm()
const submitVisible = computed(() => {
  if (state.value === 'check') {
    return false
  }
  switch (state.value) {
    case 'edit':
      return props.permission?.edit ? hasPerm(props.permission?.edit) : true
    case 'new':
    case 'copy':
      return props.permission?.save ? hasPerm(props.permission?.save) : true
  }
})
const toggleEditVisible = computed(() => {
  if (state.value !== 'check') {
    return false
  }
  return props.permission?.edit ? hasPerm(props.permission?.edit) : true
})

onBeforeMount(async () => {
  await props.beforeMount?.()
})
onMounted(async () => {
  await props.mounted?.()
})
onActivated(async () => {
  await props.activated?.()
})
onDeactivated(async () => {
  await props.deactivated?.()
})
</script>

<template>
  <NCard>
    <main :style="{ height: `${cardContentHeight}px` }" class="overflow-y-auto no-scrollbar relative">
      <section class="flex justify-between pb-4 sticky top-0 left-0 right-0 z-10">
        <div class="flex gap-2">
          <NButton v-if="submitVisible" type="primary" @click="handleSubmit">
            <template #icon>
              <div class="i-icon-park-outline-save"></div>
            </template>
            {{ $t('common.submit') }}
          </NButton>
          <NButton v-if="toggleEditVisible" type="primary" @click="handleToggle"
            :disabled="meta.actionHideList?.includes('edit')">
            <template #icon>
              <div class="i-icon-park-outline-edit"></div>
            </template>
            {{ $t('common.edit') }}
          </NButton>
          <NButton @click="handleCancel">
            <template #icon>
              <div class="i-icon-park-outline-close"></div>
            </template>
            {{ $t('common.cancel') }}
          </NButton>
          <slot name="actions" />
        </div>
        <NButton @click="handleCopy" type="info" v-if="state === 'edit' && !meta.actionHideList?.includes('save')">
          <template #icon>
            <div class="i-icon-park-outline-copy"></div>
          </template>
          {{ t('common.copy') }}
        </NButton>
      </section>

      <slot name="free-zone" />

      <div class="space-y-4">
        <NCard class="dark:bg-transparent bg-#fafafa">
          <MarControlledForm ref="mainFormRef" v-if="formConfig" v-bind="formConfig" v-model:model-value="form"
            :hide-list="mainFormHideList" :readonly="state === 'check'" :render-form="renderForm" />
          <div v-else class="error-tip">
            {{ $t('tip.fetchConfigError') }}
          </div>
        </NCard>

        <NCard class="dark:bg-transparent bg-#fafafa" v-if="childFormConfig">
          <MarControlledEditTable :readonly="state === 'check'" ref="childFormRef" :row-actions="props.rowActions" v-bind="childFormConfig"
            v-model:model-value="form.sonDetail">
            <template #sub-actions>
              <slot name="sub-actions" />
            </template>
          </MarControlledEditTable>
        </NCard>

        <slot name="ext-form" />
      </div>
    </main>
  </NCard>
</template>

<style scoped>
/* 隐藏滚动条但保留滚动功能 */
.no-scrollbar {
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
</style>