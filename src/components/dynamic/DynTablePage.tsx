import type { Component } from 'vue'
import 'reflect-metadata'
import { h, ref, defineComponent } from 'vue'
import DynTableV3 from './DynTableV3.vue'
import type { Core, App, Router, Request, Components, Dict, I18n, Message, Dialog, Utils } from '@/core'
import type { IController } from '@/decorator/controller'
import type { QueryForm, UseTableReturn } from '@/hooks'
import { BatchOperate, ActionBar } from './DynTableType'
import { SummaryRowData } from 'naive-ui/es/data-table/src/interface'
import { Columns } from '@/utils'

type TableInstance = UseTableReturn

class DynTablePage implements IController {
  app: App;
  router: Router;
  request: Request;
  components: Components;
  dict: Dict;
  i18n: I18n;
  message: Message;
  dialog: Dialog;
  utils: Utils;
  
  // 实际会渲染的 Vue 组件
  public view: Component
  
  // 修改ref类型定义，更准确地匹配Vue组件实例
  private tableRef = ref<any>(null)
  
  // 获取选中行数据的方法
  public getChecked() {
    console.log('tableRef.value:', this.tableRef.value)
    console.log('getExpose:', this.tableRef.value?.getExpose?.())
    return this.tableRef.value?.getExpose?.()?.checked || []
  }
  public refreshData() {
    return this.tableRef.value?.getExpose?.()?.refresh?.()
  }

  /**
   * 自定义渲染区域
   * 留空，交给子组件去实现
   */
  render() {
  }

  private apiOptions: URL | undefined = Reflect.getMetadata('Reflect.OverrideAPI', this.constructor);
  private subTableOptions: URL | undefined = Reflect.getMetadata('Reflect.OverrideSubTableAPI', this.constructor);
  private overridesColumnMap: Record<string, string> | undefined = Reflect.getMetadata('Reflect.OverrideColumn', this.constructor);
  private overridesSubTableColumnMap: Record<string, string> | undefined = Reflect.getMetadata('Reflect.OverrideSubTableColumn', this.constructor);
  private hideButtons: string[] | undefined = Reflect.getMetadata('Reflect.HideButton', this.constructor);
  private permission: string[] | undefined = Reflect.getMetadata('Reflect.Permission', this.constructor);
  private hideSubTableButtons: string[] | undefined = Reflect.getMetadata('Reflect.HideSubTableButton', this.constructor);
  private defaultSort: { columnKey: string, order: 'ascend' | 'descend' } | undefined = Reflect.getMetadata('Reflect.DefaultSort', this.constructor);
  private formPath: string | undefined = Reflect.getMetadata('Reflect.BindForm', this.constructor);
  private hideRowOperate: boolean | undefined = Reflect.getMetadata('Reflect.HideRowOperate', this.constructor);
  private getQueryFromUrl: boolean | undefined = Reflect.getMetadata('Reflect.QueryController', this.constructor);
  private overrideQueryMap: Record<string, any> | undefined = Reflect.getMetadata('Reflect.OverrideQuery', this.constructor);
  private defaultSubTableSort: { columnKey: string, order: 'ascend' | 'descend' } | undefined = Reflect.getMetadata('Reflect.DefaultSubTableSort', this.constructor);
  private hideFirstColumnHref: boolean | undefined = Reflect.getMetadata('Reflect.HideFirstColumnHref', this.constructor);

  appendRowButton(row: any): any[] {
    const actions = this._appendRowButton ? this._appendRowButton(row) : []
    return actions.map(action => ({
      ...action,
      click: action.click.bind(this)
    }))
  }
  appendToolbarButton(row: any): any[] {
    const actions = this._appendToolbarButton ? this._appendToolbarButton(row) : []
    return actions.map(action => ({
      ...action,
      onClick: action.onClick.bind(this)
    }))
  }
  appendBatchOperate(row: any): any[] {
    const actions = this._appendBatchOperate ? this._appendBatchOperate(row) : []
    return actions.map(action => ({
      ...action
    }))
  }

  _appendRowButton(row: any): any[] {
    return []
  }
  _appendToolbarButton(instance: TableInstance): ActionBar {
    return []
  }
  _appendBatchOperate(instance: TableInstance): BatchOperate {
    return []
  }
  _appendQuery(): Columns[] {
    return []
  }

  _onCheckedChange(val: any[]) {
  }

  _createSummary(pageData: any[]): SummaryRowData | SummaryRowData[] {
    return []
  }

  _createBadges(): any | void | Component {
  }

  _rowProps(rowData: any, rowIndex : number): Record<string, any> {
    return {}
  }

  onBeforeMount() {
  }
  onMounted() {
  }
  onActivated() {
  }
  onDeactivated() {
  }
  onBeforeQuery(params: QueryForm): QueryForm {
    return params
  }
  onAfterQuery(dataList: any): void | any | Promise<any | void> {
  }

  constructor(core: Core) {
    this.app = core.app
    this.router = core.router
    this.request = core.request
    this.components = core.components
    this.dict = core.dict
    this.i18n = core.i18n
    this.message = core.message
    this.dialog = core.dialog
    this.utils = core.utils

    // 处理列渲染器
    const columnsRender: Record<string, any> = {}
    if (this.overridesColumnMap) {
      Object.keys(this.overridesColumnMap).forEach(key => {
        const methodName = this.overridesColumnMap?.[key]
        if (methodName && typeof this[methodName as keyof this] === 'function') {
          columnsRender[key] = this[methodName as keyof this]
        }
      })
    }

    // 处理子表列渲染器
    const subColumnsRender: Record<string, any> = {}
    if (this.overridesSubTableColumnMap) {
      Object.keys(this.overridesSubTableColumnMap).forEach(key => {
        const methodName = this.overridesSubTableColumnMap?.[key]
        if (methodName && typeof this[methodName as keyof this] === 'function') {
          subColumnsRender[key] = this[methodName as keyof this]
        }
      })
    }

    const extQuery = this._appendQuery()

    console.log(this.hideSubTableButtons, 'hideSubTableButtons')
    this.view = defineComponent({
      setup: () => {
        return () => h(DynTableV3, {
          ref: this.tableRef,
          formPath: this.formPath,
          api: {
            page: this.apiOptions?.page,
            importTable: this.apiOptions?.importTable,
            exportTable: this.apiOptions?.exportTable,
            importTemplate: this.apiOptions?.importTemplate,
            delete: this.apiOptions?.delete,
            save: this.apiOptions?.save,
            info: this.apiOptions?.info,
            update: this.apiOptions?.update,
            subPage: this.subTableOptions?.page,
            subDelete: this.subTableOptions?.delete,
            subSave: this.subTableOptions?.save,
            subUpdate: this.subTableOptions?.update,
            subInfo: this.subTableOptions?.info,
          },
          columnsRender,
          subColumnsRender,
          otherOptions: {
            buttonHideList: this.hideButtons,
            subTableButtonHideList: this.hideSubTableButtons,
            defaultSort: this.defaultSort,
            beforeRequest: this.onBeforeQuery.bind(this),
            afterRequest: this.onAfterQuery.bind(this),
          },
          rowOperate: this.appendRowButton.bind(this),
          actionBar: this.appendToolbarButton.bind(this),
          batchOperate: this.appendBatchOperate.bind(this),
          hide: {
            rowOperate: this.hideRowOperate
          },
          onCheckedChange: this._onCheckedChange.bind(this),
          getQueryFromUrl: this.getQueryFromUrl,
          beforeMount: this.onBeforeMount.bind(this),
          mounted: this.onMounted.bind(this),
          activated: this.onActivated.bind(this),
          deactivated: this.onDeactivated.bind(this),
          createSummary: this._createSummary.bind(this),
          rowProps: this._rowProps.bind(this),
          extQuery: extQuery,
          overrideQueryMap: this.overrideQueryMap,
          defaultSubTableSort: this.defaultSubTableSort,
          hideFirstColumnHref: this.hideFirstColumnHref,
        }, {
          'free-zone': () => this.render(),
          badges: () => this._createBadges()
        })
      }
    })
  }
}

interface URL {
  page: string
  importTable: string
  exportTable: string
  importTemplate: string
  delete: string
  save: string
  info: string
  update: string
}

export default DynTablePage

export type {
  TableInstance
}