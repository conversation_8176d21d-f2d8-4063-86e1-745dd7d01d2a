<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import SubCompDrawer from './SubCompDrawer.vue'
import { FormDialogV2 } from '@/components/index'
import { useTable } from '@/hooks/useTable'
import { getRefresh, RowActionType, RowAction, useDelDialog, usePerm } from '@/hooks'
import { t } from '@/modules/i18n'
import TablePageSettingDialog from '@/components/custom/Dialog/TablePageSettingDialog.vue'
import { bareTableBff } from '@/bff/bareTable'

export default defineComponent({
  name: 'DynTable',
  components: {
    MarWrapper: () => import('@/components/index').then(m => m.MarWrapper),
    NButton: () => import('naive-ui').then(m => m.NButton),
    FormDialogV2,
    SubCompDrawer,
    TablePageSettingDialog
  },
  props: {
    columnsRender: Object,
    subColumnsRender: Object,
    rowOperate: [Array, Function],
    actionBar: Function,
    batchOperate: Function,
    otherOptions: Object,
    hide: Object,
    rawMeta: Object,
    dynCode: String,
    appendRowButton: Function,
    onCheckedChange: Function,
    defaultQueryValue: Object,
    inner: String,
    checkable: String,
    api: Object,
    extQuery: Array,
    pageSizeOptions: Array,
    defaultPageSize: Number,
  },
  async setup(props) {
    // get route meta
    let dynCode = props.dynCode as string

    let bff
    bff = await bareTableBff(dynCode)
    let { columns, child } = bff
    if (props.columnsRender) {
      Object.keys(props.columnsRender).forEach(key => {
        for (let i = 0; i < columns.length; i++) {
          if (columns[i].key === key) {
            columns[i].render = props.columnsRender?.[key]
            break
          }
        }
      })
    }

    // 处理子表的列渲染配置
    if (child && props.subColumnsRender) {
      Object.keys(props.subColumnsRender).forEach(key => {
        for (let i = 0; i < child.columns.length; i++) {
          if (child.columns[i].key === key) {
            child.columns[i].render = props.subColumnsRender?.[key]
            break
          }
        }
      })
    }

    // 处理 defaultQueryValue
    if (props.defaultQueryValue) {
      Object.keys(props.defaultQueryValue).forEach(key => {
        for (let i = 0; i < columns.length; i++) {
          if (columns[i].key === key) {
            columns[i].defaultQueryValue = props.defaultQueryValue?.[key]
            break
          }
        }
      })
    }

    const rowOperate = (row: any) => {
      let operate: RowActionType = []
      if (props.rowOperate) {
        if (typeof props.rowOperate === 'function') {
          operate.push(...props.rowOperate(row))
        } else {
          operate.push(...(props.rowOperate as RowAction[]))
        }
      }

      return operate
    }
    function filterColumns(columns: any[]) {
      return columns.filter(item => !props.hide?.columns?.includes(item.key))
    }
    Object.assign(bff.url, props.api)
    // 使用计算后的 url
    const useTableReturn = useTable(bff.url, filterColumns(columns), {
      checkable: props.checkable === 'off' ? false : true,
      actionHideList: ["save", "deleteMul", "exportTable", "importTable", "edit", "delete", "config", "groupSearch"],
      rowActions: rowOperate,
      defaultSort: props.otherOptions?.defaultSort,
      immediate: props.otherOptions?.immediate,
      beforeRequest: props.otherOptions?.beforeRequest,
      hide: {
        rowAction: props.hide?.rowOperate
      },
      inner: props.inner === 'on' ? true : false,
      extQuery: props.extQuery as any,
      pageSizeOptions: props.pageSizeOptions as any,
      defaultPageSize: props.defaultPageSize,
    })
    const { state, on, refresh, checked, queryWrapper } = useTableReturn
    watch(checked, (val) => {
      props.onCheckedChange?.(val)
    })
    const actionBar = props.actionBar?.(useTableReturn)

    return {
      getExpose: () => ({
        state,
        refresh,
        on,
        checked,
        queryWrapper,
      }),
      state,
      actionBar,
      dynCode,
      bff,
      refresh,
      t,
    }
  },
})
</script>

<template>
  <MarWrapper v-model="state">

    <template #toolbarExt>
      <template v-for="item in actionBar" :key="item.label">
        <NButton size="small" tertiary :type="item.type" strong @click="item.onClick" :class="item.classStr + ' relative'">
          <template #icon v-if="item.icon">
            <div :class="item.icon" />
          </template>
          {{ item.label }}
          <div v-if="item.countSource" class="absolute top-0 left-0 transform -translate-x-1/2 -translate-y-1/2 bg-blue-500 text-white rounded-full px-1 py-0.5 text-10px z-10">
            {{ item.countSource.value.length }}
          </div>
        </NButton>
      </template>
    </template>

  </MarWrapper>
</template>
