<script setup lang="tsx">
import { NButton } from 'naive-ui';
import { tableBff } from '@/bff/table';
import { MarEditTable, MarForm } from '@/components';
import { goback } from '@/utils';
import { useFormLifecycle } from './hooks/useFormLifecycle';
import { t } from '@/modules/i18n';
import { API } from './DynTableType';
import { useRequest } from '@/hooks';
import type { Component, Ref } from 'vue';

const props = defineProps<{
  watchForm?: (
    changedProps: Record<string, any>,
    form: Ref<Record<string, any>>,
    initializing: boolean
  ) => void
  hideForm?: (form: Record<string, any>) => string[]
  beforeSubmit?: (form: Record<string, any>) =>
    | Promise<Array<any>>
    | Array<any>
  breakLine?: string[]
  renderForm?: (formValue: any, onUpdateValue: (prop: string, value: any) => void) => Record<string, Component>
  multiLine?: boolean
}>()

interface Meta {
  dynCode: string
  subDynCode?: string
  momKey?: string
  subMomKey?: string
  title: string
  state: string
  api?: API
  menuId: string
}

const route = useRoute()
const meta = route.meta as unknown as Meta
const dynCode = meta.dynCode as string

defineExpose({
  getMainFormData: () => mainFormData.value,
  setMainFormData: (data: Record<string, any>) => {
    mainFormData.value = data
  },
  getTableData: () => tableData.value,
  setTableData: (data: any) => {
    tableData.value = data
  },
  appendTableData: (data: any) => {
    if (Array.isArray(data))
      data.forEach(item => tableData.value.push(item))
    else
      tableData.value.push(data)
  },
  getCurrentEditRow: () => currentEditRow.value,
  setCurrentEditRow: (row: Record<string, any>) => {
    tableData.value[Number(currentEditRow.value)] = row
  },
  getCheckedRowKeys: () => checkedRowKeys.value,
  setCheckedRowKeys: (keys: string[]) => {
    checkedRowKeys.value = keys
  },
})
const { formConfigs, child } = await tableBff(dynCode, meta.menuId)
const mainFormRef = ref<InstanceType<typeof MarForm> | null>(null)
const { state, infoParam, mainFormData } = useFormLifecycle(meta)
async function handleSubmit() {
  const data = await mainFormRef.value?.submit()
  if (data && state.value === 'new') {
    mainFormData.value = data
    infoParam.value = data.id
    await nextTick()
    state.value = 'edit'
  }
}
function resetState() {
  state.value = 'new'
  mainFormRef.value?.reset()
  tableData.value.length = 0
}
async function handleCancel() {
  resetState()
  await goback(true)
}

const tableData = ref<any[]>([])
const mainFormConfig = computed(() => formConfigs.find(item => item.key === state.value)?.config)
const computedMainApi = computed(() => {
  if (state.value === 'edit') {
    return meta.api?.update || mainFormConfig.value?.api
  }
  return meta.api?.save || mainFormConfig.value?.api
})

const childFormConfig = computed(() => child?.formConfigs.find(item => item.key === state.value)?.config)
const childNewValueTemplate = computed(() => {
  return childFormConfig.value?.formSchema.reduce((acc, item) => {
    if (item.default) {
      acc[item.prop] = !isNaN(Number(item.default)) ? Number(item.default) : item.default
    }
    else if (item.type === 'input-number') {
      acc[item.prop] = 0
    }
    else if (item.type === 'date') {
      // get yyyy-MM-dd
      acc[item.prop] = new Date().toISOString().split('T')[0]
    }
    else if (item.type === 'date-time') {
      // get yyyy-MM-dd HH:mm:ss
      acc[item.prop] = new Date().toISOString().split('T')[0] + ' ' + new Date().toISOString().split('T')[1].split('.')[0]
    }
    else {
      acc[item.prop] = ''
    }
    return acc
  }, {} as Record<string, any>)
})

const currentEditRow = ref<string | null>(null)
const checkedRowKeys = ref<string[]>([])
function handleNewRow() {
  if (currentEditRow.value) {
    return window.$message.error(t('tip.saveCurrentRow'))
  }
  tableData.value.push({ ...childNewValueTemplate.value })
  currentEditRow.value = String(tableData.value.length - 1)
}
function handleNewMulRow() {
  if (currentEditRow.value) {
    return window.$message.error(t('tip.saveCurrentRow'))
  }
  tableData.value.push({ ...childNewValueTemplate.value })
}
const childFormApi = {
  save: meta.api?.subSave || child?.formConfigs.find(item => item.key === 'new')?.config.api,
  update: meta.api?.subUpdate || child?.formConfigs.find(item => item.key === 'edit')?.config.api,
  delete: meta.api?.subDelete || `/virtual/${child?.dynCode}/deleteMul`,
}
async function handleDelMul() {
  if (currentEditRow.value) {
    return window.$message.error(t('tip.saveCurrentRow'))
  }
  const { error } = await useRequest(childFormApi.delete).post(JSON.stringify(checkedRowKeys.value))
  if (!error.value) {
    tableData.value = tableData.value.filter(item => !checkedRowKeys.value.includes(item.id))
    window.$message.success(t('common.operationSuccess'))
    checkedRowKeys.value = []
  }
}

function beforeSubmit(form: any) {
  // 检查必要的关联字段配置
  if (!child?.subMomKey) {
    throw new Error(t('tip.subMomKeyRequired'))
  }

  // 检查主表关联字段值是否存在
  if (!mainFormData.value[child?.momKey]) {
    throw new Error(t('tip.submitMainFormFirst'))
  }

  // 构建新的表单数据,添加关联字段
  const newForm = {
    ...form,
    [child.subMomKey]: mainFormData.value[child.momKey]
  }

  return newForm
}

const subFormRef = ref<any>(null)

async function handleSaveAll() {
  // 检查必要的关联字段配置
  if (!child?.subMomKey || !child?.momKey) {
    return window.$message.error(t('tip.subMomKeyRequired'))
  }

  // 检查主表关联字段值是否存在
  if (!mainFormData.value || !mainFormData.value[child.momKey]) {
    return window.$message.error(t('tip.submitMainFormFirst'))
  }

  // 获取表格组件实例并调用submit方法
  const formData = await subFormRef.value?.submit()
  if (!formData) {
    return window.$message.error(t('common.validateError'))
  }

  // 批量保存所有行数据
  try {
    // 不再区分新增和更新，将所有数据一次性提交
    if (!childFormApi.save) {
      return window.$message.error(t('error.saveUrlNotConfigured'))
    }

    const { error } = await useRequest(childFormApi.save).post({
      ...mainFormData.value,
      sonDetailList: formData
    })
    if (error.value) {
      return window.$message.error(error.value.message || t('common.saveError'))
    }

    window.$message.success(t('common.operationSuccess'))
  } catch (e) {
    if (e instanceof Error) {
      window.$message.error(e.message)
    }
  }
}
</script>

<template>
  <main class="nest-form-container">
    <NButton @click="handleCancel" strong secondary class="back-button">
      <template #icon>
        <div class="i-icon-park-outline-arrow-left h-1.2rem w-1.2rem" />
      </template>
      {{ $t('common.exit') }}
    </NButton>

    <NCard class="form-card">
      <template #header>
        <div class="flex p-1 px-3 flex-col justify-center">
          <h2 class="text-1.2rem font-450 mr-3 opacity-80 mb-2">{{ $t('components.dynNestForm.title') }}</h2>
          <div class="flex items-center">
            <NButton @click="handleSubmit" size="small" strong secondary type="primary" class="mr-1">
              <span>{{ $t('common.submit') }}</span>
            </NButton>
            <slot name="main-actions" />
          </div>
        </div>
      </template>
      <MarForm ref="mainFormRef" v-if="mainFormConfig" v-bind="mainFormConfig" :api="computedMainApi || ''" :col="4"
        actions="none" class="main-form" :watch-form="props.watchForm" :hide-form="props.hideForm"
        :before-submit="props.beforeSubmit" :break-line="props.breakLine" :render-form="props.renderForm" />
      <div v-else class="error-tip">
        {{ $t('tip.fetchConfigError') }}
      </div>
    </NCard>

    <slot name="free-zone" />

    <NCard class="form-card">
      <template #header>
        <div class="flex p-1 px-3 flex-col justify-center">
          <h2 class="text-1.2rem font-450 mr-3 opacity-80 mb-2">{{ $t('components.dynNestForm.childTitle') }}</h2>
          <div class="flex items-center">
            <NButton @click="handleSaveAll" v-if="multiLine" size="small" strong secondary type="error">
              {{ $t('common.submit') }}
            </NButton>
            <NButton @click="handleNewRow" v-if="!multiLine" size="small" strong secondary type="success"
              class="new-Row-btn">
              {{ $t('components.dynNestForm.newRow') }}
            </NButton>
            <NButton @click="handleNewMulRow" v-if="multiLine" size="small" strong secondary type="success"
              class="new-Row-btn">
              {{ $t('components.dynNestForm.newRow') }}
            </NButton>
            <NButton @click="handleDelMul" v-if="!multiLine" size="small" strong secondary type="warning"
              class="new-Row-btn mr-2">
              {{ $t('common.delete') }}
            </NButton>
            <slot name="sub-actions" />
          </div>
        </div>
      </template>
      <MarEditTable ref="subFormRef" v-if="childFormConfig && !props.multiLine" v-bind="childFormConfig"
        v-model="tableData" class="child-table" :api="childFormApi" v-model:current-edit-row="currentEditRow"
        v-model:checked-row-keys="checkedRowKeys" :before-submit="beforeSubmit" />
      <MarEditMultiLineTable ref="subFormRef" v-if="childFormConfig && props.multiLine" v-bind="childFormConfig"
        v-model="tableData" class="child-table" :api="childFormApi" v-model:current-edit-row="currentEditRow"
        v-model:checked-row-keys="checkedRowKeys" :before-submit="beforeSubmit" />
    </NCard>
  </main>
</template>

<style scoped>
.nest-form-container {
  @apply min-h-60vh bg-white dark:bg-[#18181C] rounded-sm overflow-hidden p-6;
}

.back-button {
  @apply mb-4 transition-all duration-300 hover:translate-x-1;
}

.form-card {
  @apply mb-6 transition-all duration-300 hover:shadow-md;
}

.form-card:deep(.n-card-header) {
  @apply p-0 mb-3;
}

.form-card:deep(.n-card) {
  @apply border-none;
}

.main-form {
  @apply p-4;
}

.error-tip {
  @apply text-red-500 text-center p-4;
}

.new-Row-btn {
  @apply ml-2 transition-colors duration-300;
}

.child-table {
  @apply mt-4;
}

/* 深色模式适配 */
:deep(.dark) {
  .form-card {
    @apply bg-[#1a1a1a] border-[#333];
  }
}
</style>
