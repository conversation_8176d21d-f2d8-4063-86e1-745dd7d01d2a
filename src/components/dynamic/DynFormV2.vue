<script setup lang="tsx">
import { API } from './DynTableType';
import type { DynFormV2Props } from './DynFormType';
import { setRefresh, useLayout, useRequest } from '@/hooks';
import { tableBff } from '@/bff/table';
import { diffObj, diffObjDep, goback, goto } from '@/utils';
import { t } from '@/modules/i18n';
import MarControlledEditTable from '../common/MarControlledEditTable.vue';
import MarControlledForm from '../common/MarControlledForm.vue';
import { CompareEnum, LinkEnum } from '@/constants';
import { router } from '@/router';
import DynCopy from './DynCopy.vue';
import { useSlots } from 'vue';

const props = defineProps<DynFormV2Props>()

interface Meta {
  dynCode: string
  subDynCode?: string
  momKey?: string
  subMomKey?: string
  title: string
  state: string
  api?: API
  actionHideList?: string[]
  menuId: string
}

const form = reactive<any>({
  sonDetail: []
})
let rawForm = {}
defineExpose({
  form
})

const route = useRoute()
const { id } = route.query
const meta = route.meta as unknown as Meta
const isEdit = computed(() => meta.state === 'edit')
const state = ref<'readonly' | 'edit'>(isEdit.value ? 'readonly' : 'edit')

// 主表监听
const mainFormHideList = ref<string[]>([])
let oldFormValue = {} // 创建一个变量保存旧值

watch(form, (newVal) => {
  if (props.watchMainForm) {
    const diff = diffObj(newVal, oldFormValue)
    props.watchMainForm(form, diff)
    // 更新旧值
    oldFormValue = JSON.parse(JSON.stringify(newVal))
  }
  if (props.hideMainForm) {
    mainFormHideList.value = props.hideMainForm(form)
  }
}, {
  deep: true,
})

const dynCode = meta.dynCode as string
const { url, columns, formConfigs, child } = await tableBff(dynCode, meta.menuId)
const hasChild = !!child

const formConfig = computed(() => formConfigs.find(item => item.key === (isEdit.value ? 'edit' : 'new'))?.config)
// 初始化数据
// 如果是新增模式，需要初始化默认值
if (!isEdit.value) {
  const defaultFormValue = formConfig.value?.formSchema.reduce((acc, item) => {
    if (item.default) {
      acc[item.prop] = !isNaN(Number(item.default)) ? Number(item.default) : item.default
    }
    else if (item.type === 'input') {
      acc[item.prop] = ''
    }
    else if (item.type === 'input-number') {
      acc[item.prop] = 0
    }
    else {
      acc[item.prop] = null
    }
    return acc
  }, {} as Record<string, any>)
  Object.assign(form, defaultFormValue)
}
// 如果是编辑模式，需要初始化数据
if (isEdit.value) {
  // 主表
  const { data: mainData } = await useRequest(meta.api?.info ? `${meta.api.info}/${id}` : `/virtual/${meta.dynCode}/info/${id}`)
  Object.assign(form, mainData.value)
  // 子表
  if (hasChild) {
    let args = {
      filterInfo: [{
        link: LinkEnum.AND,
        data: [{
          colkey: meta.subMomKey,
          op: CompareEnum.EQUAL,
          opval: router.currentRoute.value.query['id'],
          link: LinkEnum.AND,
        }]
      }],
      page: 1,
      limit: 1000,
      orders: [
        {
          column: 'createDate',
          asc: false
        }
      ],
    }
    const { data: childData } = await useRequest<{ list: any[] }>(meta.api?.subPage ? meta.api.subPage : `/virtual/${child.dynCode}/page`).post(args)
    form.sonDetail = []
    if (childData.value) {
      if (Array.isArray(childData.value.list) && childData.value.list.length > 0) {
        form.sonDetail.push(...childData.value.list)
      }
    }
  }
  rawForm = JSON.parse(JSON.stringify(form))
}

const { mainContentHeight } = useLayout();
const cardContentHeight = computed(() => {
  return mainContentHeight.value - 32 - 66 - 28;
});

const computedApi = computed(() => {
  if (isEdit.value) {
    return meta.api?.update || formConfig.value?.api
  }
  return meta.api?.save || formConfig.value?.api
})
const mainFormRef = ref<any>(null)
const childFormRef = ref<any>(null)
async function handleSubmit() {
  const cpForm = JSON.parse(JSON.stringify(form))
  if (!computedApi.value) {
    return
  }
  const mainFormValid = await mainFormRef.value?.validate()
  const childFormValid = child ? await childFormRef.value?.validate() : true
  if (!mainFormValid || !childFormValid) {
    window.$message.error(t('common.validateFailed'))
    return
  }
  const formData = props.beforeSubmit ? await props.beforeSubmit(cpForm) : cpForm
  console.log('formData', formData)
  console.log('rawForm', rawForm)
  const diffData = isEdit.value
    ? diffObjDep(formData, rawForm)
    : diffObjDep(formData, {})
  console.log('diffData', diffData)
  const { error } = await useRequest(computedApi.value).post(diffData)
  if (!error.value) {
    window.$message.success(t('common.operateSuccess'))
    goback(true)
  }
}

const childFormConfig = computed(() => child?.formConfigs.find(item => item.key === (isEdit.value ? 'edit' : 'new'))?.config)

function handleCancel() {
  goback(true)
}

function handleToggle() {
  state.value = 'edit'
}
const slots = useSlots()
function handleCopy() {
  const currentTitle = meta.title
  const tabTitle = currentTitle.split('-')[0]
  goto(`/dynamic/${meta.dynCode}/copy`, {
    component: defineComponent(() => {
      return () => {
        return h(DynCopy, {
          props: props,
        }, slots)
      }
    }),
    meta: {
      ...meta,
      state: 'edit',
      title: `${tabTitle}-${t('common.copy')}`
    },
    query: {
      source: id
    },
    disableFrom: true
  })
}
</script>

<template>
  <NCard>
    <main :style="{ height: `${cardContentHeight}px` }" class="overflow-y-auto no-scrollbar relative">
      <section class="flex justify-between pb-4 sticky top-0 left-0 right-0 z-10">
        <div class="flex gap-2">
          <NButton v-if="state === 'edit'" type="primary" @click="handleSubmit">
            <template #icon>
              <div class="i-icon-park-outline-save"></div>
            </template>
            {{ $t('common.submit') }}
          </NButton>
          <NButton v-if="state === 'readonly'" type="primary" @click="handleToggle"
            :disabled="meta.actionHideList?.includes('edit')">
            <template #icon>
              <div class="i-icon-park-outline-edit"></div>
            </template>
            {{ $t('common.edit') }}
          </NButton>
          <NButton @click="handleCancel">
            <template #icon>
              <div class="i-icon-park-outline-close"></div>
            </template>
            {{ $t('common.cancel') }}
          </NButton>
          <slot name="actions" />
        </div>
        <NButton @click="handleCopy" type="info" v-if="isEdit && !meta.actionHideList?.includes('save')" :disabled="state === 'readonly'">
          <template #icon>
            <div class="i-icon-park-outline-copy"></div>
          </template>
          {{ t('common.copy') }}
        </NButton>
      </section>

      <slot name="free-zone" />

      <div class="space-y-4">
        <NCard class="dark:bg-transparent bg-#fafafa">
          <MarControlledForm ref="mainFormRef" v-if="formConfig" v-bind="formConfig" v-model:model-value="form"
            :hide-list="mainFormHideList" :readonly="state === 'readonly'" />
          <div v-else class="error-tip">
            {{ $t('tip.fetchConfigError') }}
          </div>
        </NCard>

        <NCard class="dark:bg-transparent bg-#fafafa" v-if="childFormConfig">
          <MarControlledEditTable :readonly="state === 'readonly'" ref="childFormRef" :row-actions="props.rowActions" v-bind="childFormConfig"
            v-model:model-value="form.sonDetail">
            <template #sub-actions>
              <div class="relative">
                <slot name="sub-actions" />
                <div v-if="state === 'readonly'" class="absolute inset-0 bg-white opacity-50 z-10 cursor-not-allowed"></div>
              </div>
            </template>
          </MarControlledEditTable>
        </NCard>
      </div>
    </main>
  </NCard>
</template>

<style scoped>
/* 隐藏滚动条但保留滚动功能 */
.no-scrollbar {
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
</style>