<script setup lang="ts">
import { NDataTable, NInput, NInputNumber, NSelect, NSwitch, NButton, NForm, NFormItem, NRadioGroup, NDatePicker, NPopover } from 'naive-ui'
import type { RowData, TableColumn } from 'naive-ui/es/data-table/src/interface'
import type { MarFormProps } from './MarFormType'
import type { FormInst, FormItemRule } from 'naive-ui'
import { t } from '@/modules/i18n';
import { useRequest } from '@/hooks';

const props = defineProps<Omit<MarFormProps, 'api'> & {
  api: {
    save?: string
    update?: string
    delete?: string
  }
  beforeSubmit?: (form: any) => any
}>()

const modelValue = defineModel<RowData[]>({
  required: true,
})
const currentEditRow = defineModel<string | null>('currentEditRow', {
  required: true,
})
const checkedRowKeys = defineModel<string[]>('checkedRowKeys', {
  required: true,
})
const filter = ref('')

// 为每一行创建一个 form 实例
const formRef = ref<FormInst | null>(null)

// 生成校验规则
function generateRules(item: MarFormProps['formSchema'][0]): FormItemRule[] {
  if (!item.rules) return []
  const validations: FormItemRule[] = []

  // 获取触发器和类型
  function getTriggerAndType(type: string, item: MarFormProps['formSchema'][0]): { trigger: string[], type?: 'number' | 'array' } {
    if (type === 'input-number') {
      return { trigger: ['blur', 'input'], type: 'number' }
    }
    if (type === 'select') {
      if (item.options && item.options.length > 0 && typeof item.options[0].value === 'number') {
        return { trigger: ['blur', 'change'], type: 'number' }
      }
      return { trigger: ['blur', 'change'] }
    }
    if (type === 'select-multiple') {
      return { trigger: ['blur', 'change'], type: 'array' }
    }
    return { trigger: ['blur', 'input'] }
  }

  // 必填校验
  if (item.rules.required) {
    validations.push({
      required: true,
      message: t('common.required'),
      ...getTriggerAndType(item.type, item)
    })
  }

  // 正则校验
  if (item.rules.regex) {
    try {
      const pattern = typeof item.rules.regex === 'string'
        ? new RegExp(item.rules.regex)
        : item.rules.regex
      validations.push({
        validator: (rule: any, value: any) => {
          if (pattern) {
            if (!pattern.test(value)) {
              return new Error(t('common.formatError'))
            }
            return true
          }
        },
        ...getTriggerAndType(item.type, item)
      })
    }
    catch (e) {
    }
  }

  // 自定义校验
  if (item.rules?.validator) {
    validations.push({
      validator: (rule: any, value: any) => {
        const valid = item.rules!.validator!(value)
        if (!valid)
          return new Error(t('common.validateError'))
        return true
      },
      ...getTriggerAndType(item.type, item)
    })
  }

  return validations
}

// 实现过滤功能
const filteredData = computed(() => {
  if (!filter.value) return modelValue.value

  const searchText = filter.value.toLowerCase()
  return modelValue.value.filter((row) => {
    // 遍历所有列，检查每一列的值是否包含搜索文本
    return Object.values(row).some(value => {
      if (value === null || value === undefined) return false
      return String(value).includes(searchText)
    })
  })
})

const columns = computed(() => {
  if (!Array.isArray(props.formSchema)) return []
  const baseColumns = props.formSchema.map(config => {
    return {
      title: config.remark ? () => {
        return h('div', { class: 'flex items-center gap-1' }, [
          h(NPopover, {
            trigger: 'hover',
          }, {
            trigger: () => h('div', { class: 'i-icon-park-outline-help text-gray-400 text-sm' }),
            default: () => h('div', config.remark ? t(config.remark) : '')
          }),
          h('span', t(config.label))
        ]);
      } : t(config.label),
      key: config.prop,
      minWidth: 140,
      render(row: RowData, index: number | string) {
        // 在多行编辑模式下，所有行始终处于编辑状态
        // 渲染对应的编辑组件
        const renderEditComponent = () => {
          switch (config.type) {
            case 'input':
              return h(NInput, {
                value: row[config.prop],
                size: 'small',
                onUpdateValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'input-number':
              return h(NInputNumber, {
                value: row[config.prop],
                size: 'small',
                onUpdateValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'select':
              return h(NSelect, {
                value: row[config.prop],
                size: 'small',
                options: config.options || [],
                onUpdateValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'radio':
              return h(NRadioGroup, {
                value: row[config.prop],
                size: 'small',
                options: config.options || [],
                onUpdateValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'date':
              return h(NDatePicker, {
                formattedValue: row[config.prop],
                size: 'small',
                type: 'date',
                format: 'yyyy-MM-dd',
                onUpdateFormattedValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'date-time':
              return h(NDatePicker, {
                formattedValue: row[config.prop],
                size: 'small',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                onUpdateFormattedValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })

            default:
              return row[config.prop]
          }
        }

        // 使用 FormItem 包装编辑组件
        return h(NFormItem, {
          rule: generateRules(config),
          path: `${index}.${config.prop}`,
          feedbackClass: 'custom-feedback'
        }, {
          default: () => renderEditComponent()
        })
      }
    }
  })

  // 操作列只保留删除按钮
  const actionColumn = {
    title: t('common.action'),
    key: 'actions',
    width: 100,
    fixed: 'left',
    render(row: RowData, index: number | string) {
      return [
        // 只保留删除按钮
        h(NButton, {
          size: 'small',
          type: 'primary',
          text: true,
          class: 'mx-1',
          onClick: () => handleDelete(index)
        }, { default: () => t('common.delete') }),
      ]
    }
  }

  return [actionColumn, ...baseColumns] as TableColumn[]
})

// 删除行方法
function handleDelete(index: number | string) {
  // 直接删除本地数据，不需要发送API请求
  modelValue.value.splice(Number(index), 1)
}

// 行选中
function handleSelect(keys: (string | number)[]) {
  checkedRowKeys.value = keys as string[]
}

function init(rowData: RowData[]) {
  modelValue.value = rowData
}

// 验证所有表单项
async function validate() {
  try {
    await formRef.value?.validate((_errors) => {
      if (_errors) {
        // 拼装错误信息
        const errorMessage = _errors.map((list) => {
          return list.map((item) => {
            // 从字段路径中提取实际的字段名（去掉行号前缀）
            const fieldParts = item.field?.split('.')
            if (fieldParts && fieldParts.length > 1) {
              const actualField = fieldParts[1]
              return t('error.no') + (Number(fieldParts[0]) + 1) + t('error.row') + t(`system.header.${actualField}`) + item.message
            }
            return t(`system.header.${item.field}`) + item.message
          }).join('\n')
        }).join('\n')
        window.$notification.error({
          title: t('common.validateError'),
          content: errorMessage,
          duration: 4000
        })
        throw new Error(t('common.validateError'))
      }
    })
    return true
  } catch (errors) {
    return false
  }
}

// 提交数据方法，供父组件调用
async function submit() {
  const isValid = await validate()
  if (!isValid) {
    return null
  }

  // 返回处理后的所有行数据
  return modelValue.value.map(row => {
    if (props.beforeSubmit) {
      return props.beforeSubmit(row)
    }
    return row
  })
}

defineExpose({
  init,
  submit,
  validate
})
</script>

<template>
  <div>
    <NInput class="mb-2 max-w-50" v-model:value="filter" placeholder="输入关键字进行过滤..." />
    <NForm ref="formRef" :model="modelValue">
      <NDataTable :columns="columns" :data="filteredData" :pagination="false" :single-line="false" striped size="small"
        single-column @update:checked-row-keys="handleSelect" :row-key="row => row.id" />
    </NForm>
  </div>
</template>

<style scoped>
.drag-handle {
  cursor: move;
}

:deep(.custom-feedback) {
  z-index: 1000;
  overflow: visible;
  position: absolute;
  margin-top: 30px;
  visibility: hidden;
}

:deep(.n-form-item.n-form-item--top-labelled) {
  grid-template-rows: unset;
  grid-template-columns: unset;
  overflow: visible;
}
</style>
