<script setup lang="ts">
const props = withDefaults(defineProps<{
  label?: string
  title?: string
  onOpen?: () => Promise<void> | void
  onClose?: () => Promise<void> | void
  buttonProps?: Record<string, any>
  width?: string
}>(), {
  width: '700px',
})

const modalVisible = ref(false)

// 按钮点击处理
function handleClick() {
  open()
}

// 对外暴露的打开方法
async function open(cb?: Function) {
  if (modalVisible.value)
    return
  await props.onOpen?.()
  modalVisible.value = true
  cb?.()
}

function close() {
  modalVisible.value = false
  props.onClose?.()
}

defineExpose({
  open,
  close,
})
</script>

<template>
  <n-button v-if="label" v-bind="buttonProps" size="small" @click="handleClick"
    class="w-100% overflow-hidden text-ellipsis">
    {{ label }}
  </n-button>
  <n-modal v-model:show="modalVisible" :mask-closable="false" preset="card" :title="title" :style="{
    width,
  }">
    <slot />
  </n-modal>
</template>
