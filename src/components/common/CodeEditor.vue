<script lang="ts" setup>
import type { PrismEditor } from 'prism-code-editor';
import { basicEditor } from 'prism-code-editor/setups';
import "prism-code-editor/prism/languages/javascript"
import "prism-code-editor/prism/languages/java"
import "prism-code-editor/prism/languages/markup"

const modelValue = defineModel<string>({
  required: true
})
type LangOpt = 'html' | 'javascript' | 'java' | 'xml' | 'vue' | 'json';
type PrismValidLang = 'javascript' | 'java' | 'markup';
const props = withDefaults(defineProps<{
  language?: LangOpt
  readOnly?: boolean
}>(), {
  language: 'java',
  readOnly: false,
})

function langAlias(lang: LangOpt): PrismValidLang {
  switch(lang) {
    case 'html':
    case 'xml':
    case 'vue':
      return 'markup';
    default:
      return lang as PrismValidLang;
  }
}

const options = {
  language: langAlias(props.language),
  insertSpaces: true,
  tabSize: 2,
  theme: 'prism',
  lineNumbers: true,
  readOnly: props.readOnly,
  wordWrap: false,
  value: modelValue.value,
  rtl: false,
  onUpdate(code: string) {
    modelValue.value = code;
  },
}

let editorRef: PrismEditor | null = null;

onMounted(async () => {
  let editor = document.getElementById('editor');
  while (!editor) {
    await new Promise(resolve => setTimeout(resolve, 100));
    editor = document.getElementById('editor');
  }

  editorRef = basicEditor(
    "#editor",
    options,
    () => console.log("code editor ready")
  )
})

onUnmounted(() => {
  editorRef = null;
})
</script>

<template>
  <div id="editor"></div>
</template>
