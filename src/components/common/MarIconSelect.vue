<script setup lang="ts">
import icons from '@/assets/icons.json'

const model = defineModel({
  required: true,
})

const searchValue = ref('')

const filteredIcons = computed(() => {
  if (!searchValue.value)
    return icons
  return icons.filter(icon => icon.toLowerCase().includes(searchValue.value.toLowerCase()))
})

const items = computed(() => {
  return groupItems(filteredIcons.value, 6).map((group: string[]) => {
    return group.map((icon) => {
      return icon
    })
  })
})

function groupItems(arr: string[], groupSize: number) {
  const result = []
  for (let i = 0; i < arr.length; i += groupSize) {
    result.push(arr.slice(i, i + groupSize))
  }
  return result
}
</script>

<template>
  <div class="dark:bg-[#181818] bg-[#ffffff] w-full rounded-lg border border-[#EBEAEA]">
    <div class="p-2 border-b border-[#EBEAEA] dark:border-[#333]">
      <div class="flex items-center gap-6">
        <div :class="model" class="w-8 h-8" />
        <n-input
          v-model:value="searchValue"
          placeholder="搜索图标"
          clearable
        >
          <template #prefix>
            <div class="icon-park-outline-search h-1.2rem w-1.2rem" />
          </template>
        </n-input>
      </div>
    </div>

    <div class="h-30">
      <n-virtual-list
        :items="items"
        :item-size="40"
      >
        <template #default="{ item }">
          <div class="flex items-center justify-between w-full p-2">
            <div
              v-for="icon in item"
              :key="icon"
              :class="icon"
              class="mb-2px w-6 h-6 cursor-pointer p-4px dark:hover:bg-[#2A2A2A] hover:bg-[#EBEAEA]"
              @click="model = icon"
            />
          </div>
        </template>
      </n-virtual-list>
    </div>
  </div>
</template>
