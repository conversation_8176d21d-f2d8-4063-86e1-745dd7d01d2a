<script setup lang="ts">
import { NInput } from 'naive-ui'

const props = defineProps<{
  value: string | number
  onUpdateValue: (value: string | number) => void
}>()

const isEdit = ref(false)
const inputRef = ref<InstanceType<typeof NInput>>()
const inputValue = ref(props.value)

function handleClick() {
  isEdit.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

function handleChange() {
  props.onUpdateValue(inputValue.value)
  isEdit.value = false
}
</script>

<template>
  <div class="min-h-22px" @click="handleClick">
    <NInput v-if="isEdit" ref="inputRef" v-model="inputValue" @blur="handleChange" />
    <span v-else>{{ value }}</span>
  </div>
</template>
