import { TreeSelectOption } from 'naive-ui'
import type { Ref, Component } from 'vue'

export interface TreeOption {
  label: string
  value: string
  children?: TreeOption[]
}

export type FormSchema = Array<{
  /**
   * form label
   */
  label: string
  /**
   * property key
   */
  prop: string
  /**
   * form type
   */
  type: 'input'
  | 'input-group'
  | 'input-number'
  | 'select'
  | 'select-multiple'
  | 'radio'
  | 'upload'
  | 'icon-select'
  | 'tree-select'
  | 'checkbox-group'
  | 'tree-cascade'
  | 'date'
  | 'date-time'
  | 'date-range'
  | 'textarea'
  | 'upload-image'
  | 'upload-file'
  /**
   * bind ref
   */
    bindRef?: (ref: any) => void
  /**
   * @optional
   * type: select, select-multiple, tree-select, radio
   */
  options?: { label: string, value: string | number }[] | TreeOption[] | TreeSelectOption[]
  /**
   * @optional
   * default value
   */
  default?: string | number | boolean | unknown[] | Record<string, any>
  /**
   * @optional
   * type: upload
   */
  extra?: {
    fileSizeLimit?: number
    fileType?: string
    fileNumLimit?: number
    // cascade tree search
    search?: boolean
    // date picker format
    format?: string
    // input group
    inputGroup?: {
      prefix?: Component
      suffix?: Component
    }
    // tree select multiple
    multiple?: boolean
    // tree cascade check strategy
    checkStrategy?: 'all' | 'parent' | 'child'
  }
  rules?: {
    required?: boolean
    regex?: string
    /**
     * npm: validator
     */
    validator?: (value: any) => boolean
  }
  /**
   * Call when form value change
   * If return true, the form item will be hidden
   * The values on the form will be remembered
   * but will be deleted when submit
   */
  hide?: (formValue: Record<string, any>) => boolean
  /**
   * Call when form value change
   * If return true, the form item will be disabled
   */
  disabled?: boolean | ((formValue: Record<string, any>) => boolean)
  /**
   * Call when form value change
   * The return value will be used as the form item value when
   * returning a non-undefined value, the form item will be hidden
   */
  change?: (formValue: Record<string, any>) => any
  /**
   * @optional
   * remark
   */
  remark?: string
}>

export interface MarFormProps {
  /**
   * form title
   */
  title?: string
  readonly?: boolean
  /**
   * form schema
   *
   */
  formSchema: FormSchema
  /**
   * request api url
   */
  api: string
  /**
   * success callback
   */
  onSuccess?: () => void
  /**
   * cancel callback
   */
  onCancel?: () => void
  /**
   * before submit hook
   * return value will be used as the form data
   */
  beforeSubmit?: (form: Record<string, any>) =>
    | Promise<Array<any>>
    | Array<any>
  /**
   * fetch initial data
   * can be overridden by overrideFetchData
   * @param args passed parameters
   */
  fetchData?: (...args: any[]) => Promise<any>
  /**
   * called when form data changes
   * fetchData will not be triggered
   * @param changedProps changed data
   * @param form passed form reference
   */
  watchForm?: (
    changedProps: Record<string, any>,
    form: Ref<Record<string, any>>,
    initializing: boolean
  ) => void | Promise<void> | Promise<Record<string, any>> | Record<string, any>

  /**
   * actions
   */
  actions?: 'default' | 'none'
  col?: number
  breakLine?: string[]
  hideForm?: (form: Record<string, any>) => string[]
  disabledForm?: (form: Record<string, any>) => string[]
  renderForm?: (formValue: any, onUpdateValue: (prop: string, value: any) => void) => Record<string, Component>
}
