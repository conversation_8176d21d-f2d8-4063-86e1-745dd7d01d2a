import { t } from '@/modules/i18n'
import { goback } from '@/utils'
import { NButton, type ButtonProps } from 'naive-ui'

const SubmitButtonGroup = defineComponent({
  props: {
    handleSubmit: {
      type: Function,
      required: true
    },
    handleCancel: {
      type: Function,
    },
    navWhenCancel: {
      type: Boolean,
      default: false
    },
    navWhenSubmit: {
      type: Boolean,
      default: false
    },
    justify: {
      type: String,
      default: 'start'
    },
    size: {
      type: String as PropType<ButtonProps['size']>,
      default: 'small'
    }
  },
  setup(props) {
    return () => (
      <div class={`flex gap-4 justify-${props.justify}`}>
        <NButton size={props.size} onClick={async () => {
          await props.handleCancel?.()
          if (props.navWhenCancel) {
            goback(true)
          }
        }}>{t('common.cancel')}</NButton>
        <NButton type="primary" size={props.size} onClick={async () => {
          await props.handleSubmit()
          if (props.navWhenSubmit) {
            goback(true)
          }
        }}>{t('common.saveLabel')}</NButton>
      </div>
    )
  }
})

export {
  SubmitButtonGroup
}