## MarForm
```ts
export interface MarFormProps {
  /**
   * form title
   */
  title?: string
  /**
   * form schema
   *
   */
  formSchema: Component.FormSchema
  /**
   * request api url
   */
  api: string
  /**
   * success callback
   */
  onSuccess?: () => void
  /**
   * cancel callback
   */
  onCancel?: () => void
  /**
   * fetch initial data
   * can be overridden by overrideFetchData
   * @param args passed parameters
   */
  fetchData?: (...args: any[]) => Promise<any>
  /**
   * called when form data changes
   * fetchData will not be triggered
   * @param changedProps changed data
   * @param form passed form reference
   */
  watchForm?: (
    changedProps: Record<string, any>,
    form: Ref<Record<string, any>>,
    initializing: boolean
  ) => void
  /**
   * actions visible
   */
  actionsVisible?: boolean
}

interface MarFormExpose {
  init: (args?: Record<string, any>, opt?: {
    noFetch?: boolean
    overrideFetchData?: (args?: Record<string, any>) => Promise<any>
  }) => void
  close: () => void
  submit: () => void
}

type FormSchema = Array<{
  /**
   * form label
   */
  label: string
  /**
   * property key
   */
  prop: string
  /**
   * form type
   */
  type: 'input' | 'input-number' | 'select' | 'select-multiple' | 'radio' | 'upload' | 'icon-select' | 'tree-select'
  /**
   * @optional
   * type: select, select-multiple, tree-select, radio
   */
  options?: { label: string, value: string | number }[]
  /**
   * @optional
   * type: upload
   */
  extra?: {
    fileSizeLimit?: number
    fileType?: string
    fileNumLimit?: number
  }
  rules?: {
    required?: boolean
    regex?: string
    /**
     * npm: validator
     */
    validator?: (value: any) => boolean
  }
  /**
   * Call when form value change
   * If return true, the form item will be hidden
   * The values on the form will be remembered
   * but will be deleted when submit
   */
  hide?: (formValue: Record<string, any>) => boolean
  /**
   * Call when form value change
   * The return value will be used as the form item value when
   * returning a non-undefined value, the form item will be hidden
   */
  change?: (formValue: Record<string, any>) => any
}>
```

## MarDialog
```ts
export interface MarDialogProps {
  /**
   * Trigger button text
   * It controls the button visibility too
   */
  label?: string
  /**
   * dialog title
   */
  title?: string
  /**
   * open callback
   */
  onOpen?: () => Promise<void> | void
  /**
   * close callback
   */
  onClose?: () => Promise<void> | void
}
```

## FormDialog
