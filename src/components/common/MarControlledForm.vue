<script setup lang="ts">
import type { FormInst, FormItemRule, TreeSelectOption } from 'naive-ui'
import type { FormSchema, MarFormProps } from './MarFormType'
import MarUpload from '@/components/common/MarUpload.vue'
import { t } from '@/modules/i18n'
import { NGridItem, NTreeSelect } from 'naive-ui'

const modelValue = defineModel<Record<string, any>>({
  required: true,
})
type MarControlledFormProps = Pick<MarFormProps, 'formSchema' | 'col' | 'renderForm' | 'readonly'> & {
  hideList?: string[]
  disabledList?: string[]
  breakLine?: string[]
}
const props = withDefaults(defineProps<MarControlledFormProps>(), {
  col: 4,
})

function updateFormModel(newVal: Record<string, any>) {
  modelValue.value = newVal
}

// form data
watch(modelValue, (newVal) => {
  props.formSchema.forEach((item) => {
    if (item.change) {
      modelValue.value[item.prop] = item.change(newVal)
    }
  })
}, {
  deep: true,
  immediate: true,
})

/**
 * form instance
 */
const formRef = ref<FormInst | null>(null)

function getTriggerAndType(type: string, item: FormSchema[0]) {
  if (type === 'input-number') {
    return { trigger: ['blur', 'input'], type: 'number' as const }
  }
  if (type === 'select' || type === 'radio' || type === 'checkbox-group') {
    if (item.options && item.options.length > 0 && typeof item.options[0].value === 'number') {
      return { trigger: ['blur', 'change'], type: 'number' as const }
    }
    else {
      return { trigger: ['blur', 'change'] }
    }
  }
  if (type === 'select-multiple') {
    return { trigger: ['blur', 'change'], type: 'array' as const }
  }
  return { trigger: ['blur', 'input'] }
}

/**
 * generate validation rules
 */
function generateRules(item: FormSchema[0]): FormItemRule[] {
  if (props.hideList?.includes(item.prop))
    return []
  const { rules, type } = item
  if (!rules)
    return []

  const validations: FormItemRule[] = []

  // required validation
  if (rules.required) {
    validations.push({
      required: true,
      message: t('common.required'),
      ...getTriggerAndType(type, item),
    })
  }

  // regex validation
  if (rules.regex) {
    try {
      validations.push({
        validator: (rule: any, value: any) => {
          if (rules.regex) {
            const regex = typeof rules.regex === 'string' ? new RegExp(rules.regex) : rules.regex
            if (!regex.test(value)) {
              return new Error(t('common.formatError'))
            }
            return true
          }
        },
        ...getTriggerAndType(type, item),
      })
    }
    catch (e) {
      window.$message?.error?.(t('common.regexError') || '正则表达式格式错误')
    }
  }

  /**
   * custom validator
   */
  if (rules.validator) {
    validations.push({
      validator: (rule: any, value: any) => {
        const valid = rules.validator!(value)
        if (!valid)
          return new Error(t('common.validateError'))
        return true
      },
      ...getTriggerAndType(type, item),
    })
  }

  return validations
}

function reset() {
  modelValue.value = props.formSchema.reduce((acc, item) => {
    if (item.default) {
      acc[item.prop] = item.default
    }
    else if (item.type === 'input') {
      acc[item.prop] = ''
    }
    else if (item.type === 'input-number') {
      acc[item.prop] = 0
    }
    else {
      acc[item.prop] = null
    }
    return acc
  }, {} as Record<string, any>)

  formRef.value?.restoreValidation()
}

function getRenderForm(prop: string) {
  if (!props.renderForm) return null
  const forms = props.renderForm(modelValue.value, (prop: string, value: any) => {
    modelValue.value[prop] = value
  })
  return forms[prop]
}

defineExpose({
  validate: () => formRef.value?.validate(),
  reset,
  updateFormModel,
})

function disabledHelper(item: FormSchema[0]) {
  return props.disabledList?.includes(item.prop) || props.readonly || (typeof item.disabled === 'function' ? item.disabled(modelValue) : item.disabled)
}
</script>

<template>
  <n-form ref="formRef" label-placement="left" :model="modelValue" :label-width="100">
    <n-grid :cols="24" :x-gap="18">
      <template v-for="(item, index) in formSchema" :key="item.prop">
        <n-form-item-grid-item :span="24 / col" :label="t(item.label)" :path="item.prop" :rule="generateRules(item)"
          :class="{ hidden: hideList?.includes(item.prop) }">
          <template #label>
            <div class="flex justify-end gap-1">
              <n-popover trigger="hover" v-if="item.remark">
                <template #trigger>
                  <div class="i-icon-park-outline-help text-gray-400 text-lg"></div>
                </template>
                <div>{{ t(item.remark) }}</div>
              </n-popover>
              <div>{{ t(item.label) }}</div>
            </div>
          </template>
          <component :is="getRenderForm(item.prop)" v-if="props.renderForm && getRenderForm(item.prop)"
            v-model:model-value="modelValue[item.prop]" :disabled="disabledHelper(item)" />

          <n-input clearable v-else-if="item.type === 'input' || item.type === 'textarea'" v-model:value="modelValue[item.prop]"
            :disabled="disabledHelper(item)" :type="item.type === 'textarea' ? 'textarea' : 'text'" />

          <n-input-group v-else-if="item.type === 'input-group'">
            <!-- render options.prefix -->
            <component :is="item.extra?.inputGroup?.prefix" />
            <n-input clearable v-model:value="modelValue[item.prop]" :disabled="disabledHelper(item)" />
            <!-- render options.suffix -->
            <component :is="item.extra?.inputGroup?.suffix" />
          </n-input-group>

          <n-input-number clearable v-else-if="item.type === 'input-number'" v-model:value="modelValue[item.prop]"
            :disabled="disabledHelper(item)" />

          <n-select v-else-if="item.type === 'select'" v-model:value="modelValue[item.prop]" :options="item.options as any"
            filterable clearable :disabled="disabledHelper(item)" />

          <n-select v-else-if="item.type === 'select-multiple'" v-model:value="modelValue[item.prop]"
            :options="item.options as any" multiple filterable :disabled="disabledHelper(item)" :consistent-menu-width="false" />

          <n-radio-group v-else-if="item.type === 'radio'" v-model:value="modelValue[item.prop]"
            :disabled="disabledHelper(item)">
            <n-radio-button v-for="option in item.options" :key="option.value as PropertyKey"
              :value="option.value as string">
              {{ option.label }}
            </n-radio-button>
          </n-radio-group>

          <n-checkbox-group v-else-if="item.type === 'checkbox-group'" v-model:value="modelValue[item.prop]"
            :disabled="disabledHelper(item)">
            <n-checkbox v-for="option in item.options" :key="option.value as PropertyKey"
              :value="option.value as string">
              {{ option.label }}
            </n-checkbox>
          </n-checkbox-group>

          <mar-icon-select v-else-if="item.type === 'icon-select'" v-model="modelValue[item.prop]"
            :disabled="disabledHelper(item)" />

          <MarUpload v-else-if="item.type === 'upload'" v-model:model-value="modelValue[item.prop]"
            :size-limit="item.extra?.fileSizeLimit" :type="item.extra?.fileType"
            :disabled="disabledHelper(item)" />

          <n-tree-select v-else-if="item.type === 'tree-select'" v-model:value="modelValue[item.prop]"
            :options="item.options as TreeSelectOption[]" key-field="value" label-field="label"
            :disabled="disabledHelper(item)" :multiple="item.extra?.multiple" />

          <n-date-picker v-else-if="item.type === 'date'"
            type="date" :formatted-value="modelValue[item.prop] === '' ? null : modelValue[item.prop]"
            @update-formatted-value="(newVal: string) => {
              modelValue[item.prop] = newVal
            }"
            :format="item.extra?.format || 'yyyy-MM-dd'" :disabled="disabledHelper(item)" />

          <n-date-picker v-else-if="item.type === 'date-time'"
            type="datetime" :formatted-value="modelValue[item.prop] === '' ? null : modelValue[item.prop]"
            @update-formatted-value="(newVal: string) => {
              modelValue[item.prop] = newVal
            }"
            :format="item.extra?.format || 'yyyy-MM-dd HH:mm:ss'"
            :disabled="disabledHelper(item)" />

          <MarTree v-else-if="item.type === 'tree-cascade'" v-model="modelValue[item.prop]" :options="item.options as any"
            :disabled="disabledHelper(item)" />
        </n-form-item-grid-item>
        <n-grid-item v-if="props.breakLine?.includes(item.prop)" :span="24">
          <div class="border-b-1px border-solid dark:border-gray-700 border-gray-200 mb-6"></div>
        </n-grid-item>
      </template>
    </n-grid>
  </n-form>
</template>
