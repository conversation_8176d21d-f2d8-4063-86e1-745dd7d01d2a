<script setup lang="ts">
import type { FormInst, FormItemRule, TreeSelectOption } from 'naive-ui'
import type { FormSchema, MarFormProps, TreeOption } from './MarFormType'
import MarUpload from '@/components/common/MarUpload.vue'
import { useRequest } from '@/hooks/useRequest'
import { t } from '@/modules/i18n'
import { diffObj } from '@/utils/obj'
import { NGridItem, NTreeSelect } from 'naive-ui'
import MarTree from './MarTree.vue'

const props = withDefaults(defineProps<MarFormProps>(), {
  actions: 'default',
  col: 1,
})

const formModel = ref<Record<string, any>>({})
function updateFormModel(newVal: Record<string, any>) {
  formModel.value = newVal
}
const hideList = computed(() => {
  const formModelCopy = { ...formModel.value }

  const result = new Set<string>()

  if (props.hideForm) {
    props.hideForm(formModelCopy).forEach(item => result.add(item))
  }

  props.formSchema.forEach(item => {
    if (item.hide?.(formModelCopy)) {
      result.add(item.prop)
    }
  })

  return Array.from(result)
})
const disabledList = computed(() => {
  const formModelCopy = { ...formModel.value }
  const result = new Set<string>()
  if (props.disabledForm) {
    props.disabledForm(formModelCopy).forEach(item => result.add(item))
  }
  props.formSchema.forEach(item => {
    if (typeof item.disabled === 'boolean' && item.disabled) {
      result.add(item.prop)
    }
    else if (typeof item.disabled === 'function') {
      if (item.disabled(formModelCopy)) {
        result.add(item.prop)
      }
    }
  })
  return Array.from(result)
})
// form data
watch(formModel, (newVal) => {
  props.formSchema.forEach((item) => {
    if (item.change) {
      formModel.value[item.prop] = item.change(newVal)
    }
  })
}, {
  deep: true,
  immediate: true,
})

/**
 * if form is initializing
 */
const isInitializing = ref(false)

if (props.watchForm) {
  let oldValue: Record<string, any> = {}

  watch(formModel, (newVal) => {
    const diff = diffObj(newVal, oldValue)
    oldValue = JSON.parse(JSON.stringify(newVal))
    props.watchForm?.(diff, formModel, isInitializing.value)
  }, { deep: true })
}

/**
 * form instance
 */
const formRef = ref<FormInst | null>(null)

/**
 * open dialog
 */
async function init(args?: Record<string, any>, opt?: {
  noFetch?: boolean
  overrideFetchData?: (args?: Record<string, any>) => Promise<any>
}) {
  formRef.value?.restoreValidation()
  isInitializing.value = true

  // initialize form data
  formModel.value = props.formSchema.reduce((acc, item) => {
    if (item.default) {
      acc[item.prop] = !isNaN(Number(item.default)) ? Number(item.default) : item.default
    }
    else if (item.type === 'input') {
      acc[item.prop] = ''
    }
    else if (item.type === 'input-number') {
      acc[item.prop] = 0
    }
    else {
      acc[item.prop] = null
    }
    return acc
  }, {} as Record<string, any>)

  if (opt?.overrideFetchData) {
    const data = await opt.overrideFetchData(args)
    formModel.value = {
      ...args,
      ...data,
    }
    nextTick(() => {
      isInitializing.value = false
    })
    isInitializing.value = false
  }
  else if (props.fetchData && !opt?.noFetch) {
    const data = await props.fetchData(args)
    formModel.value = {
      ...args,
      ...data,
    }
    nextTick(() => {
      isInitializing.value = false
    })
  }
  else {
    nextTick(() => {
      isInitializing.value = false
    })
  }
}

/**
 * cancel form
 */
function close() {
  formModel.value = {}
  formRef.value?.restoreValidation()
  props.onCancel?.()
}

/**
 * submit form
 */
async function submit(): Promise<any> {
  try {
    formRef.value?.restoreValidation()
    await formRef.value?.validate()

    // deep clone and filter hideList
    const formData = JSON.parse(JSON.stringify(formModel.value))
    hideList.value.forEach((key) => {
      delete formData[key]
    })
    let submitUrl = props.api
    let submitData = formData
    if (props.beforeSubmit) {
      const result = await props.beforeSubmit(formData)
      if (!result || result.length === 0) {
        return
      }
      submitData = result[0]
      submitUrl = result[1] || props.api
    }
    const { data, error } = await useRequest(submitUrl).post(JSON.stringify(submitData))
    if (error.value) {
      throw error.value
    }

    window.$message.success(t('common.operationSuccess'))
    props.onSuccess?.()
    props.onCancel?.()
    if (data.value) {
      return data.value
    }
  }
  catch (e) {
  }
}

function getTriggerAndType(type: string, item: FormSchema[0]) {
  if (type === 'input-number') {
    return { trigger: ['blur', 'input'], type: 'number' as const }
  }
  if (type === 'select' || type === 'radio' || type === 'checkbox-group') {
    if (item.options && item.options.length > 0 && typeof item.options[0].value === 'number') {
      return { trigger: ['blur', 'change'], type: 'number' as const }
    }
    else {
      return { trigger: ['blur', 'change'] }
    }
  }
  if (type === 'select-multiple') {
    return { trigger: ['blur', 'change'], type: 'array' as const }
  }
  return { trigger: ['blur', 'input'] }
}

/**
 * generate validation rules
 */
function generateRules(item: FormSchema[0]): FormItemRule[] {
  if (hideList.value.includes(item.prop))
    return []
  const { rules, type } = item
  if (!rules)
    return []

  const validations: FormItemRule[] = []

  // required validation
  if (rules.required) {
    validations.push({
      required: true,
      message: t('common.required'),
      ...getTriggerAndType(type, item),
    })
  }

  // regex validation
  if (rules.regex) {
    try {
      validations.push({
        validator: (rule: any, value: any) => {
          if (rules.regex) {
            const regex = typeof rules.regex === 'string' ? new RegExp(rules.regex) : rules.regex
            if (!regex.test(value)) {
              return new Error(t('common.formatError'))
            }
            return true
          }
        },
        ...getTriggerAndType(type, item),
      })
    }
    catch (e) {
      window.$message?.error?.(t('common.regexError') || '正则表达式格式错误')
    }
  }

  /**
   * custom validator
   */
  if (rules.validator) {
    validations.push({
      validator: (rule: any, value: any) => {
        const valid = rules.validator!(value)
        if (!valid)
          return new Error(t('common.validateError'))
        return true
      },
      ...getTriggerAndType(type, item),
    })
  }

  return validations
}

function reset() {
  formModel.value = props.formSchema.reduce((acc, item) => {
    if (item.default) {
      acc[item.prop] = item.default
    }
    else if (item.type === 'input') {
      acc[item.prop] = ''
    }
    else if (item.type === 'input-number') {
      acc[item.prop] = 0
    }
    else {
      acc[item.prop] = null
    }
    return acc
  }, {} as Record<string, any>)

  formRef.value?.restoreValidation()
}

function getRenderForm(prop: string) {
  if (!props.renderForm) return null
  const forms = props.renderForm(formModel.value, (prop: string, value: any) => {
    formModel.value[prop] = value
  })
  return forms[prop]
}

defineExpose({
  init,
  close,
  submit,
  reset,
  formModel,
  updateFormModel,
})
</script>

<template>
  <n-form ref="formRef" label-placement="left" :model="formModel" :label-width="100">
    <n-grid :cols="24" :x-gap="18">
      <template v-for="(item, index) in formSchema" :key="item.prop">
        <n-form-item-grid-item :span="24 / col" :label="t(item.label)" :path="item.prop" :rule="generateRules(item)"
          :class="{ hidden: hideList.includes(item.prop) }">
          <template #label>
            <div class="flex justify-end gap-1">
              <NPopover v-if="item.remark">
                <template #trigger>
                  <div class="i-icon-park-outline-help text-gray-400 text-sm"></div>
                </template>
                <div class="whitespace-pre">{{ t(item.remark) }}</div>
              </NPopover>
              <div>{{ t(item.label) }}</div>
            </div>
          </template>
          <component :is="getRenderForm(item.prop)" v-if="props.renderForm && getRenderForm(item.prop)"
            v-model:model-value="formModel[item.prop]" :disabled="disabledList.includes(item.prop)"
            :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <n-input clearable v-else-if="item.type === 'input'" v-model:value="formModel[item.prop]"
            :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <n-input-group v-else-if="item.type === 'input-group'">
            <!-- render options.prefix -->
            <component :is="item.extra?.inputGroup?.prefix" />
            <n-input clearable v-model:value="formModel[item.prop]" :disabled="disabledList.includes(item.prop)"
              :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />
            <!-- render options.suffix -->
            <component :is="item.extra?.inputGroup?.suffix" />
          </n-input-group>

          <n-input-number clearable v-else-if="item.type === 'input-number'" v-model:value="formModel[item.prop]"
            :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <n-select v-else-if="item.type === 'select'" v-model:value="formModel[item.prop]" :options="item.options as any"
            filterable clearable :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <n-select v-else-if="item.type === 'select-multiple'" v-model:value="formModel[item.prop]" :consistent-menu-width="false"
            :options="item.options as any" multiple filterable :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <n-radio-group v-else-if="item.type === 'radio'" v-model:value="formModel[item.prop]"
            :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }">
            <n-radio-button v-for="option in item.options" :key="option.value as PropertyKey"
              :value="option.value as string">
              {{ option.label }}
            </n-radio-button>
          </n-radio-group>

          <n-checkbox-group v-else-if="item.type === 'checkbox-group'" v-model:value="formModel[item.prop]"
            :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }">
            <n-checkbox v-for="option in item.options" :key="option.value as PropertyKey"
              :value="option.value as string">
              {{ option.label }}
            </n-checkbox>
          </n-checkbox-group>

          <mar-icon-select v-else-if="item.type === 'icon-select'" v-model="formModel[item.prop]"
            :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <MarUpload v-else-if="item.type === 'upload'" v-model:model-value="formModel[item.prop]"
            :size-limit="item.extra?.fileSizeLimit" :type="item.extra?.fileType"
            :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <n-tree-select v-else-if="item.type === 'tree-select'" v-model:value="formModel[item.prop]"
            :options="item.options as TreeSelectOption[]" key-field="value" label-field="label" :disabled="disabledList.includes(item.prop)"
            :multiple="item.extra?.multiple" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <n-date-picker v-else-if="item.type === 'date'" v-model:formatted-value="formModel[item.prop]" type="date"
            :format="item.extra?.format || 'yyyy-MM-dd'" :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <n-date-picker v-else-if="item.type === 'date-time'" v-model:formatted-value="formModel[item.prop]"
            type="datetime" :format="item.extra?.format || 'yyyy-MM-dd HH:mm:ss'"
            :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />

          <!-- <n-space v-else-if="item.type === 'tree-cascade'" vertical :size="12">
            <n-tree v-model:checked-keys="formModel[item.prop]" cascade block-line checkable key-field="value"
              label-field="label" :selectable="false" :data="item.options"
              :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" :check-strategy="item.extra?.checkStrategy" />
          </n-space> -->
          <!-- <n-tree-select v-else-if="item.type === 'tree-cascade'" v-model:value="formModel[item.prop]" checkable
            key-field="value" label-field="label" :selectable="false" :options="item.options" multiple cascade
            :disabled="disabledList.includes(item.prop)" max-tag-count="responsive" check-strategy="all"
            :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" /> -->
          <MarTree v-else-if="item.type === 'tree-cascade' && item.options" v-model="formModel[item.prop]"
            :options="item.options as TreeOption[]" :disabled="disabledList.includes(item.prop)" :ref="(el) => {
              if (item.bindRef && el) {
                item.bindRef(el)
              }
            }" />
        </n-form-item-grid-item>
        <n-grid-item v-if="props.breakLine?.includes(item.prop)" :span="24">
          <div class="border-b-1px border-solid dark:border-gray-700 border-gray-200 mb-6"></div>
        </n-grid-item>
      </template>
    </n-grid>
    <div v-if="actions === 'default'" class="flex justify-end gap-2">
      <n-button @click="close">
        {{ t('common.cancel') }}
      </n-button>
      <n-button type="primary" @click="submit">
        {{ t('common.submit') }}
      </n-button>
    </div>
  </n-form>
</template>
