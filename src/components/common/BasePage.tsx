import type { Component } from 'vue'
import 'reflect-metadata'
import { h, ref, defineComponent } from 'vue'
import type { Core, App, Router, Request, Components, Dict, I18n, Message, Dialog, Utils } from '@/core'
import type { IController } from '@/decorator/controller'

class BasePage implements IController {
  app: App;
  router: Router;
  request: Request;
  components: Components;
  dict: Dict;
  i18n: I18n;
  message: Message;
  dialog: Dialog;
  utils: Utils;
  
  // 实际会渲染的 Vue 组件
  public view: Component
  
  render() {
    return <div>Inform</div>
  }


  constructor(core: Core) {
    this.app = core.app
    this.router = core.router
    this.request = core.request
    this.components = core.components
    this.dict = core.dict
    this.i18n = core.i18n
    this.message = core.message
    this.dialog = core.dialog
    this.utils = core.utils

    this.view = defineComponent({
      setup: () => {
        return () => this.render()
      }
    })
  }
}

export default BasePage
