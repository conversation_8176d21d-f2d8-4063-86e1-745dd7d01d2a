<script setup lang="ts">
import { useFileDialog } from '@vueuse/core'
import { useDownload } from '@/hooks'
import { t } from '@/modules/i18n'

interface Props {
  // 文件大小限制(MB)
  sizeLimit?: number
  // 文件类型
  type?: string
  // 下载模板接口
  templateApi?: string
  // 是否多选
  multiple?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  sizeLimit: 10,
  type: 'xlsx',
  templateApi: '',
  uploadApi: '',
  multiple: true,
})

const modelValue = defineModel<File | null>('modelValue', {
  required: true,
})

const { files, open, reset } = useFileDialog({
  accept: props.type === 'xlsx' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' : '*',
  multiple: props.multiple,
})

// 监听文件选择
watch(files, (newFiles) => {
  if (!newFiles?.length) return
  
  const file = newFiles[0]
  validateFile(file)
})

// 处理上传前验证
function validateFile(file: File) {
  // 检查文件大小
  if (props.sizeLimit && file.size > props.sizeLimit * 1024 * 1024) {
    window.$message?.error(t('hooks.useUpload.fileSize'))
    reset()
    return
  }

  modelValue.value = file
}

// 处理拖拽
function handleDrop(e: DragEvent) {
  e.preventDefault()
  const files = e.dataTransfer?.files
  if (!files?.length) return
  validateFile(files[0])
}

function handleDragover(e: DragEvent) {
  e.preventDefault()
}

// 下载模板
async function downloadTemplate() {
  if (!props.templateApi) return
  
  try {
    await useDownload(props.templateApi, {
      method: 'get',
    })
  } catch (error) {
    window.$message.error(t('error.downloadFailed'))
  }
}
</script>

<template>
  <n-space vertical class="w-full flex flex-col items-center justify-center">
    <div
      class="border-dashed border-2 border-gray-300 rounded-lg p-4 hover:border-primary transition-colors cursor-pointer"
      @click="(e: MouseEvent) => open()"
      @drop="handleDrop"
      @dragover="handleDragover"
    >
      <div class="text-gray-400 text-sm w-300px h-120px flex items-center justify-center">
        {{ modelValue ? modelValue.name : $t('common.dragUploadTip') }}
      </div>
    </div>

    <n-button v-if="templateApi" type="primary" @click.stop="downloadTemplate">
      <template #icon>
        <div class="i-icon-park-outline-download h-1.2rem w-1.2rem" />
      </template>
      {{ t('common.downloadTemplate') }}
    </n-button>
  </n-space>
</template>

<style>
.n-upload {
  display: flex;
  justify-content: center;
}
</style>
