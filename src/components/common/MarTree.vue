<script setup lang="tsx">
import { ref, defineComponent } from 'vue' // Import Transition
import { TreeOption } from './MarFormType';

// 树形选择器

/**
 * 受控组件，绑定值为 id 数组
 * 取消勾选子节点时，父节点不会受到影响
 * 勾选子节点时，如果父节点没有勾选，则勾选父节点，如果父节点的父节点没有勾选，则勾选父节点的父节点，以此类推
 * 勾选或取消勾选父节点时，其子节点也会同步被勾选或者取消勾选
 */
const modelValue = defineModel<string[]>()

const props = defineProps<{
  options: TreeOption[]
  disabled?: boolean
}>()

// 存储展开状态的节点
const expandedNodes = ref<Set<string>>(new Set())

// 切换节点展开/折叠状态
const toggleExpand = (nodeValue: string, event: Event) => {
  event.stopPropagation()
  if (expandedNodes.value.has(nodeValue)) {
    expandedNodes.value.delete(nodeValue)
  } else {
    expandedNodes.value.add(nodeValue)
  }
}

// 获取所有子节点的值（包括孙子节点等）
const getAllChildrenValues = (node: TreeOption): string[] => {
  const values: string[] = [node.value]
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      values.push(...getAllChildrenValues(child))
    })
  }
  return values
}

// 获取所有父节点的值
const getAllParentValues = (options: TreeOption[], value: string, path: string[] = []): string[] => {
  for (const option of options) {
    const currentPath = [...path, option.value]
    
    if (option.value === value) {
      return path
    }
    
    if (option.children && option.children.length > 0) {
      const result = getAllParentValues(option.children, value, currentPath)
      if (result.length > 0) {
        return result
      }
    }
  }
  
  return []
}

// 处理节点选择
const handleNodeSelect = (node: TreeOption, checked: boolean) => {
  const newModelValue = [...modelValue.value || []]
  
  if (checked) {
    // 添加当前节点
    if (!newModelValue.includes(node.value)) {
      newModelValue.push(node.value)
    }
    
    // 添加所有子节点
    if (node.children && node.children.length > 0) {
      const childrenValues = getAllChildrenValues(node).filter(v => v !== node.value)
      childrenValues.forEach(value => {
        if (!newModelValue.includes(value)) {
          newModelValue.push(value)
        }
      })
    }
    
    // 添加所有父节点
    const parentValues = getAllParentValues(props.options, node.value)
    parentValues.forEach(value => {
      if (!newModelValue.includes(value)) {
        newModelValue.push(value)
      }
    })
  } else {
    // 移除当前节点
    const index = newModelValue.indexOf(node.value)
    if (index !== -1) {
      newModelValue.splice(index, 1)
    }
    
    // 移除所有子节点
    if (node.children && node.children.length > 0) {
      const childrenValues = getAllChildrenValues(node).filter(v => v !== node.value)
      childrenValues.forEach(value => {
        const childIndex = newModelValue.indexOf(value)
        if (childIndex !== -1) {
          newModelValue.splice(childIndex, 1)
        }
      })
    }
    
    // 父节点不受影响
  }
  
  modelValue.value = newModelValue
}

// 默认展开第一级节点
props.options.forEach(node => {
  if (node.children && node.children.length > 0) {
    expandedNodes.value.add(node.value)
  }
})

// 递归树节点组件
const TreeNode = defineComponent({
  name: 'TreeNode',
  props: {
    node: {
      type: Object as () => TreeOption,
      required: true
    },
    level: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { slots }) {
    return () => (
      <div class="tree-node">
        <div 
          class="flex items-center py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer"
          onClick={() => !props.disabled && handleNodeSelect(props.node, !modelValue.value?.includes(props.node.value))}
        >
          {/* Checkbox with Transition */}
          <div class="w-5 h-5 flex items-center justify-center mr-1 relative"> {/* Added relative positioning */}
              {modelValue.value?.includes(props.node.value) ? (
                <div key="checked" class="i-mdi-checkbox-marked text-lg text-red-700 absolute"></div>
              ) : (
                <div key="unchecked" class="i-mdi-checkbox-blank-outline text-lg text-gray-500 dark:text-gray-400 absolute"></div>
              )}
          </div>
          
          {/* Expand/Collapse icon */}
          {props.node.children && props.node.children.length > 0 ? (
            <div 
              class="mr-1 w-5 h-5 flex items-center justify-center cursor-pointer"
              onClick={(e) => toggleExpand(props.node.value, e)}
            >
              <div 
                class={[
                  expandedNodes.value.has(props.node.value) 
                    ? 'i-heroicons-outline-chevron-down' 
                    : 'i-heroicons-outline-chevron-right',
                  'text-gray-500 dark:text-gray-400'
                ]}
              ></div>
            </div>
          ) : (
            <div class="mr-1 w-5 h-5"></div>
          )}
          
          {/* Label */}
          <div class="flex-1 truncate">{props.node.label}</div>
        </div>
        
        {/* Children */}
        {props.node.children && props.node.children.length > 0 && expandedNodes.value.has(props.node.value) && (
          <div class="pl-5">
            {props.node.children.map(child => (
              <TreeNode 
                key={child.value} 
                node={child} 
                level={props.level + 1}
                disabled={props.disabled}
              />
            ))}
          </div>
        )}
      </div>
    )
  }
})
</script>

<template>
  <div class="mar-tree">
    <div class="tree-container">
      <TreeNode
        v-for="node in options"
        :key="node.value"
        :node="node"
        :level="0"
        :disabled="disabled"
      />
    </div>
  </div>
</template>

<style scoped>
.mar-tree {
  width: 100%;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 4px;
  padding: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.tree-node {
  margin-bottom: 2px;
}

.tree-children {
  margin-left: 4px;
}

</style>
