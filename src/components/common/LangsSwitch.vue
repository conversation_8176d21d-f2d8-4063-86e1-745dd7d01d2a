<script setup lang="ts">
import { useApp } from '@/hooks'

const { state, setAppLang } = useApp()
const options = [
  {
    label: 'English',
    value: 'enUS',
  },
  {
    label: '中文',
    value: 'zhCN',
  },
]
</script>

<template>
  <n-popselect :value="state.lang" :options="options" trigger="click" @update:value="setAppLang">
    <CommonWrapper>
      <div class="i-icon-park-outline-translate h-1.2rem w-1.2rem" />
    </CommonWrapper>
  </n-popselect>
</template>
