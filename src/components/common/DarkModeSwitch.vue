<script setup lang="tsx">
import { NFlex } from 'naive-ui'
import { useApp } from '@/hooks'

const { t } = useI18n()

const { setColorMode, storeColorMode } = useApp()
const options = computed(() => {
  return [
    {
      label: t('app.light'),
      value: 'light',
      icon: () => h('div', { class: 'i-icon-park-outline-sun-one h-1.2rem w-1.2rem' }),
    },
    {
      label: t('app.dark'),
      value: 'dark',
      icon: () => h('div', { class: 'i-icon-park-outline-moon h-1.2rem w-1.2rem' }),
    },
    {
      label: t('app.system'),
      value: 'auto',
      icon: () => h('div', { class: 'i-icon-park-outline-laptop-computer h-1.2rem w-1.2rem' }),
    },
  ]
})

function renderLabel(option: any) {
  return h(NFlex, { align: 'center' }, {
    default: () => [
      h(option.icon),
      option.label,
    ],
  })
}
</script>

<template>
  <n-popselect :value="storeColorMode" :render-label="renderLabel" :options="options" trigger="click" @update:value="setColorMode">
    <CommonWrapper>
      <div v-if="storeColorMode === 'dark'" class="i-icon-park-outline-moon h-1.2rem w-1.2rem" />
      <div v-if="storeColorMode === 'light'" class="i-icon-park-outline-sun-one h-1.2rem w-1.2rem" />
      <div v-if="storeColorMode === 'auto'" class="i-icon-park-outline-laptop-computer h-1.2rem w-1.2rem" />
    </CommonWrapper>
  </n-popselect>
</template>
