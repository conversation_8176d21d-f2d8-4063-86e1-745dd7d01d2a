<script setup lang="ts">
import { NDataTable, NInput, NInputNumber, NSelect, NSwitch, NButton, NForm, NFormItem, NRadioGroup, NDatePicker, NPopover } from 'naive-ui'
import type { RowData, TableColumn } from 'naive-ui/es/data-table/src/interface'
import type { MarFormProps } from './MarFormType'
import type { FormInst, FormItemRule } from 'naive-ui'
import { t } from '@/modules/i18n';
import { useRequest } from '@/hooks';

const props = defineProps<Omit<MarFormProps, 'api'> & {
  api: {
    save?: string
    update?: string
    delete?: string
  }
  beforeSubmit?: (form: any) => any
}>()

const modelValue = defineModel<RowData[]>({
  required: true,
})
const currentEditRow = defineModel<string | null>('currentEditRow', {
  required: true,
})
const checkedRowKeys = defineModel<string[]>('checkedRowKeys', {
  required: true,
})

// 为每一行创建一个 form 实例
const formRef = ref<FormInst | null>(null)

// 监听编辑行变化，重置表单验证状态
watch(currentEditRow, () => {
  // 当切换编辑行时，重置表单验证状态
  if (formRef.value) {
    formRef.value.restoreValidation()
  }
})

// 生成校验规则
function generateRules(item: MarFormProps['formSchema'][0]): FormItemRule[] {
  if (!item.rules) return []
  const validations: FormItemRule[] = []

  // 获取触发器和类型
  function getTriggerAndType(type: string, item: MarFormProps['formSchema'][0]): { trigger: string[], type?: 'number' | 'array' } {
    if (type === 'input-number') {
      return { trigger: ['blur', 'input'], type: 'number' }
    }
    if (type === 'select') {
      if (item.options && item.options.length > 0 && typeof item.options[0].value === 'number') {
        return { trigger: ['blur', 'change'], type: 'number' }
      }
      return { trigger: ['blur', 'change'] }
    }
    if (type === 'select-multiple') {
      return { trigger: ['blur', 'change'], type: 'array' }
    }
    return { trigger: ['blur', 'input'] }
  }

  // 必填校验
  if (item.rules.required) {
    validations.push({
      required: true,
      message: t('common.required'),
      ...getTriggerAndType(item.type, item)
    })
  }

  // 正则校验
  if (item.rules.regex) {
    try {
      const pattern = typeof item.rules.regex === 'string'
        ? new RegExp(item.rules.regex)
        : item.rules.regex
      validations.push({
        validator: (rule: any, value: any) => {
          if (pattern) {
            if (!pattern.test(value)) {
              return new Error(t('common.formatError'))
            }
            return true
          }
        },
        ...getTriggerAndType(item.type, item)
      })
    }
    catch (e) {
    }
  }

  // 自定义校验
  if (item.rules?.validator) {
    validations.push({
      validator: (rule: any, value: any) => {
        const valid = item.rules!.validator!(value)
        if (!valid)
          return new Error(t('common.validateError'))
        return true
      },
      ...getTriggerAndType(item.type, item)
    })
  }

  return validations
}

const columns = computed(() => {
  if (!Array.isArray(props.formSchema)) return []
  const baseColumns = props.formSchema.map(config => {
    return {
      title: config.remark ? () => {
        return h('div', { class: 'flex items-center gap-1' }, [
          h(NPopover, {
            trigger: 'hover',
          }, {
            trigger: () => h('div', { class: 'i-icon-park-outline-help text-gray-400 text-sm' }),
            default: () => h('div', config.remark ? t(config.remark) : '')
          }),
          h('span', t(config.label))
        ]);
      } : t(config.label),
      key: config.prop,
      minWidth: 140,
      render(row: RowData, index: number | string) {
        // 判断是否是当前编辑行
        const isEdit = currentEditRow.value === String(index)

        // 如果不是编辑状态，则显示值
        if (!isEdit) {
          // 判断是否有 options 字段
          if (config.options) {
            // 如果有 options 字段，则显示 options 中的 label
            return config.options.find(option => option.value === row[config.prop])?.label
          }
          return row[config.prop]
        }

        // 编辑状态下显示对应的编辑组件
        const renderEditComponent = () => {
          switch (config.type) {
            case 'input':
              return h(NInput, {
                value: row[config.prop],
                size: 'small',
                onUpdateValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'input-number':
              return h(NInputNumber, {
                value: row[config.prop],
                size: 'small',
                onUpdateValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'select':
              return h(NSelect, {
                value: row[config.prop],
                size: 'small',
                options: config.options || [],
                onUpdateValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'radio':
              return h(NRadioGroup, {
                value: row[config.prop],
                size: 'small',
                options: config.options || [],
                onUpdateValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'date':
              return h(NDatePicker, {
                formattedValue: row[config.prop],
                size: 'small',
                type: 'date',
                format: 'yyyy-MM-dd',
                onUpdateFormattedValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })
            case 'date-time':
              return h(NDatePicker, {
                formattedValue: row[config.prop],
                size: 'small',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                onUpdateFormattedValue(v) {
                  modelValue.value[index][config.prop] = v
                },
                disabled: config.disabled === true
              })

            default:
              return row[config.prop]
          }
        }

        // 使用 FormItem 包装编辑组件
        return h(NFormItem, {
          rule: generateRules(config),
          path: config.prop,
          feedbackClass: 'custom-feedback'
        }, {
          default: () => renderEditComponent()
        })
      }
    }
  })

  // 操作列
  const actionColumn = {
    title: t('common.action'),
    key: 'actions',
    width: 180,
    fixed: 'left',
    render(row: RowData, index: number | string) {
      const isEdit = currentEditRow.value === String(index)
      if (isEdit) {
        return [
          // 保存按钮
          h(NButton, {
            size: 'small',
            type: 'primary',
            text: true,
            class: 'mx-1',
            onClick: () => handleSave(index)
          }, { default: () => t('common.saveLabel') }),
          // 删除按钮
          h(NButton, {
            size: 'small',
            type: 'primary',
            text: true,
            class: 'mx-1',
            onClick: () => handleDelete(index)
          }, { default: () => t('common.delete') }),
        ]
      }
      return [
        // 编辑按钮
        h(NButton, {
          size: 'small',
          type: 'primary',
          text: true,
          class: 'mx-1',
          onClick: () => handleEdit(index)
        }, { default: () => t('common.edit') }),
        // 删除按钮
        h(NButton, {
          size: 'small',
          type: 'primary',
          text: true,
          class: 'mx-1',
          onClick: () => handleDelete(index)
        }, { default: () => t('common.delete') }),
      ]
    }
  }
  const checkboxColumn = {
    type: 'selection',
    fixed: 'left',
  }

  return [checkboxColumn, actionColumn, ...baseColumns] as TableColumn[]
})

// 删除行方法
async function handleDelete(index: number | string) {
  // 获取行数据，查看是否有 id 字段
  const row = modelValue.value[Number(index)]
  if (row.id && props.api.delete) {
    const { error } = await useRequest(props.api.delete).post(JSON.stringify([
      row.id
    ]))
    if (error.value) {
      return
    }
    modelValue.value.splice(Number(index), 1)
    currentEditRow.value = null
  } else {
    modelValue.value.splice(Number(index), 1)
    currentEditRow.value = null
  }
}

// 编辑行方法
function handleEdit(index: number | string) {
  // 切换编辑行前，先重置验证状态
  if (formRef.value) {
    formRef.value.restoreValidation()
  }
  currentEditRow.value = String(index)
}

// 修改保存方法
async function handleSave(index: number | string) {
  try {
    // 保存前清除之前的验证状态
    formRef.value?.restoreValidation()

    // 然后重新验证
    await formRef.value?.validate((_errors) => {
      if (_errors) {
        // 拼装错误信息
        const errorMessage = _errors.map((list) => {
          return list.map((item) => {
            return t(`system.header.${item.field}`) + item.message
          }).join('\n')
        }).join('\n')
        window.$notification.error({
          title: t('common.validateError'),
          content: errorMessage,
          duration: 4000
        })
      }
    })

    // 获取行数据，查看是否有 id 字段
    const row = modelValue.value[Number(index)]
    if (row.id && !props.api.update) {
      window.$message.error(t('error.updateUrlNotConfigured'))
      return
    } else if (!props.api.save) {
      window.$message.error(t('error.saveUrlNotConfigured'))
      return
    }
    const api = (row.id ? props.api.update : props.api.save) as string
    const form = props.beforeSubmit?.({ id: row.id, ...row })
    const { error, data } = await useRequest(api).post(form)
    if (!error.value) {
      window.$message.success(t('common.operationSuccess'))
      currentEditRow.value = null
    }
    if (data.value && typeof data.value === 'object') {
      modelValue.value[Number(index)] = data.value
    }
  } catch (e) {
    if (e instanceof Error) {
      window.$message.error(e.message)
    }
  }
}

// 行选中
function handleSelect(keys: (string | number)[]) {
  checkedRowKeys.value = keys as string[]
}

function init(rowData: RowData[]) {
  modelValue.value = rowData
}

defineExpose({
  init
})
</script>

<template>
  <div>
    <NForm ref="formRef" :model="modelValue[Number(currentEditRow)]">
      <NDataTable :columns="columns" :data="modelValue" :pagination="false" :single-line="false" striped size="small"
        single-column @update:checked-row-keys="handleSelect" :row-key="row => row.id" />
    </NForm>
  </div>
</template>

<style scoped>
.drag-handle {
  cursor: move;
}

:deep(.custom-feedback) {
  z-index: 1000;
  overflow: visible;
  position: absolute;
  margin-top: 30px;
  visibility: hidden;
}

:deep(.n-form-item.n-form-item--top-labelled) {
  grid-template-rows: unset;
  grid-template-columns: unset;
  overflow: visible;
}
</style>