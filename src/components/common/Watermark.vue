<script setup lang="ts">
import { useAuth } from '@/hooks';

interface Props {
  showWatermark: boolean
}
const props = defineProps<Props>()
const { userInfo } = useAuth()
const text = computed(() => userInfo.value?.username + ' ' + userInfo.value?.realName)

const needWatermark = computed(() => {
  return props.showWatermark && (userInfo.value?.username || userInfo.value?.realName)
})
</script>

<template>
  <n-watermark
    v-if="needWatermark"
    :content="text"
    cross
    fullscreen
    :font-size="16"
    :line-height="16"
    :width="384"
    :height="384"
    :x-offset="12"
    :y-offset="60"
    :rotate="-15"
  />
</template>
