<script setup lang="ts">
import { NDataTable, NInput, NInputNumber, NSelect, NSwitch, NButton, NForm, NFormItem, NRadioGroup, NDatePicker, NPopover } from 'naive-ui'
import type { RowData, TableColumn } from 'naive-ui/es/data-table/src/interface'
import type { MarFormProps } from './MarFormType'
import type { FormInst, FormItemRule } from 'naive-ui'
import { t } from '@/modules/i18n';
import type { Component, VNode } from 'vue';
import { isNumber } from 'radash'

const props = defineProps<
  Omit<MarFormProps, 'api'> & {
    rowActions?: (rowData: any, index: number | string) => VNode[]
  }
>()

const modelValue = defineModel<RowData[]>({
  required: true,
})
const checkedRowKeys = defineModel<string[]>('checkedRowKeys', {
  required: false,
})
const filter = ref('')

// 为每一行创建一个 form 实例
const formRef = ref<FormInst | null>(null)

// 生成校验规则
function generateRules(item: MarFormProps['formSchema'][0]): FormItemRule[] {
  if (!item.rules) return []
  const validations: FormItemRule[] = []

  // 获取触发器和类型
  function getTriggerAndType(type: string, item: MarFormProps['formSchema'][0]): { trigger: string[], type?: 'number' | 'array' } {
    if (type === 'input-number') {
      return { trigger: ['blur', 'input'], type: 'number' }
    }
    if (type === 'select') {
      if (item.options && item.options.length > 0 && typeof item.options[0].value === 'number') {
        return { trigger: ['blur', 'change'], type: 'number' }
      }
      return { trigger: ['blur', 'change'] }
    }
    if (type === 'select-multiple') {
      return { trigger: ['blur', 'change'], type: 'array' }
    }
    return { trigger: ['blur', 'input'] }
  }

  // 必填校验
  if (item.rules.required) {
    validations.push({
      required: true,
      message: t('common.required'),
      ...getTriggerAndType(item.type, item)
    })
  }

  // 正则校验
  if (item.rules.regex) {
    try {
      const pattern = typeof item.rules.regex === 'string'
        ? new RegExp(item.rules.regex)
        : item.rules.regex
      validations.push({
        validator: (rule: any, value: any) => {
          if (pattern) {
            if (!pattern.test(value)) {
              return new Error(t('common.formatError'))
            }
            return true
          }
        },
        ...getTriggerAndType(item.type, item)
      })
    }
    catch (e) {
    }
  }

  // 自定义校验
  if (item.rules?.validator) {
    validations.push({
      validator: (rule: any, value: any) => {
        const valid = item.rules!.validator!(value)
        if (!valid)
          return new Error(t('common.validateError'))
        return true
      },
      ...getTriggerAndType(item.type, item)
    })
  }

  return validations
}

// 实现过滤功能
const filteredData = computed(() => {
  if (!filter.value) return modelValue.value

  const searchText = filter.value.toLowerCase()
  return modelValue.value.filter((row) => {
    // 遍历所有列，检查每一列的值是否包含搜索文本
    return Object.values(row).some(value => {
      if (value === null || value === undefined) return false
      return String(value).includes(searchText)
    })
  })
})

function disabledHelper(item: MarFormProps['formSchema'][0]) {
  return props.readonly || (typeof item.disabled === 'function' ? item.disabled(modelValue) : item.disabled)
}

function formatInputValue(value: any, type: string) {
  if (type === 'input' && isNumber(value)) {
    return String(value)
  }
  return value
}

const columns = computed(() => {
  if (!Array.isArray(props.formSchema)) return []
  const baseColumns = props.formSchema.map(config => {
    return {
      title: config.remark ? () => {
        return h('div', { class: 'flex items-center gap-1' }, [
          h(NPopover, {
            trigger: 'hover',
          }, {
            trigger: () => h('div', { class: 'i-icon-park-outline-help text-gray-400 text-sm' }),
            default: () => h('div', config.remark ? t(config.remark) : '')
          }),
          h('span', t(config.label))
        ]);
      } : t(config.label),
      key: config.prop,
      minWidth: 140,
      render(row: RowData, index: number | string) {
        // 在多行编辑模式下，所有行始终处于编辑状态
        // 渲染对应的编辑组件
        const renderEditComponent = () => {
          switch (config.type) {
            case 'input':
              return h(NInput, {
                value: formatInputValue(row[config.prop], config.type),
                size: 'small',
                onUpdateValue(v) {
                  const newRow = { ...modelValue.value[index] }
                  newRow[config.prop] = v
                  modelValue.value = [
                    ...modelValue.value.slice(0, Number(index)),
                    newRow,
                    ...modelValue.value.slice(Number(index) + 1)
                  ]
                },
                disabled: disabledHelper(config)
              })
            case 'input-number':
              return h(NInputNumber, {
                value: formatInputValue(row[config.prop], config.type),
                size: 'small',
                onUpdateValue(v) {
                  const newRow = { ...modelValue.value[index] }
                  newRow[config.prop] = v
                  modelValue.value = [
                    ...modelValue.value.slice(0, Number(index)),
                    newRow,
                    ...modelValue.value.slice(Number(index) + 1)
                  ]
                },
                disabled: disabledHelper(config)
              })
            case 'select':
              return h(NSelect, {
                value: formatInputValue(row[config.prop], config.type),
                size: 'small',
                clearable: true,
                filterable: true,
                options: config.options || [],
                consistentMenuWidth: false,
                onUpdateValue(v) {
                  const newRow = { ...modelValue.value[index] }
                  newRow[config.prop] = v
                  modelValue.value = [
                    ...modelValue.value.slice(0, Number(index)),
                    newRow,
                    ...modelValue.value.slice(Number(index) + 1)
                  ]
                },
                disabled: disabledHelper(config)
              })
            case 'radio':
              return h(NRadioGroup, {
                value: formatInputValue(row[config.prop], config.type),
                size: 'small',
                options: config.options || [],
                onUpdateValue(v) {
                  const newRow = { ...modelValue.value[index] }
                  newRow[config.prop] = v
                  modelValue.value = [
                    ...modelValue.value.slice(0, Number(index)),
                    newRow,
                    ...modelValue.value.slice(Number(index) + 1)
                  ]
                },
                disabled: disabledHelper(config)
              })
            case 'date':
              return h(NDatePicker, {
                formattedValue: formatInputValue(row[config.prop], config.type),
                size: 'small',
                type: 'date',
                format: 'yyyy-MM-dd',
                onUpdateFormattedValue(v) {
                  const newRow = { ...modelValue.value[index] }
                  newRow[config.prop] = v
                  modelValue.value = [
                    ...modelValue.value.slice(0, Number(index)),
                    newRow,
                    ...modelValue.value.slice(Number(index) + 1)
                  ]
                },
                disabled: disabledHelper(config)
              })
            case 'date-time':
              return h(NDatePicker, {
                formattedValue: formatInputValue(row[config.prop], config.type),
                size: 'small',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss',
                onUpdateFormattedValue(v) {
                  const newRow = { ...modelValue.value[index] }
                  newRow[config.prop] = v
                  modelValue.value = [
                    ...modelValue.value.slice(0, Number(index)),
                    newRow,
                    ...modelValue.value.slice(Number(index) + 1)
                  ]
                },
                disabled: disabledHelper(config)
              })

            default:
              return row[config.prop]
          }
        }

        // 使用 FormItem 包装编辑组件
        return h(NFormItem, {
          rule: generateRules(config),
          path: `${index}.${config.prop}`,
          feedbackClass: 'custom-feedback'
        }, {
          default: () => renderEditComponent()
        })
      }
    }
  })
  // 操作列只保留删除按钮
  const actionColumn = {
    title: t('common.action'),
    key: 'actions',
    width: 100,
    fixed: 'left',
    render(row: RowData, index: number | string) {
      return h('div', { class: 'relative flex items-center' }, [
        ...(props.rowActions?.(row, index) || []),
        h(NButton, {
          size: 'small',
          type: 'primary',
          text: true,
          class: 'mx-1',
          disabled: props.readonly, // 按钮本身也禁用
          onClick: () => handleDelete(index)
        }, { default: () => t('common.delete') }),
        // 蒙版禁用操作
        props.readonly ? h('div', { class: 'absolute inset-0 bg-white opacity-50 z-10 cursor-not-allowed' }) : null
      ])
    }
  }

  return [actionColumn, ...baseColumns] as TableColumn[]
})

// 删除行方法
function handleDelete(index: number | string) {
  // 使用数组解构创建新数组
  modelValue.value = [
    ...modelValue.value.slice(0, Number(index)),
    ...modelValue.value.slice(Number(index) + 1)
  ]
}

// 行选中
function handleSelect(keys: (string | number)[]) {
  checkedRowKeys.value = keys as string[]
}

// 验证所有表单项
async function validate() {
  try {
    await formRef.value?.validate((_errors) => {
      if (_errors) {
        // 拼装错误信息
        const errorMessage = _errors.map((list) => {
          return list.map((item) => {
            // 从字段路径中提取实际的字段名（去掉行号前缀）
            const fieldParts = item.field?.split('.')
            if (fieldParts && fieldParts.length > 1) {
              const actualField = fieldParts[1]
              return t('error.no') + (Number(fieldParts[0]) + 1) + t('error.row') + t(`system.header.${actualField}`) + item.message
            }
            return t(`system.header.${item.field}`) + item.message
          }).join('\n')
        }).join('\n')
        window.$notification.error({
          title: t('common.validateError'),
          content: errorMessage,
          duration: 4000
        })
        throw new Error(t('common.validateError'))
      }
    })
    return true
  } catch (errors) {
    return false
  }
}

// 提交数据方法，供父组件调用
async function submit() {
  const isValid = await validate()
  if (!isValid) {
    return null
  }

  // 返回处理后的所有行数据
  return modelValue.value.map(row => {
    if (props.beforeSubmit) {
      return props.beforeSubmit(row)
    }
    return row
  })
}

const childNewValueTemplate = computed(() => {
  return props.formSchema.reduce((acc, item) => {
    if (item.default) {
      acc[item.prop] = !isNaN(Number(item.default)) ? Number(item.default) : item.default
    }
    else if (item.type === 'input-number') {
      acc[item.prop] = 0
    }
    else if (item.type === 'date') {
      // get yyyy-MM-dd
      acc[item.prop] = new Date().toISOString().split('T')[0]
    }
    else if (item.type === 'date-time') {
      // get yyyy-MM-dd HH:mm:ss
      acc[item.prop] = new Date().toISOString().split('T')[0] + ' ' + new Date().toISOString().split('T')[1].split('.')[0]
    }
    else {
      acc[item.prop] = ''
    }
    return acc
  }, {} as Record<string, any>)
})
function handleNewLine() {
  modelValue.value = [...modelValue.value, childNewValueTemplate.value]
}

defineExpose({
  submit,
  validate
})
</script>

<template>
  <div>
    <div class="flex justify-between items-center">
      <div class="flex items-center">
        <NButton size="small" type="primary" @click="handleNewLine" :disabled="readonly">{{ $t('common.save') }}</NButton>
        <slot name="sub-actions" />
      </div>
      <NInput class="mb-2 max-w-50" v-model:value="filter" :placeholder="$t('tip.filter')" />
    </div>
    <NForm ref="formRef" :model="modelValue">
      <NDataTable :columns="columns" :data="filteredData" :pagination="false" :single-line="false" striped size="small"
        single-column @update:checked-row-keys="handleSelect" :row-key="row => row.id" class="dark:bg-transparent bg-#fff"
        />
    </NForm>
  </div>
</template>

<style scoped>
.drag-handle {
  cursor: move;
}

:deep(.custom-feedback) {
  z-index: 1000;
  overflow: visible;
  position: absolute;
  margin-top: 30px;
  visibility: hidden;
}

:deep(.n-form-item.n-form-item--top-labelled) {
  grid-template-rows: unset;
  grid-template-columns: unset;
  overflow: visible;
}
</style>
