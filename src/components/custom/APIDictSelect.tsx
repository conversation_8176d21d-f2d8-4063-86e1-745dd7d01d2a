import { useRequest } from '@/hooks/useRequest'
import { NSelect } from 'naive-ui'
import { defineComponent, ref, PropType } from 'vue'

interface Props {
  url: string
  afterRequest?: (data: any) => any
  modelValue: string | number | object
  NaiveSelectProps?: any
}

const APIDictSelect = defineComponent({
  name: 'APIDictSelect',
  props: {
    url: {
      type: String,
      required: true
    },
    modelValue: {
      type: [String, Number, Object],
      required: true
    },
    NaiveSelectProps: {
      type: Object,
      default: () => ({})
    },
    afterRequest: {
      type: Function as PropType<(data: any) => any>,
      default: undefined
    }
  },
  emits: ['update:modelValue'],
  setup(props: Props, { emit }) {
    const options = ref<any>([])
    useRequest(props.url).then((res) => {
      let result = res.data.value
      if (props.afterRequest) {
        result = props.afterRequest(result)
      }
      options.value = result
    })

    const handleUpdateValue = (value) => {
      emit('update:modelValue', value)
    }

    return () => (
      <NSelect
        value={props.modelValue}
        options={options.value}
        onUpdateValue={handleUpdateValue}
        onBlur={(e) => {
          // 确保 blur 事件能正确传播，用于表单校验
          props.NaiveSelectProps?.onBlur?.(e)
        }}
        onFocus={(e) => {
          // 确保 focus 事件能正确传播
          props.NaiveSelectProps?.onFocus?.(e)
        }}
        {...props.NaiveSelectProps}
      />
    )
  }
})

export default APIDictSelect