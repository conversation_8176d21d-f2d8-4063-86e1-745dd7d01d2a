<script setup lang="ts">
import { NDataTable, NSwitch } from 'naive-ui'
import type { DataTableColumns } from 'naive-ui'
import type { RowData } from 'naive-ui/es/data-table/src/interface'
import { useTableDrag } from '@/hooks'
import { t } from '@/modules/i18n'

function createColumns({
  updateRow,
}: {
  updateRow: (rowData: RowData) => void
}): DataTableColumns<RowData> {
  return [
    {
      title: '序号',
      key: 'sort',
      width: 100,
      render(row) {
        return row.sort + 1
      },
      className: 'drag-handle',
    },
    {
      title: '拖动',
      key: 'sort',
      width: 100,
      render() {
        return h('div', {
          class: ['drag-handle', 'text-black', 'dark:text-white', 'cursor-move', 'w-1.2rem', 'h-1.2rem', 'i-icon-park-outline-application-menu'],
        })
      },
    },
    {
      title: '列名',
      key: 'columnLabel',
      width: 100,
      render(row) {
        return t(row.columnLabel)
      }
    },
    {
      title: '显示/隐藏',
      key: 'isList',
      width: 100,
      render(row) {
        return h(NSwitch, {
          value: row.isList,
          size: 'small',
          onUpdateValue(v) {
            row.isList = v
            updateRow(row)
          }
        })
      }
    },
    {
      title: '快速查询',
      key: 'isQuery',
      width: 100,
      render(row) {
        return h(NSwitch, {
          value: row.isQuery,
          size: 'small',
          onUpdateValue(v) {
            row.isQuery = v
            updateRow(row)
          }
        })
      }
    },
    {
      title: '允许排序',
      key: 'isSortable',
      width: 100,
      render(row) {
        return h(NSwitch, {
          value: row.isSortable,
          size: 'small',
          onUpdateValue(v) {
            row.isSortable = v
            updateRow(row)
          }
        })
      }
    },
  ]
}
const modelValue = defineModel<RowData[]>({
  required: true
})

function handleUpdateRow(row: RowData) {
  const index = modelValue.value.findIndex(item => item.id === row.id)

  if (index > -1) {
    const newData = [...modelValue.value]
    newData[index] = row
    modelValue.value = newData
  }
}

const columns = createColumns({
  updateRow: handleUpdateRow,
})

const tableRef = ref<InstanceType<typeof NDataTable>>()
useTableDrag({
  tableRef,
  data: modelValue,
})
watch(modelValue, () => {
  modelValue.value.forEach((item, index) => {
    item.sort = index
  })
})
</script>

<template>
  <NDataTable ref="tableRef" :columns="columns" :data="modelValue" :pagination="false" :single-line="false" striped
    size="small" single-column />
</template>

<style scoped>
.drag-handle {
  cursor: move;
}
</style>
