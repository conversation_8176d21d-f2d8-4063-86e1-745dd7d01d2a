import { computed, defineComponent, ref } from 'vue'
import type { MarFormProps } from '@/components'
import MarDialog from '@/components/common/MarDialog.vue'
import MarForm from '@/components/common/MarForm.vue'

export interface FormConfig {
  key: string
  config: MarFormProps
}

export default defineComponent({
  name: 'FormDialogV2',
  props: {
    label: String,
    formConfigs: {
      type: Array as PropType<FormConfig[]>,
      required: true,
    },
  },
  setup(props, ctx) {
    const currentConfig = ref<string>('')
    const dialogRef = ref<InstanceType<typeof MarDialog>>()
    const formRef = ref<InstanceType<typeof MarForm>>()

    const formBind = computed(() => {
      if (currentConfig.value) {
        const config = props.formConfigs.find(item => item.key === currentConfig.value)?.config
        return {
          ...config,
          onCancel() {
            dialogRef.value?.close()
            config?.onCancel?.()
          },
        }
      }
      return {}
    })

    const open = async (key: string, args?: any, options?: any) => {
      currentConfig.value = key
      dialogRef.value?.open()
      while (!formRef.value) {
        await nextTick()
      }
      formRef.value?.init(args, options)
    }

    const formModel = computed(() => formRef.value?.formModel)
    const updateFormModel = computed(() => formRef.value?.updateFormModel)

    ctx.expose({
      open,
      formModel,
      updateFormModel,
    })

    return {
      dialogRef,
      formBind,
      formRef,
      open,
      formModel,
      updateFormModel,
    }
  },
  render() {
    return (
      <MarDialog
        label={this.label}
        ref={(el: any) => this.dialogRef = el}
      >
        <MarForm {...this.formBind as MarFormProps} ref={(el: any) => this.formRef = el} />
      </MarDialog>
    )
  },
})
