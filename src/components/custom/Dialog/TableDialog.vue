<script setup lang="ts">
import { useBoolean } from '@/hooks'
import type { UseTableReturn } from '@/hooks/useTableType'

interface Props {
  title: string
  tableConfig: UseTableReturn
}

defineProps<Props>()

// 弹窗控制
const { bool: modalVisible, setTrue: showModal } = useBoolean(false)

// 打开弹窗
function open() {
  showModal()
}

defineExpose({
  open,
})
</script>

<template>
  <n-modal v-model:show="modalVisible" :mask-closable="false" preset="card" :title="title" class="w-800px" :segmented="{
    content: true,
    action: true,
  }">
    <mar-wrapper :model-value="tableConfig.state" />
  </n-modal>
</template>
