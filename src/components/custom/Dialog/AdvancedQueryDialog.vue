<script setup lang="ts">
import { t } from '@/modules/i18n'
import { CompareEnum, LinkEnum } from '@/constants/query'

interface Props {
  fields: Array<{
    type: 'input' | 'select' | string
    prop: string
    label: string
    options?: Array<{ label: string, value: any }>
  }>
}

const props = defineProps<Props>()
const processedFields = computed(() => {
  return props.fields.map(field => ({
    label: field.label,
    value: field.prop,
    options: field.options,
  }))
})
const emit = defineEmits(['search', 'reset'])

// 查询条件组
const queryGroups = ref<{
  link: LinkEnum
  data: Array<{
    colkey: string
    op: CompareEnum
    opval: any
    link: LinkEnum
  }>
}[]>([
  {
    link: LinkEnum.AND,
    data: [{ colkey: '', op: CompareEnum.EQUAL, opval: '', link: LinkEnum.AND }],
  },
])

// 弹窗控制
const modalVisible = ref(false)

// 修改为临时存储的查询条件
const tempQueryGroups = ref<{
  link: LinkEnum
  data: Array<{
    colkey: string
    op: CompareEnum
    opval: any
    link: LinkEnum
  }>
}[]>([])
watch(tempQueryGroups, (newVal) => {
  console.log(newVal)
}, { deep: true })

// 添加一个深拷贝的工具函数
function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj))
}

// 添加查询条件组
function addQueryGroup() {
  tempQueryGroups.value.push({
    link: LinkEnum.AND,
    data: [{ colkey: '', op: CompareEnum.EQUAL, opval: '', link: LinkEnum.AND }],
  })
}

// 添加查询条件
function addQueryItem(groupIndex: number) {
  tempQueryGroups.value[groupIndex].data.push({
    colkey: '',
    op: CompareEnum.EQUAL,
    opval: '',
    link: LinkEnum.AND,
  })
}

// 删除查询条件组
function removeQueryGroup(groupIndex: number) {
  tempQueryGroups.value.splice(groupIndex, 1)
}

// 删除查询条件
function removeQueryItem(groupIndex: number, itemIndex: number) {
  tempQueryGroups.value[groupIndex].data.splice(itemIndex, 1)
}

function beforeSearch() {
  const form = [...tempQueryGroups.value]
  // 在搜索之前，对数据进行预处理
  // 过滤掉 data 为空的查询条件组
  form.forEach(group => {
    group.data = group.data.filter(item => item.colkey !== '')
  })
  // 如果查询条件是 IN 或 NOT IN，则将 opval join 成字符串
  form.forEach(group => {
    group.data.forEach(item => {
      if (item.op === CompareEnum.IN || item.op === CompareEnum.NOT_IN) {
        item.opval = item.opval.join(',')
      }
    })
  })
  return form
}

// 当弹窗打开时，复制当前的查询条件
function open() {
  // 如果 queryGroups 为空，使用默认值
  const defaultGroup = {
    link: LinkEnum.AND,
    data: [{ colkey: '', op: CompareEnum.EQUAL, opval: '', link: LinkEnum.AND }],
  }
  
  const clonedData = deepClone(queryGroups.value.length ? queryGroups.value : [defaultGroup])
  
  // 处理 IN 和 NOT IN 的值，将字符串转换回数组
  clonedData.forEach(group => {
    group.data.forEach(item => {
      if ((item.op === CompareEnum.IN || item.op === CompareEnum.NOT_IN) && typeof item.opval === 'string') {
        item.opval = item.opval.split(',').filter(Boolean).map(Number)
      }
    })
  })
  tempQueryGroups.value = clonedData
  modalVisible.value = true
}

// 搜索时，将临时数据同步到实际数据
function handleSearch() {
  queryGroups.value = deepClone(tempQueryGroups.value)
  emit('search', beforeSearch())
  modalVisible.value = false
}

// 重置时也要处理临时数据
function handleReset() {
  const defaultGroup = {
    link: LinkEnum.AND,
    data: [{ colkey: '', op: CompareEnum.EQUAL, opval: '', link: LinkEnum.AND }],
  }
  tempQueryGroups.value = [defaultGroup]
  queryGroups.value = deepClone([defaultGroup])
  emit('reset')
}

defineExpose({
  open,
})
</script>

<template>
  <n-modal v-model:show="modalVisible" preset="card" :title="t('components.advancedQueryDialog.title')" class="w-900px">
    <n-space vertical>
      <!-- 查询条件组 -->
      <div v-for="(group, groupIndex) in tempQueryGroups" :key="groupIndex" class="query-group">
        <n-card :title="`组 ${groupIndex + 1}`" class="mb-4">
          <!-- 组间逻辑运算符 -->
          <template #header-extra>
            <n-select v-if="groupIndex > 0" v-model:value="group.link" :options="[
              { label: t('common.link.and'), value: LinkEnum.AND },
              { label: t('common.link.or'), value: LinkEnum.OR },
            ]" class="w-260px" />
            <n-button v-if="tempQueryGroups.length > 1" type="error" circle class="ml-2"
              @click="removeQueryGroup(groupIndex)">
              <template #icon>
                <div class="i-icon-park-outline-delete h-1.2rem w-1.2rem" />
              </template>
            </n-button>
          </template>

          <!-- 查询条件 -->
          <div v-for="(item, itemIndex) in group.data" :key="itemIndex" class="query-item">
            <!-- 条件内逻辑运算符 -->
            <n-select v-if="itemIndex > 0" v-model:value="item.link" :options="[
              { label: t('common.link.and'), value: LinkEnum.AND },
              { label: t('common.link.or'), value: LinkEnum.OR },
            ]" class="w-150px" />
            <div v-else class="w-150px" />

            <!-- 字段选择 -->
            <n-select v-model:value="item.colkey" :options="processedFields" class="w-200px"
              :placeholder="t('components.advancedQueryDialog.pleaseSelectField')" />

            <!-- 比较运算符 -->
            <n-select v-model:value="item.op" :options="[
              { label: t('common.compare.equal'), value: CompareEnum.EQUAL },
              { label: t('common.compare.notEqual'), value: CompareEnum.NOT_EQUAL },
              { label: t('common.compare.greater'), value: CompareEnum.GREATER },
              { label: t('common.compare.greaterEqual'), value: CompareEnum.GREATER_EQUAL },
              { label: t('common.compare.less'), value: CompareEnum.LESS },
              { label: t('common.compare.lessEqual'), value: CompareEnum.LESS_EQUAL },
              { label: t('common.compare.like'), value: CompareEnum.LIKE },
              { label: t('common.compare.in'), value: CompareEnum.IN },
              { label: t('common.compare.notIn'), value: CompareEnum.NOT_IN },
            ]" class="w-200px" :placeholder="t('components.advancedQueryDialog.pleaseSelectOperator')" />

            <!-- 查询值输入 -->
            <n-input v-if="props.fields.find(field => field.prop === item.colkey)?.type === 'input'"
              v-model:value="item.opval" class="w-200px max-w-200px" clearable
              :placeholder="t('components.advancedQueryDialog.pleaseInputValue')" />
            <n-select v-else v-model:value="item.opval"
              :options="props.fields.find(field => field.prop === item.colkey)?.options" class="w-200px min-w-200px"
              :placeholder="t('components.advancedQueryDialog.pleaseSelectValue')" clearable
              :multiple="item.op === CompareEnum.IN || item.op === CompareEnum.NOT_IN" max-tag-count="responsive" />

            <!-- 操作按钮 -->
            <div class="flex items-center gap-1">
              <n-button v-if="group.data.length > 1" circle class="bg-gray-5 text-white w-20px h-20px"
                @click="removeQueryItem(groupIndex, itemIndex)">
                <template #icon>
                  <div class="i-icon-park-outline-minus h-1.2rem w-1.2rem" />
                </template>
              </n-button>
              <n-button type="warning" circle class="w-20px h-20px" @click="addQueryItem(groupIndex)">
                <template #icon>
                  <div class="i-icon-park-outline-plus h-1.2rem w-1.2rem" />
                </template>
              </n-button>
            </div>
          </div>
        </n-card>
      </div>

      <!-- 底部按钮 -->
      <n-space justify="center">
        <n-button @click="addQueryGroup">
          {{ t('components.advancedQueryDialog.addQueryGroup') }}
        </n-button>
        <n-button type="primary" @click="handleSearch">
          {{ t('components.advancedQueryDialog.handleSearch') }}
        </n-button>
        <n-button @click="handleReset">
          {{ t('components.advancedQueryDialog.handleReset') }}
        </n-button>
      </n-space>
    </n-space>
  </n-modal>
</template>

<style scoped>
.query-group {
  width: 100%;
}

.query-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.query-item:last-child {
  margin-bottom: 0;
}
</style>
