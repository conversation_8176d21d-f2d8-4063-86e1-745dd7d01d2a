<script setup lang="ts">
import type { MarFormProps } from '@/components/common/MarFormType'
import { MarDialog, MarForm } from '@/components'

const props = defineProps<MarFormProps & {
  label?: string
}>()

const dialogRef = ref<InstanceType<typeof MarDialog>>()
const formRef = ref<InstanceType<typeof MarForm>>()

const formBind = computed(() => ({
  ...props,
  onCancel() {
    props.onCancel?.()
    dialogRef.value?.close()
  },
}))

async function openCb(args?: Record<string, any>, opt?: {
  noFetch?: boolean
  overrideFetchData?: (args?: Record<string, any>) => Promise<any>
}) {
  while (!formRef.value)
    await nextTick()
  formRef.value?.init(args, opt)
}
defineExpose({
  open: async (args?: Record<string, any>, opt?: {
    noFetch?: boolean
    overrideFetchData?: (args?: Record<string, any>) => Promise<any>
  }) => {
    dialogRef.value?.open(() => openCb(args, opt))
  },
})
</script>

<template>
  <mar-dialog ref="dialogRef" :label="props.label">
    <mar-form v-bind="formBind" ref="formRef" />
  </mar-dialog>
</template>
