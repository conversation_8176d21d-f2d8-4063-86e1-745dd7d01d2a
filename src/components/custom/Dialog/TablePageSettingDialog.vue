<script setup lang="ts">
import { RowData } from '@/types';
import TablePageSettingEdit from './TablePageSettingEdit.vue';
import { useApp, useRequest } from '@/hooks';
import { refreshTableBff } from '@/bff/table';
import { t } from '@/modules/i18n';

// 将 Props 改为 export
export interface TablePageSettingDialogProps {
  dynCode: string
  bff: {
    raw: {
      id: string
      fieldList: RowData[]
    }
  }
}

const meta = useRoute().meta

const props = defineProps<TablePageSettingDialogProps>()

const fieldList = ref<RowData[]>([])
const modalVisible = ref(false)

function open() {
  fieldList.value = JSON.parse(JSON.stringify(props.bff.raw.fieldList))
  modalVisible.value = true
}

const { reloadPage } = useApp()
async function handleSubmit() {
  if (fieldList.value.length !== props.bff.raw.fieldList.length) {
    return
  }
  const { error } = await useRequest(`/devtools/dynTable/saveUserMenuInfo`)
    .post({
      fieldList: fieldList.value,
      menuId: meta.menuId as string,
      dynCode: props.dynCode,
    })
  if (!error.value) {
    window.$message.success(t('common.operationSuccess'))
    if (props.dynCode) {
      refreshTableBff(props.dynCode)
    }
    reloadPage()
    modalVisible.value = false
  }
}

defineExpose({
  open,
})
</script>

<template>
  <n-modal v-model:show="modalVisible" preset="card" :title="$t('components.toolbar.config')" class="w-700px">
    <div class="flex flex-col gap-4 max-h-400px overflow-y-scroll no-scrollbar">
      <TablePageSettingEdit v-model="fieldList" />
    </div>
    <template #footer>
      <div class="flex justify-end gap-2">
        <n-button @click="modalVisible = false">
          {{ $t('common.cancel') }}
        </n-button>
        <n-button type="primary" @click="handleSubmit">
          {{ $t('common.saveLabel') }}
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<style scoped>
/* 隐藏滚动条但保留滚动功能 */
.no-scrollbar {
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
</style>
