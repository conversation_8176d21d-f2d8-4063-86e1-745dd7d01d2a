<script setup lang="ts">
import { useBoolean, useRequest } from '@/hooks'
import { t } from '@/modules/i18n'
import MarUpload from '@/components/common/MarUpload.vue'

interface Props {
  // 弹窗标题
  title?: string
  // 文件大小限制(MB)
  sizeLimit?: number
  // 文件类型
  type?: string
  // 上传接口
  uploadApi: string
  // 下载模板接口
  templateApi?: string
  // 上传成功后回调
  afterUpload?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  title: '上传文件',
  sizeLimit: 10,
  numLimit: 1,
  type: '',
})

const emit = defineEmits<{
  success: [file: File]
  close: []
}>()

// 弹窗显示控制
const { bool: modalVisible, setTrue: showModal, setFalse: hiddenModal } = useBoolean(false)
const { bool: submitLoading, setTrue: startLoading, setFalse: endLoading } = useBoolean(false)

// 上传的文件
const uploadedFile = ref<File | null>(null)

// 提交
async function handleSubmit() {
  if (!uploadedFile.value) {
    window.$message.warning(t('common.pleaseUploadFileFirst'))
    return
  }
  startLoading()
  try {
    emit('success', uploadedFile.value)
    const formData = new FormData()
    formData.append('file', uploadedFile.value)
    const { error } = await useRequest(props.uploadApi, {
      method: 'POST',
      body: formData,
    }, {
      beforeFetch(ctx) {
        if (ctx.options.headers) {
          delete ctx.options.headers['Content-Type']
        }
      },
    })
    if (error.value) {
      window.$message.error(t('common.operationFailed'))
    }
    else {
      window.$message.success(t('common.operationSuccess'))
      props.afterUpload?.()
      closeModal()
    }
  }
  finally {
    endLoading()
  }
}

// 关闭弹窗
function closeModal() {
  hiddenModal()
  uploadedFile.value = null
  emit('close')
}

// 打开弹窗
function open() {
  showModal()
}

defineExpose({
  open,
})
</script>

<template>
  <n-modal v-model:show="modalVisible" :mask-closable="false" preset="card" :title="title" class="w-500px" :segmented="{
    content: true,
    action: true,
  }">
    <n-space vertical class="w-full">
      <MarUpload v-model="uploadedFile" :template-api="templateApi" :multiple="false" />
    </n-space>

    <template #action>
      <n-space justify="end">
        <n-button @click="closeModal">
          {{ t('common.cancel') }}
        </n-button>
        <n-button type="primary" :loading="submitLoading" :disabled="!uploadedFile" @click="handleSubmit">
          {{ t('common.confirm') }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>
