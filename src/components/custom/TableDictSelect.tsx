import { useTableDict } from '@/hooks/useTableDict'
import { NSelect } from 'naive-ui'
import { defineComponent, ref } from 'vue'

interface Props {
  tableName: string
  modelValue: string | number | object
  NaiveSelectProps?: any
}

const TableDictSelect = defineComponent((props: Props, { emit }) => {
  const options = ref([])
  useTableDict(props.tableName).then((res) => {
    options.value = res
  })
  
  const handleUpdateValue = (value) => {
    emit('update:modelValue', value)
  }
  
  return () => (
    <NSelect 
      value={props.modelValue} 
      options={options.value} 
      onUpdateValue={handleUpdateValue}
      {...props.NaiveSelectProps} 
    />
  )
}, {
  name: 'TableDictSelect',
  props: {
    tableName: {
      type: String,
      required: true
    },
    modelValue: {
      type: [String, Number, Object],
      required: true
    },
    NaiveSelectProps: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
})

export default TableDictSelect