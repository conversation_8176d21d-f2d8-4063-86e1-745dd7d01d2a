<script setup lang="ts">
import type { State } from '@/hooks/useTable'
import { t } from '@/modules/i18n'
import AdvancedQueryDialog from '@/components/custom/Dialog/AdvancedQueryDialog.vue'
import type { QueryForm } from '@/hooks'

const emit = defineEmits(['superSearch', 'superReset'])
const trigger = inject<State['trigger']>('trigger') as State['trigger']
const toolbar = inject<State['toolbar']>('toolbar') as State['toolbar']
const superQuery = inject<State['superQuery']>('superQuery') as State['superQuery']
const groupFieldList = inject<State['groupFieldList']>('groupFieldList') as State['groupFieldList']
const sortStates = inject<State['sortStates']>('sortStates') as State['sortStates']

function handleExport() {
  window.$dialog.success({
    title: t('components.toolbar.export'),
    content: t('components.toolbar.exportConfirm'),
    positiveText: t('common.confirm'),
    negativeText: t('common.cancel'),
    onPositiveClick: () => {
      trigger('exportTable')
    },
  })
}
const groupSearch = ref<{
  field: string
  op: 'query' | 'sum' | 'max' | 'min' | 'avg'
  sort?: 'descend' | 'ascend'
}[]>([
  {
    field: '',
    op: 'query',
    sort: 'ascend'
  }
])

// 将QueryPatternItem数组转换为NSelect可接受的选项格式
const fieldOptions = computed(() => {
  if (!superQuery.pattern || !Array.isArray(superQuery.pattern)) {
    return []
  }
  return superQuery.pattern.map(item => ({
    label: item.label,
    value: item.prop
  }))
})

// 检查字段和操作是否可用
function isFieldOpAvailable(field: string, op: string, currentIndex: number) {
  if (!field) return true

  // 如果当前字段已经选择了query操作，则不能再添加新条目
  const hasQueryOp = groupSearch.value.some((item, index) =>
    index !== currentIndex && item.field === field && item.op === 'query'
  )

  if (hasQueryOp) return false

  // 检查当前字段是否已经使用了相同的操作
  const hasSameFieldOp = groupSearch.value.some((item, index) =>
    index !== currentIndex && item.field === field && item.op === op
  )

  return !hasSameFieldOp
}

function handleGroupSearchAdd() {
  groupSearch.value.push({
    field: '',
    op: 'query'
  })
}

function handleGroupSearchRemove(index: number) {
  if (groupSearch.value.length > 1) {
    groupSearch.value.splice(index, 1)
  }
}

function handleGroupSearch() {
  if (groupSearch.value.length === 1 && groupSearch.value[0].field === '') {
    sortStates.length = 0
    groupFieldList.form = []
    return
  }
  let vali = true
  groupSearch.value.forEach(item => {
    if (item.op === 'query' && !item.sort)
      vali = false
  })
  if (!vali) {
    window.$message.error(t('tip.queryNeedSort'))
    return
  }
  groupFieldList.form = groupSearch.value
    .filter(item => item.field !== '')
    .map(item => ({
      field: item.field,
      op: item.op,
    }))
  sortStates.length = 0
  groupSearch.value.forEach(item => {
    if (item.field.length > 0 && item.op === 'query') {
      sortStates.push({
        columnKey: item.field,
        order: item.sort || 'ascend',
      })
    }
  })
}

function handleGroupSearchClear() {
  groupSearch.value = [{
    field: '',
    op: 'query',
    sort: 'ascend'
  }]
  handleGroupSearch()
}

const advancedQueryDialogRef = ref<InstanceType<typeof AdvancedQueryDialog> | null>(null)
function handleAdvancedQuery() {
  advancedQueryDialogRef.value?.open()
}
function handleTablePageConfig() {
  trigger('config')
}
function handleSuperSearch(form: QueryForm) {
  emit('superSearch', form)
}

function handleSuperReset() {
  emit('superReset')
}
</script>

<template>
  <div class="flex gap-4 mb-4">
    <AdvancedQueryDialog ref="advancedQueryDialogRef" :fields="superQuery.pattern" @search="handleSuperSearch"
      @reset="handleSuperReset" />
    <NButton v-if="toolbar.includes('save')" type="primary" size="small" @click="trigger('new')">
      <template #icon>
        <div class="i-icon-park-outline-add-one h-1.2rem w-1.2rem" />
      </template>
      {{ $t('components.toolbar.new') }}
    </NButton>
    <NButton v-if="toolbar.includes('deleteMul')" type="warning" size="small" @click="trigger('deleteMul')">
      <template #icon>
        <div class="i-icon-park-outline-delete-one h-1.2rem w-1.2rem" />
      </template>
      {{ $t('common.delete') }}
    </NButton>
    <NButton v-if="toolbar.includes('importTable')" strong secondary size="small" @click="trigger('importTable')">
      <template #icon>
        <div class="i-icon-park-outline-upload h-1.2rem w-1.2rem" />
      </template>
      {{ $t('components.toolbar.import') }}
    </NButton>
    <NButton v-if="toolbar.includes('exportTable')" strong secondary size="small" @click="handleExport">
      <template #icon>
        <div class="i-icon-park-outline-download h-1.2rem w-1.2rem" />
      </template>
      {{ $t('components.toolbar.export') }}
    </NButton>
    <slot name="left" />
    <div class="flex gap-4 ml-auto">
      <NPopover trigger="click" v-if="toolbar.includes('groupSearch')">
        <template #trigger>
          <NButton size="small" strong type="success">
            <template #icon>
              <div class="i-icon-park-outline-api-app h-1.2rem w-1.2rem" />
            </template>
            {{ $t('components.toolbar.groupSearch') }}
          </NButton>
        </template>
        <div class="p-2 w-auto max-h-40 overflow-y-auto">
          <!-- group search main -->
          <div v-for="(item, index) in groupSearch" :key="index" class="flex items-center gap-2 mb-2">
            <NSelect v-model:value="item.field" :options="fieldOptions"
              :placeholder="$t('components.advancedQueryDialog.pleaseSelectField')" size="small" class="min-w-40"
              filterable />
            <NSelect v-model:value="item.op" :options="[
              { label: $t('common.query'), value: 'query' },
              { label: $t('common.sum'), value: 'sum' },
              { label: $t('common.max'), value: 'max' },
              { label: $t('common.min'), value: 'min' },
              { label: $t('common.avg'), value: 'avg' }
            ]" size="small" class="min-w-24"
              :disabled="!item.field || !isFieldOpAvailable(item.field, item.op, index)" />
            <NSelect v-if="item.op === 'query'" v-model:value="item.sort" :options="[
              { label: $t('common.descend'), value: 'descend' },
              { label: $t('common.ascend'), value: 'ascend' }
            ]" size="small" class="min-w-24" />
            <div class="flex">
              <NButton type="primary" text @click="handleGroupSearchAdd" class="mr-1">
                <template #icon>
                  <div class="i-icon-park-outline-plus h-1.2rem w-1.2rem" />
                </template>
              </NButton>
              <NButton type="error" text @click="handleGroupSearchRemove(index)" :disabled="groupSearch.length <= 1">
                <template #icon>
                  <div class="i-icon-park-outline-minus h-1.2rem w-1.2rem" />
                </template>
              </NButton>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="flex gap-2 justify-end">
            <NButton size="medium" strong text type="success" @click="handleGroupSearch">
              {{ $t('common.confirm') }}
            </NButton>
            <NButton size="medium" strong text type="warning" @click="handleGroupSearchClear">
              {{ $t('common.clear') }}
            </NButton>
          </div>
        </template>
      </NPopover>
      <NButton v-if="toolbar.includes('advancedQuery')" type="info" size="small" @click="handleAdvancedQuery">
        <template #icon>
          <div class="i-icon-park-outline-find h-1.2rem w-1.2rem" />
        </template>
        {{ $t('components.toolbar.advancedQuery') }}
      </NButton>
      <NButton v-if="toolbar.includes('config')" size="small" strong secondary @click="handleTablePageConfig">
        <template #icon>
          <div class="i-icon-park-outline-setting-two h-1.2rem w-1.2rem" />
        </template>
        {{ $t('components.toolbar.config') }}
      </NButton>
      <slot />
      <slot name="right" />
    </div>
  </div>
</template>