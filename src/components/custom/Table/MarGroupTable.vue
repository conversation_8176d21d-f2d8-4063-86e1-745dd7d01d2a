<script setup lang="ts">
import { NDataTable } from 'naive-ui'
import type { TableColumn } from 'naive-ui/es/data-table/src/interface'
import { useLayout } from '@/hooks/useLayout'
import type { State } from '@/hooks/useTable'
import { t } from '@/modules/i18n'
import MarGroupExpand from './MarGroupExpand.vue'

const table = inject<State['table']>('table') as State['table']
const searchBar = inject<State['searchBar']>('searchBar') as State['searchBar']
const handler = inject<State['handler']>('handler') as State['handler']
const query = inject<State['query']>('query') as State['query']
const superQuery = inject<State['superQuery']>('superQuery') as State['superQuery']
const mainBeforeRequest = inject<State['rawOptions']['beforeRequest']>('mainBeforeRequest') as State['rawOptions']['beforeRequest']

// 为数据添加唯一ID - 使用更稳定的生成方式
const processedData = computed(() => {
  return table.data.map((row, index) => {
    // 使用行数据的字符串表示和索引生成相对稳定的hash
    const dataString = JSON.stringify(row)
    const hash = Array.from(dataString).reduce((acc, char) => {
      return ((acc << 5) - acc) + char.charCodeAt(0) | 0
    }, 0)

    return {
      ...row,
      _uniqueId: `row-${index}-${Math.abs(hash)}` // 使用数据hash和索引组合作为唯一ID
    }
  })
})

const processedColumns = computed(() => {
  const cols: TableColumn[] = table.columns
    .filter(item => item.key !== undefined)
    .map(item => {
      return {
        title: item.title,
        key: item.key as string,
        render: (row: any) => {
          // if column has render function, use it
          if (item?.render) {
            return h(item.render, row)
          }
          // if column has options, use it
          if (item?.options) {
            return item.options.find(item => item.value === row[item.value as string])?.label || ''
          }
          // show default value
          return row[item.key as string] ?? ''
        },
      }
    })
  // add expand column
  cols.unshift({
    type: 'expand',
    renderExpand: (row, index) => h(MarGroupExpand, { row, index, superQuery, query, mainBeforeRequest }),
  })
  cols.push({
    title: t('common.count'),
    key: '|count',
    width: 120,
  })
  return cols
})
const scrollX = ref(0)

// 计算基于实际 th 元素宽度的 scrollX
const calculateScrollX = () => {
  nextTick(() => {
    if (!tableRef.value?.$el) return
    
    const thElements = tableRef.value.$el.querySelectorAll('n-data-table-th')
    let totalWidth = 0
    
    thElements.forEach((th: HTMLElement) => {
      totalWidth += th.offsetWidth
    })
    
    scrollX.value = totalWidth
  })
}

const tableRef = ref<InstanceType<typeof NDataTable> | undefined>()

// 监听表格数据和列变化，重新计算 scrollX
watchEffect(() => {
  if (processedData.value.length > 0 && processedColumns.value.length > 0) {
    calculateScrollX()
  }
})

// 监听窗口大小变化，重新计算 scrollX
useEventListener('resize', calculateScrollX)

const { mainContentHeight } = useLayout()
const tableHeight = computed(() => {
  if (searchBar.height > 0) {
    return mainContentHeight.value - searchBar.height - 48 - 66 - 28 - 76
  }
  return mainContentHeight.value - 32 - 66 - 28 - 76
})
</script>

<template>
  <NDataTable ref="tableRef" v-model:checked-row-keys="table.checked" :flex-height="table.inner ? false : true"
    :style="{ height: table.inner ? undefined : `${tableHeight}px` }" :columns="processedColumns" :data="processedData"
    :row-key="row => row._uniqueId" :row-class-name="table.draggable ? 'drag-handle mar-table-row' : 'mar-table-row'"
    size="small" @update-sorter="handler.handleSort" :scroll-x="scrollX"
    v-model:expanded-row-keys="table.expandedRow" />
</template>