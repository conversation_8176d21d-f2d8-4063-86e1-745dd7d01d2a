<script setup lang="ts">
import { CompareEnum, LinkEnum } from '@/constants';
import { State, useTable } from '@/hooks';

const { row, index, query, superQuery, mainBeforeRequest } = defineProps<{
  row: any
  index: number
  query: State['query']
  superQuery: State['superQuery']
  mainBeforeRequest: State['rawOptions']['beforeRequest']
}>()

const raw = inject('raw') as any
const { state, refresh } = useTable(raw.url, raw.columns.map(column => ({
  ...column,
  query: undefined,
})), {
  ...raw.options,
  actionHideList: ['save', 'edit', 'delete', 'deleteMul', 'importTable', 'exportTable', 'advancedQuery', 'config', 'groupSearch'],
  checkable: false,
  beforeRequest: (form) => {
    console.log(row)
    form.push({
      link: LinkEnum.AND,
      data: Object.keys(row)
        .filter(key => key !== '_uniqueId' && !key.includes('|') && key !== 'id')
        .map(key => ({
          colkey: key,
          op: CompareEnum.EQUAL,
          opval: row[key],
          link: LinkEnum.AND
        }))
    })
    if (typeof mainBeforeRequest === 'function') {
      return [...form, ...mainBeforeRequest([...query.form, ...superQuery.form])]
    }
    return [...form, ...query.form, ...superQuery.form]
  },
  inner: true
})
</script>

<template>
  <MarWrapper v-model="state"></MarWrapper>
</template>

<style scoped>
:deep(.n-card__content) {
  padding: 10px !important;
}
</style>