<script setup lang="ts">
import { NDataTable } from 'naive-ui'
import type { TableColumn } from 'naive-ui/es/data-table/src/interface'
import { useLayout } from '@/hooks/useLayout'
import type { State } from '@/hooks/useTable'
import { throttle } from 'radash'

const table = inject<State['table']>('table') as State['table']
const searchBar = inject<State['searchBar']>('searchBar') as State['searchBar']
const handler = inject<State['handler']>('handler') as State['handler']
const sortStates = inject<State['sortStates']>('sortStates') as State['sortStates']
const raw = inject<State['raw']>('raw') as State['raw']
const createSummary = raw.options.createSummary
const rowProps = raw.options.rowProps

const scrollX = ref(0)

// 计算基于实际 th 元素宽度的 scrollX
const calculateScrollX = () => {
  nextTick(() => {
    if (!tableRef.value?.$el) return

    const thElements = tableRef.value.$el.querySelector('.n-data-table-thead')
    if (!thElements || !thElements.offsetWidth) return
    scrollX.value = thElements.offsetWidth
  })
}
onMounted(async () => {
  await nextTick()
  calculateScrollX()
})
onActivated(async () => {
  await nextTick()
  calculateScrollX()
})

// 使用节流函数包装 calculateScrollX，限制 500ms 内只触发一次
const throttledCalculateScrollX = throttle({ interval: 1000 }, calculateScrollX)

const processedColumns = computed(() => {
  return table.columns.filter(column => !column.hideColumn).map((column, index) => {
    if (column.key === 'action' || column.type === 'expand' || column.type === 'selection')
      return column as TableColumn
    const baseColumn = {
      ...column,
      sortOrder: column.sort === 'on' ? (sortStates.find(item => item.columnKey === column.key)?.order || false) : undefined,
      sorter: column.sort === 'on' ? {
        multiple: index
      } : undefined,
      width: column.width ? column.width : column.minWidth,
    } as TableColumn
    const needRenderFn = column.render || column.options
    if (!needRenderFn) return baseColumn
    return {
      ...baseColumn,
      render: (row: any) => {
        // if column has render function, use it
        if (column.render) {
          return h(column.render, row)
        }
        // if column has options, use it
        if (column.options) {
          return column.options.find(item => item.value === row[column.key as string])?.label
        }
        // show default value
        return row[column.key as string]
      },
    } as TableColumn
  })
})

const tableRef = ref<InstanceType<typeof NDataTable> | undefined>()

// 监听表格数据和列变化，重新计算 scrollX
watchEffect(() => {
  if (table.data.length > 0 && processedColumns.value.length > 0) {
    calculateScrollX()
  }
})

// 监听窗口大小变化，重新计算 scrollX
useEventListener('resize', calculateScrollX)

const { mainContentHeight } = useLayout()
const tableHeight = computed(() => {
  if (searchBar.height > 0) {
    return mainContentHeight.value - searchBar.height - 48 - 66 - 28 - 76
  }
  return mainContentHeight.value - 32 - 66 - 28 - 76
})

const needVirtual = computed(() => {
  return table.data.length > 200
})
</script>

<template>
  <NDataTable 
    v-if="table.view === 'table'" 
    ref="tableRef" 
    v-model:checked-row-keys="table.checked"
    :flex-height="table.inner ? false : true" 
    :style="table.inner ? undefined : { height: `${tableHeight}px` }"
    :columns="processedColumns" 
    :data="table.data" :row-key="row => row.id" :row-props="rowProps"
    :row-class-name="table.draggable ? 'drag-handle mar-table-row' : 'mar-table-row'" 
    size="small"
    @update:sorter="handler.handleSort" 
    :scroll-x="scrollX" 
    v-model:expanded-row-keys="table.expandedRow"
    :summary="createSummary" 
    :min-row-height="45" 
    :height-for-row="() => 45" 
    :scrollbar-props="{
      xScrollable: true, trigger: 'none'
    }"
    @scroll="throttledCalculateScrollX" 
    :max-height="table.inner ? undefined : tableHeight" 
    :header-height="45"
    :virtual-scroll="table.inner ? false : needVirtual"
    :sticky-summary="true" :summary-max-height="46"
     />
  <div v-else class="grid gap-4 grid-cols-4">
    <mar-card v-for="item in table.data" :key="item.id" :data="item" />
  </div>
</template>
