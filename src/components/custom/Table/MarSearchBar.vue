<script setup lang="ts">
import { CompareEnum, LinkEnum } from '@/constants/query'
import type { QueryPatternItem } from '@/hooks/useTableType'
import type { QueryForm } from '@/hooks/useTableType'
import type { Shortcuts } from 'naive-ui/es/date-picker/src/interface'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import { resolveDefaultValue } from '@/hooks/useTable'
import { useRoute, useRouter } from 'vue-router'
import { onUnmounted, ref, computed, watch, inject, nextTick, h } from 'vue'
import { useDialog, NInput } from 'naive-ui'

const { t } = useI18n()

const props = defineProps<{
  query: {
    pattern: QueryPatternItem[]
    form: Record<string, any>
  }
}>()

const emit = defineEmits(['search', 'reset'])

const route = useRoute()
const router = useRouter()
const dialog = useDialog()
const formData = ref<Record<string, any>>({})

const raw = inject('raw') as {
  columns: any[]
  options: {
    getQueryFromUrl: boolean
  }
}

function initFormData() {
  props.query.pattern.forEach((item) => {
    if (formData.value[item.prop] !== undefined && formData.value[item.prop] !== null && formData.value[item.prop] !== '') {
      return
    }
    
    // 解析默认查询值
    const defaultValue = resolveDefaultValue(item)
    if (defaultValue !== undefined) {
      formData.value[item.prop] = defaultValue
      return
    }
    
    if (item.type === 'date-range' || item.type === 'date' || item.type === 'select') {
      formData.value[item.prop] = null
      return
    }
    
    formData.value[item.prop] = ''
  })
}

function resetAllFormData() {
  props.query.pattern.forEach((item) => {
    if (item.type === 'date-range' || item.type === 'date' || item.type === 'select') {
      formData.value[item.prop] = null
      return
    }
    formData.value[item.prop] = ''
  })
}

const queryWrapper: QueryForm = [{
  link: LinkEnum.AND,
  data: [],
}]
function resetQueryWrapper() {
  console.log('reset query wrapper')
  queryWrapper[0].data = []
}
function handleSearch() {
  resetQueryWrapper()
  for (const [key, value] of Object.entries(formData.value)) {
    if (value === undefined || value === null || value === '') {
      continue
    }
    const pattern = props.query.pattern.find(item => item.prop === key)
    if ((pattern?.type === 'date' || pattern?.type === 'date-range' || pattern?.type === 'date-range-time') && Array.isArray(value) && value.length === 2) {
      console.log('date range', pattern.type)
      queryWrapper[0].data.push({
        colkey: key,
        op: CompareEnum.GREATER_EQUAL,
        opval: value[0],
        link: LinkEnum.AND,
      })
      queryWrapper[0].data.push({
        colkey: key,
        op: CompareEnum.LESS_EQUAL,
        /**
         * 日期时间格式为 yyyy-MM-dd HH:mm:ss
         * 日期格式为 yyyy-MM-dd
         * 需要使用 dayjs 为 value[1] 加一天，并格式化回原格式
         */
        opval: pattern?.type === 'date-range-time' 
          ? dayjs(value[1]).add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
          : dayjs(value[1]).add(1, 'day').format('YYYY-MM-DD'),
        link: LinkEnum.AND,
      })
    } else if(pattern?.type === 'excel' || pattern?.type === 'excel-textarea') {
      queryWrapper[0].data.push({
        colkey: key,
        op: CompareEnum.IN,
        opval: value.split('\n').join(','),
        link: LinkEnum.AND,
      })
    } else if(pattern?.type === 'select-multiple') {
      queryWrapper[0].data.push({
        colkey: key,
        op: CompareEnum.IN,
        opval: value.join(','),
        link: LinkEnum.AND,
      })
    } else {
      queryWrapper[0].data.push({
        colkey: key,
        op: pattern?.type === 'select' ? CompareEnum.EQUAL : CompareEnum.LIKE,
        opval: value,
        link: LinkEnum.AND,
      })
    }
  }
  console.log('compact query wrapper')
  emit('search', queryWrapper)
}

function handleReset() {
  resetAllFormData()
  // initFormData()
  nextTick(() => {
    emit('reset')
  })
}

// 获取日期的时间戳（毫秒）
function getDateTimestamp(date: dayjs.Dayjs): number {
  return date.valueOf()
}

// 计算季度的开始和结束日期
function getQuarterRange(date: dayjs.Dayjs): [dayjs.Dayjs, dayjs.Dayjs] {
  const quarter = Math.floor(date.month() / 3)
  const startMonth = quarter * 3
  const endMonth = startMonth + 2
  
  const startDate = dayjs(date).month(startMonth).startOf('month')
  const endDate = dayjs(date).month(endMonth).endOf('month')
  
  return [startDate, endDate]
}

// 日期时间选择器快捷选项
// const dateTimeShortcuts: Shortcuts = {
//   [t('datePicker.today')]: () => {
//     return getDateTimestamp(dayjs())
//   }
// }

// 日期范围选择器快捷选项
const dateRangeShortcuts: Shortcuts = {
  [t('datePicker.today')]: () => {
    const today = dayjs()
    return [getDateTimestamp(today), getDateTimestamp(today)]
  },
  [t('datePicker.thisWeek')]: () => {
    const now = dayjs()
    const startDate = now.startOf('week')
    const endDate = now.endOf('week')
    return [getDateTimestamp(startDate), getDateTimestamp(endDate)]
  },
  [t('datePicker.thisMonth')]: () => {
    const now = dayjs()
    const startDate = now.startOf('month')
    const endDate = now.endOf('month')
    return [getDateTimestamp(startDate), getDateTimestamp(endDate)]
  },
  [t('datePicker.thisQuarter')]: () => {
    const now = dayjs()
    const [startDate, endDate] = getQuarterRange(now)
    return [getDateTimestamp(startDate), getDateTimestamp(endDate)]
  }
}

// 日期时间范围选择器快捷选项
const dateTimeRangeShortcuts: Shortcuts = {
  [t('datePicker.today')]: () => {
    const today = dayjs()
    const startOfDay = today.startOf('day')
    const endOfDay = today.endOf('day')
    return [getDateTimestamp(startOfDay), getDateTimestamp(endOfDay)]
  },
  [t('datePicker.thisWeek')]: () => {
    const now = dayjs()
    const startDate = now.startOf('week')
    const endDate = now.endOf('week')
    return [getDateTimestamp(startDate), getDateTimestamp(endDate)]
  },
  [t('datePicker.thisMonth')]: () => {
    const now = dayjs()
    const startDate = now.startOf('month')
    const endDate = now.endOf('month')
    return [getDateTimestamp(startDate), getDateTimestamp(endDate)]
  },
  [t('datePicker.thisQuarter')]: () => {
    const now = dayjs()
    const [startDate, endDate] = getQuarterRange(now)
    return [getDateTimestamp(startDate), getDateTimestamp(endDate)]
  }
}

// 使用 ref 来跟踪搜索栏是否获得焦点
const searchBarFocused = ref(false)

// 处理表单内元素的焦点事件
const handleFocus = () => {
  searchBarFocused.value = true
}

const handleBlur = () => {
  searchBarFocused.value = false
}

// 处理键盘事件，只有当搜索栏获得焦点时才响应 Enter 键
const handleKeyDown = (event: KeyboardEvent) => {
  if (searchBarFocused.value && event.key === 'Enter') {
    event.preventDefault()
    handleSearch()
  }
}

const target = ref<HTMLDivElement | null>(null)

initFormData()
const getQueryFromUrl = computed(() => raw.options.getQueryFromUrl)
let isFirst = true
let currentPath = route.path

const stopRouteWatch = watch([() => route.query, () => route.hash, () => route.path], (newVal, oldVal) => {
  // 如果路径发生变化且不是当前页面路径，则不执行搜索
  if (route.path !== currentPath) {
    return
  }
  
  if (!getQueryFromUrl.value) return
  if (!isFirst && newVal[1] !== oldVal[1]) return
  isFirst = false
  raw.columns.forEach((item) => {
    formData.value[item.key] = route.query[item.key]
  })
  handleSearch()
}, { immediate: true, deep: true })

// 组件卸载时清理路由监听
onUnmounted(() => {
  stopRouteWatch()
})

function handleExcelDialog(prop: string) {
  // 打开一个包含 textarea 的弹窗
  const val = ref(formData.value[prop])
  const instance = dialog.warning({
    title: '输入数据',
    content: () => h('div', [
      h(NInput, {
        type: 'textarea',
        value: val.value,
        onUpdateValue: (value) => {
          val.value = value
        },
        rows: 10,
        placeholder: '请输入数据，每行一个值',
      })
    ]),
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      formData.value[prop] = val.value
      instance.destroy()
    },
    onNegativeClick: () => {
      instance.destroy()
    }
  })
}
</script>

<template>
  <n-form ref="target" inline :model="formData" label-placement="left" class="flex-wrap-form" size="small" @keydown="handleKeyDown">
    <n-form-item v-for="item in query.pattern" :key="item.prop" :label="item.label">
      <!-- 输入框 -->
      <n-input v-if="item.type === 'input'" v-model:value="formData[item.prop]"
        :style="{ width: item.width ? `${item.width}px` : '140px' }" clearable
        @focus="handleFocus" @blur="handleBlur">
        <template v-if="item.prefix" #prefix>
          <span v-if="typeof item.prefix === 'string'">{{ item.prefix }}</span>
          <component v-else :is="item.prefix" />
        </template>
        <template v-if="item.suffix" #suffix>
          <span v-if="typeof item.suffix === 'string'">{{ item.suffix }}</span>
          <component v-else :is="item.suffix" />
        </template>
      </n-input>

      <n-input-number v-else-if="item.type === 'input-number'" v-model:value="formData[item.prop]"
        :style="{ width: item.width ? `${item.width}px` : '140px' }" clearable
        @focus="handleFocus" @blur="handleBlur">
        <template v-if="item.prefix" #prefix>
          <span v-if="typeof item.prefix === 'string'">{{ item.prefix }}</span>
          <component v-else :is="item.prefix" />
        </template>
        <template v-if="item.suffix" #suffix>
          <span v-if="typeof item.suffix === 'string'">{{ item.suffix }}</span>
          <component v-else :is="item.suffix" />
        </template>
      </n-input-number>

      <!-- 选择框 -->
      <n-select v-else-if="item.type === 'select' || item.type === 'select-multiple'" filterable clearable v-model:value="formData[item.prop]"
        :options="item.options" max-tag-count="responsive" :style="{ width: item.width ? `${item.width}px` : '140px' }" :multiple="item.type === 'select-multiple'"
        @focus="handleFocus" @blur="handleBlur">
        <template v-if="item.prefix" #prefix>
          <span v-if="typeof item.prefix === 'string'">{{ item.prefix }}</span>
          <component v-else :is="item.prefix" />
        </template>
        <template v-if="item.suffix" #suffix>
          <span v-if="typeof item.suffix === 'string'">{{ item.suffix }}</span>
          <component v-else :is="item.suffix" />
        </template>
      </n-select>

      <!-- 日期选择器 -->
      <!-- <n-date-picker value-format="yyyy-MM-dd HH:mm:ss" clearable v-else-if="item.type === 'date'"
        v-model:formatted-value="formData[item.prop]" type="datetime"
        :style="{ width: item.width ? `${item.width}px` : '140px' }" :shortcuts="dateTimeShortcuts" /> -->

      <!-- 日期范围选择器 -->
      <n-date-picker value-format="yyyy-MM-dd" clearable v-else-if="item.type === 'date-range' || item.type === 'date'"
        v-model:formatted-value="formData[item.prop]" type="daterange"
        :style="{ width: item.width ? `${item.width}px` : '260px' }" :shortcuts="dateRangeShortcuts" />

      <!-- 日期时间范围选择器 -->
      <n-date-picker value-format="yyyy-MM-dd HH:mm:ss" clearable v-else-if="item.type === 'date-range-time'"
        v-model:formatted-value="formData[item.prop]" type="datetimerange"
        :style="{ width: item.width ? `${item.width}px` : '260px' }" :shortcuts="dateTimeRangeShortcuts" />

      <!-- excel -->
      <n-input v-else-if="item.type === 'excel'" v-model:value="formData[item.prop]"
        :style="{ width: item.width ? `${item.width}px` : '140px' }" clearable
        @focus="handleFocus" @blur="handleBlur">
        <template #suffix>
          <n-button size="small" text @click.stop="handleExcelDialog(item.prop)">
            <template #icon>
              <div class="i-icon-park-outline-excel h-1.2rem w-1.2rem" />
            </template>
          </n-button>
        </template>
      </n-input>

      <!-- excel-textarea -->
      <n-input v-else-if="item.type === 'excel-textarea'" v-model:value="formData[item.prop]"
        :style="{ width: item.width ? `${item.width}px` : '140px' }" clearable type="textarea" rows="3"
        @focus="handleFocus" @blur="handleBlur">
      </n-input>
    </n-form-item>
    <n-flex class="ml-auto">
      <n-button type="primary" size="small" @click.stop="handleSearch">
        <template #icon>
          <div class="i-icon-park-outline-search h-1.2rem w-1.2rem" />
        </template>
        {{ $t('common.search') }}
      </n-button>
      <n-button strong secondary size="small" @click="handleReset">
        <template #icon>
          <div class="i-icon-park-outline-redo h-1.2rem w-1.2rem" />
        </template>
        {{ $t('common.reset') }}
      </n-button>
    </n-flex>
  </n-form>
</template>

<style scoped>
.flex-wrap-form {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
  /* 垂直间距 8px，水平间距 16px */
}

.flex-wrap-form :deep(.n-form-item) {
  margin-right: 0;
  margin-bottom: 0;
  /* 移除底部边距 */
}

.flex-wrap-form :deep(.n-form-item-feedback-wrapper) {
  min-height: 0;
  /* 移除反馈区域的最小高度 */
  margin-top: 0;
  /* 移除顶部边距 */
}
</style>
