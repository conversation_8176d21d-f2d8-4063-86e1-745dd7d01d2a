<script setup lang="ts">
import UploadDialog from '../Dialog/UploadDialog.vue'
import { RootRefreshSymbol } from '@/constants/symbol'
import type { State } from '@/hooks/useTable'
import type { QueryForm } from '@/hooks/useTableType'
import MarGroupTable from './MarGroupTable.vue'

const modelValue = defineModel<State>({
  required: true,
})
function handleSearch(form: QueryForm) {
  modelValue.value.pagination.page = 1
  modelValue.value.query.form.length = 0
  modelValue.value.query.form.push(form[0])
}

function handleReset() {
  modelValue.value.query.form = []
}

function handleSuperSearch(form: QueryForm) {
  modelValue.value.superQuery.form.length = 0
  modelValue.value.superQuery.form.push(form[0])
}

function handleSuperReset() {
  modelValue.value.superQuery.form = []
}

const searchBarRef = ref<HTMLElement | null>(null)
const { height: searchBarHeight } = useElementSize(searchBarRef)
watch(searchBarHeight, (newHeight) => {
  modelValue.value.searchBar.height = newHeight
})

provide('handler', modelValue.value.handler)
provide('table', modelValue.value.table)
provide('sortStates', modelValue.value.sortStates)
provide('trigger', modelValue.value.trigger)
provide('searchBar', modelValue.value.searchBar)
provide('toolbar', modelValue.value.toolbar)
provide('superQuery', modelValue.value.superQuery)
provide('query', modelValue.value.query)
provide(RootRefreshSymbol, {
  refresh: handleReset,
})
provide('groupFieldList', modelValue.value.groupFieldList)
provide('rawOptions', modelValue.value.rawOptions)
provide('raw', modelValue.value.raw)
provide('mainBeforeRequest', modelValue.value.rawOptions.beforeRequest)
const uploadDialogRef = ref<InstanceType<typeof UploadDialog> | null>(null)
modelValue.value.on((event: string) => {
  if (event === 'importTable') {
    uploadDialogRef.value?.open()
  }
})
</script>

<template>
  <n-space vertical size="large">
    <n-card v-show="modelValue.query.pattern.length > 0" ref="searchBarRef">
      <mar-search-bar :query="modelValue.query" @search="handleSearch" @reset="handleReset" />
    </n-card>
    <n-card :class="{ 'bg-#FDFFF9 dark:bg-#1A1A1A': modelValue.table.inner }"
      :size="modelValue.table.inner ? 'small' : undefined">
      <slot name="free-zone" />
      <component :is="modelValue.freeZone" />
      <UploadDialog v-if="typeof modelValue.url === 'object' && modelValue.url.importTable" ref="uploadDialogRef"
        :upload-api="modelValue.url.importTable" :template-api="modelValue.url.importTemplate"
        :after-upload="modelValue.fetchData" />
      <n-space vertical size="large">
        <n-spin :show="modelValue.loading">
          <mar-toolbar @super-search="handleSuperSearch" @super-reset="handleSuperReset">
            <n-radio-group v-if="modelValue.allowViewChange" v-model:value="modelValue.table.view">
              <n-radio-button value="table">
                <div class="i-icon-park-outline-list-checkbox h-1.2rem w-1.2rem" />
              </n-radio-button>
              <n-radio-button value="card">
                <div class="i-icon-park-outline-id-card h-1.2rem w-1.2rem" />
              </n-radio-button>
            </n-radio-group>
            <template #left>
              <slot name="toolbarLeft" />
            </template>
            <template v-for="item in modelValue.toolbarExt">
              <n-button v-if="typeof item !== 'function'" size="small" :key="item.label" :type="item.type"
                :icon="item.icon"
                @click="typeof item.click === 'function' ? item.click() : modelValue.trigger(item.click)">
                {{ item.label }}
              </n-button>
              <component :is="item" v-else />
            </template>
            <template #right>
              <slot name="toolbarRight" />
            </template>
            <slot name="toolbarExt" />
          </mar-toolbar>
          <slot name="table">
            <mar-table v-if="modelValue.groupFieldList.form.length === 0" />
            <mar-group-table v-else />
          </slot>
        </n-spin>
        <div class="flex justify-between items-center">
          <n-pagination v-model:page="modelValue.pagination.page" v-model:page-size="modelValue.pagination.limit"
            :item-count="modelValue.pagination.total"
            :page-sizes="modelValue.rawOptions.pageSizeOptions ?? [10, 50, 100, 500]" show-size-picker>
            <template #prefix="{ itemCount }">
              {{ $t('common.total') + itemCount + $t('common.item') }}
            </template>
          </n-pagination>
          <slot name="badges" />
        </div>
      </n-space>
    </n-card>
  </n-space>
</template>

<style scoped>
:deep(.n-radio__label) {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}
</style>
