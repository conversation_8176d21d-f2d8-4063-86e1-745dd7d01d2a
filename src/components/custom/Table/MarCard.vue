<script setup lang="ts">
import type { State } from '@/hooks/useTable'

defineProps<{
  data: Record<string, any>
}>()

const table = inject<State['table']>('table') as State['table']
</script>

<template>
  <n-card>
    <n-descriptions label-placement="left" :column="1">
      <n-descriptions-item v-for="item in table.columns" :key="item.key" :label="item.title">
        {{ data[item.key as string] }}
      </n-descriptions-item>
    </n-descriptions>
  </n-card>
</template>
