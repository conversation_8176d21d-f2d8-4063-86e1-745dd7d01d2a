import { JsonTreeView } from 'json-tree-view-vue3'
import { defineComponent, h } from 'vue'
import MarDialog from '@/components/common/MarDialog.vue'
import * as clipboard from "clipboard-polyfill"
import { NButton } from 'naive-ui'
import { t } from '@/modules/i18n'

function createJsonRender(title: string, propertyKey: string) {
  return (row: Record<string, any>) => {
    return h(MarDialog, {
      label: typeof row[propertyKey] === 'string' ? row[propertyKey] : JSON.stringify(row[propertyKey]),
      title: title,
      buttonProps: {
        type: 'text',
        class: 'underline text-blue-500 p-0',
      }
    }, {
      default: () => h('div', {}, {
        default: () => [
          h(NButton, {
            type: 'primary',
            size: 'small',
            class: 'mb-2',
            onClick: () => {
              clipboard.writeText(typeof row[propertyKey] === 'string' ? row[propertyKey] : JSON.stringify(row[propertyKey])).then(
                () => {
                  window.$message.success('common.success')
                },
                () => {
                  window.$message.error('common.error')
                }
              )
            }
          }, { default: () => t('common.copy') }),
          h(JsonTreeView, { json: typeof row[propertyKey] === 'string' ? row[propertyKey] : JSON.stringify(row[propertyKey]) })
        ]
      })
    })
  }
}

const JsonRender = defineComponent({
  name: 'JsonRender',
  props: {
    json: {
      type: [String, Object],
      required: true
    },
    title: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    return () => h(MarDialog, {
      label: typeof props.json === 'string' ? props.json : JSON.stringify(props.json),
      title: props.title,
      buttonProps: {
        type: 'text',
        class: 'underline text-blue-500 p-0',
      }
    }, {
      default: () => h('div', {}, {
        default: () => [
          h(NButton, {
            type: 'primary',
            size: 'small',
            class: 'mb-2',
            onClick: () => {
              clipboard.writeText(typeof props.json === 'string' ? props.json : JSON.stringify(props.json)).then(
                () => {
                  window.$message.success('common.success')
                },
                () => {
                  window.$message.error('common.error')
                }
              )
            }
          }, { default: () => t('common.copy') }),
          h(JsonTreeView, { json: typeof props.json === 'string' ? props.json : JSON.stringify(props.json) })
        ]
      })
    })
  }
})

export { JsonRender, createJsonRender }
