import { MarDialog } from '@/components'
import { NButton, NEmpty, NImage } from 'naive-ui'
import { t } from '@/modules/i18n'

const ImgPreviewRender = defineComponent({
  name: 'ImgPreviewRender',
  props: {
    url: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const urlList = computed(() => props.url.split(','))
    return () => <MarDialog label={t('common.preview')} title={props.title} buttonProps={{
      type: 'text',
      class: 'underline text-blue-500 p-0',
    }}>
      {
        urlList.value.length > 0 ? (
          urlList.value.map((url) => <NImage src={url} width={100} height={100} />)
        ) : (
          <NEmpty />
        )
      }
    </MarDialog>
  }
})

export {
  ImgPreviewRender
}