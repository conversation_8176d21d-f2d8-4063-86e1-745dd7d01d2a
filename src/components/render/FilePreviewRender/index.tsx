import { MarDialog } from '@/components'
import { NEmpty } from 'naive-ui'
import { t } from '@/modules/i18n'
import { defineComponent } from 'vue'

const FilePreviewRender = defineComponent({
  name: 'FilePreviewRender',
  props: {
    url: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const urlList = computed(() => props.url.split(','))
    return () => <MarDialog label={t('common.preview')} title={props.title} buttonProps={{
      type: 'text',
      class: 'underline text-blue-500 p-0',
    }}>
      {
        urlList.value.length > 0 ? (
          urlList.value.map((url) => <a href={url} target="_blank">{url.split('/').pop()}</a>)
        ) : (
          <NEmpty />
        )
      }
    </MarDialog>
  }
})

export {
  FilePreviewRender
}