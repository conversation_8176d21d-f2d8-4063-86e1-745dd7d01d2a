import { goto } from '@/utils'
import { defineComponent, h } from 'vue'

const URLRender = defineComponent({
  name: 'URLRender',
  props: {
    url: {
      type: String,
      required: true
    },
    label: {
      type: String,
    },
    open: {
      type: String as () => 'inner' | 'newWindow',
      default: 'inner'
    },
    query: {
      type: Object as () => Record<string, any>,
      default: () => ({})
    }
  },
  setup(props) {
    return () => {
      return <a onClick={(e) => {
        e.preventDefault()
        if (props.open === 'inner') {
          goto(props.url, {
            query: props.query,
            disableFrom: true
          })
        } else {
          const queryString = Object.entries(props.query).map(([key, value]) => `${key}=${value}`).join('&')
          window.open(`${props.url}?${queryString}`, '_blank')
        }
      }} class="text-blue-500 underline cursor-pointer">{ props.label || props.url}</a>
    }
  }
})

export { URLRender }