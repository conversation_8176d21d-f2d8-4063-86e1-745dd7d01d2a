import MarDialog from '@/components/common/MarDialog.vue'

function createLogRender(title: string, propertyKey: string) {
  return (row: Record<string, any>) => {
    return h(MarDialog, {
      label: row[propertyKey],
      title: title,
      buttonProps: {
        type: 'text',
        class: 'underline text-blue-500 p-0',
      }
    }, {
      default: () => h('div', {
        innerHTML: row[propertyKey]
      })
    })
  }
}

export { createLogRender }
