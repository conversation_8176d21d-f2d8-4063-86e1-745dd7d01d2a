import MarForm from '@/components/common/MarForm.vue'
import MarDialog from '@/components/common/MarDialog.vue'
import AppLoading from '@/components/common/AppLoading.vue'
import CommonWrapper from '@/components/common/CommonWrapper.vue'
import CommonError from '@/components/common/CommonError.vue'
import DarkModeSwitch from '@/components/common/DarkModeSwitch.vue'
import ErrorTip from '@/components/common/ErrorTip.vue'
import HelpInfo from '@/components/common/HelpInfo.vue'
import LangsSwitch from '@/components/common/LangsSwitch.vue'
import MarIconSelect from '@/components/common/MarIconSelect.vue'
import MarUpload from '@/components/common/MarUpload.vue'
import NaiveProvider from '@/components/common/NaiveProvider.vue'
import ShowOrEdit from '@/components/common/ShowOrEdit.vue'
import Watermark from '@/components/common/Watermark.vue'
import MarTree from '@/components/common/MarTree.vue'
import DynTablePage from '@/components/dynamic/DynTablePage'
import DynFormPage from '@/components/dynamic/DynFormPage'
import TableDictSelect from '@/components/custom/TableDictSelect'
import APIDictSelect from '@/components/custom/APIDictSelect'
import BareDynTableV3 from './dynamic/BareDynTableV3.vue'
import { SubmitButtonGroup } from './common/SubmitButtonGroup'

import AdvancedQueryDialog from '@/components/custom/Dialog/AdvancedQueryDialog.vue'
import FormDialog from '@/components/custom/Dialog/FormDialog.vue'
import FormDialogV2 from '@/components/custom/Dialog/FormDialogV2'
import TableDialog from '@/components/custom/Dialog/TableDialog.vue'
import UploadDialog from '@/components/custom/Dialog/UploadDialog.vue'

import MarCard from '@/components/custom/Table/MarCard.vue'
import MarSearchBar from '@/components/custom/Table/MarSearchBar.vue'
import MarTable from '@/components/custom/Table/MarTable.vue'
import MarToolbar from '@/components/custom/Table/MarToolbar.vue'
import MarWrapper from '@/components/custom/Table/MarWrapper.vue'
import MarEditTable from '@/components/common/MarEditTable.vue'

import { createJsonRender, JsonRender } from '@/components/render/JsonRender'
import { createLogRender } from '@/components/render/LogRender'
import { URLRender } from '@/components/render/URLRender'

import DynTable from '@/components/dynamic/DynTable.vue'
import DynForm from '@/components/dynamic/DynForm.vue'
import DynNestForm from '@/components/dynamic/DynNestForm.vue'
import DynFormV2 from '@/components/dynamic/DynFormV2.vue'
import DynFormV3 from '@/components/dynamic/DynFormV3.vue'
import CodeEditor from '@/components/common/CodeEditor.vue'
import BasePage from './common/BasePage'

export type * from '@/components/common/MarFormType'
export type * from '@/components/custom/Dialog/FormDialogV2'

export {
  MarForm,
  MarDialog,
  AppLoading,
  CommonError,
  CommonWrapper,
  DarkModeSwitch,
  ErrorTip,
  HelpInfo,
  LangsSwitch,
  MarIconSelect,
  MarUpload,
  NaiveProvider,
  ShowOrEdit,
  Watermark,

  AdvancedQueryDialog,
  FormDialog,
  FormDialogV2,
  TableDialog,
  UploadDialog,
  MarCard,
  MarSearchBar,
  MarTable,
  MarToolbar,
  MarWrapper,
  MarEditTable,
  createJsonRender,
  createLogRender,
  JsonRender,
  URLRender,

  DynTable,
  DynForm,
  DynNestForm,
  DynFormV2,
  DynFormV3,
  CodeEditor,
  MarTree,
  DynTablePage,
  DynFormPage,
  TableDictSelect,
  APIDictSelect,
  BareDynTableV3,
  BasePage,
  SubmitButtonGroup
}

export * from '@/components/dynamic/DynTableType'
export * from '@/components/dynamic/DynTablePage'
