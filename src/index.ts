import AppVue from './App.vue'
import AppLoading from './components/common/AppLoading.vue'
import { initSystem } from './init'
import { installI18n, mergeLocalMessages } from './modules/i18n'
import { useAuth } from './hooks'
import type { RequestOptions, SystemOptions } from './types/configType'
import { setupRequestConfig, setupSystemConfig } from './config'
import type { Bucket } from './views/bucket'
import { pageBucket } from './views/bucket'
import { installRouter } from '@/router'
import './styles/index.css'

export async function setupApp(options: SetupAppOptions) {
  setupSystemConfig(options.system)
  // global loading
  const appLoading = createApp(AppLoading)
  appLoading.mount('#appLoading')

  setupRequestConfig(options.request)
  mergeLocalMessages(options.i18n)

  const app = createApp(AppVue)

  installI18n(app)

  await useAuth().fetchUserInfo()
  await initSystem()
  await installRouter(app)

  // unmount global loading
  appLoading.unmount()

  app.mount('#app')
}

export * from '@/components/index'
export * from '@/types/index'
export * from '@/hooks/index'
export * from '@/utils/index'
export * from '@/constants/index'
export * from '@/decorator/index'
export { t as trans } from '@/modules/i18n'
export { router } from '@/router/index'
export { makeCore } from '@/core'

export function mergePageBucket(bucket: Bucket) {
  Object.assign(pageBucket, bucket)
  console.log(pageBucket)
}

interface SetupAppOptions {
  // 系统相关配置
  system?: SystemOptions
  // 请求相关配置
  request?: RequestOptions
  i18n?: {
    zhCN?: Record<string, any>
    enUS?: Record<string, any>
  }
}

if (import.meta.env.DEV) {
  await setupApp({
    system: {
      list: [
        {
          key: 'auth',
          homePath: '/auth/dashboard/workbench',
        },
        {
          key: 'wcs',
          homePath: '/wcs/dashboard/workbench',
        },
        {
          key: 'wms',
          homePath: '/wms/dashboard/workbench',
        },
        {
          key: 'pda',
        }
      ],
      table: {
        firstColumnRouter: true,
      },
    }
  })
}
