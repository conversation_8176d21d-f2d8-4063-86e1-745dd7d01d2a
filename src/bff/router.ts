import { useSystem } from '@/hooks/useSystem'
import type { ServerPageMenuItem } from '@/types/menuType'
import type { FlatRoute } from '@/types/routerType'

export function routerBff(raw: ServerPageMenuItem[]): FlatRoute[] {
  const { currentSystem } = useSystem()
  const result: FlatRoute[] = []

  const process = (item: ServerPageMenuItem) => {
    result.push({
      id: item.id,
      pid: item.pid,
      name: item.menuName,
      path: item.url.startsWith(`/${currentSystem.value}`) ? item.url : `/${currentSystem.value}${item.url}`,
      componentPath: item.menuType === 2 ? (item.url.startsWith(`/${currentSystem.value}`) ? item.url : `/${currentSystem.value}${item.url}`) : undefined,
      title: item.menuName,
      icon: item.icon,
      keepAlive: item.isCache === 1,
      href: item.openStyle === 1 ? item.url : undefined,
      openStyle: item.openStyle as 1 | 0,
      menuType: item.menuType === 1 ? 'dir' : 'page',
      dynCode: item.dynCode,
      menuId: item.id,
    })

    if (item.children && item.children.length > 0) {
      item.children.forEach(child => process(child))
    }
  }

  // 处理顶层菜单
  raw.forEach(item => process(item))

  console.log(result)
  return result
}
