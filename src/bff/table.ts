import { useDict } from '@/hooks/useDict'
import type { Columns } from '@/utils/entity'
import { t } from '@/modules/i18n'
import type { FormConfig } from '@/components/custom/Dialog/FormDialogV2'
import type { FormSchema } from '@/components'
import { useRequest } from '@/hooks/useRequest'
import type { URL } from '@/hooks/useTableType'
import { systemConfig } from '@/config/system'
import { ImgPreviewRender } from '@/components/render/ImgPreviewRender'
import { FilePreviewRender } from '@/components/render/FilePreviewRender'
import { h } from 'vue'
import { NPopover } from 'naive-ui'

export interface ChildExt {
  subMomKey: string
  momKey: string
  dynCode: string
}

export interface TableBffReturn {
  url: URL
  columns: Columns[]
  formConfigs: FormConfig[]
  child?: TableBffReturn & ChildExt
  raw: any
}

export const serverQueryTypeMap: Record<string, Columns['query']> = {
  // this two reserved for compatibility
  'like': 'input',
  'eq': 'select',

  'input': 'input',
  'number': 'input-number',
  'select': 'select',
  'date-range': 'date-range',
  'date-range-time': 'date-range-time',
  'select-multiple': 'select-multiple',
  'excel': 'excel',
  'excel-textarea': 'excel-textarea'
}

export const serverFormTypeMap: Record<string, FormSchema[number]['type']> = {
  'text': 'input',
  'number': 'input-number',
  'select': 'select',
  'select-multiple': 'select-multiple',
  'radio': 'radio',
  'checkbox': 'checkbox-group',
  'date': 'date',
  'date-time': 'date-time',
  'date-range': 'date-range',
  'upload-image': 'upload-image',
  'upload-file': 'upload-file',
  'textarea': 'textarea',
}

export const bffCache = new Map<string, any>()

/**
 * @param fieldList 动态表核心字段
 * @returns configList
 */
export async function tableBff(dynCode: string, menuId: string, handleCheck?: (row: any) => void): Promise<TableBffReturn> {
  if (bffCache.has(dynCode)) {
    return bffCache.get(dynCode)
  }
  const { data } = await useRequest<any>(`/devtools/dynTable/menuInfo?dynCode=${dynCode}&menuId=${menuId}`)
  return metaTableBff(data, dynCode, handleCheck)
}

export async function process(fieldList: any[], dynCode: string, handleCheck?: (row: any) => void): Promise<TableBffReturn> {
  const columns: Columns[] = []
  const formSchema: FormSchema = []
  let firstColumn = handleCheck ? true : false
  for (const field of fieldList) {
    let columnOptions
    let columnRender
    if (field.dictName) {
      const { options, render } = await getDictOptionsAndRender(field.dictName, field.attrName)
      columnOptions = options
      columnRender = render
    }
    if (field.formType === 'upload-image') {
      columnRender = (row: any) => h(ImgPreviewRender, { url: row[field.attrName] })
    }
    if (field.formType === 'upload-file') {
      // 直接展示url的最后一段, 如果url为空, 则展示空字符串
      columnRender = (row: any) => h(FilePreviewRender, { url: row[field.attrName] })
    }
    const columnItem: Columns = {
      title: t(field.columnLabel),
      key: field.attrName,
      query:
        field.isQuery ?
          // field.queryLink === 'eq' ?
          serverQueryTypeMap[field.queryLink]
          // : 'input'
          : undefined,
      width: field.columnWidth ? Number(field.columnWidth) : undefined,
      sort: field.isSortable ? 'on' : 'off',
      superQuery: field.superQuery ? field.dictName ? 'select' : 'input' : undefined,
      options: columnOptions,
      // render: (systemConfig.table?.firstColumnRouter && firstColumn) ?
      //   (row: any) => h(NPopover, {
      //     trigger: 'hover',
      //   }, {
      //     trigger: () => h('a', {
      //       class: 'underline text-blue-500 cursor-pointer',
      //       onClick: () => handleCheck?.(row)
      //     }, typeof columnRender === 'function' ? columnRender(row) : row[field.attrName]),
      //     default: () => h('div', typeof columnRender === 'function' ? columnRender(row) : row[field.attrName])
      //   })
      //   : columnRender,
      render: columnRender,
      header: field.remark ? () => {
        return h('div', { class: 'flex items-center gap-1' }, [
          h(NPopover, {
            trigger: 'hover',
          }, {
            trigger: () => h('div', { class: 'i-icon-park-outline-help text-gray-400 text-sm' }),
            default: () => h('div', t(field.remark))
          }),
          h('span', t(field.columnLabel))
        ]);
      } : undefined,
      hideColumn: field.isList === false,
    }
    firstColumn = false
    columns.push(columnItem)
    if (field.isForm) {
      const rules: FormSchema[number]['rules'] = {}
      if (field.isRequired) {
        rules.required = true
      }
      if (field.validator) {
        try {
          const regex = field.validator
          new RegExp(regex)
          rules.regex = regex
          rules.required = true
        } catch (e) {
        }
      }
      formSchema.push({
        label: t(field.columnLabel),
        prop: field.attrName,
        type: serverFormTypeMap[field.formType],
        rules: rules as FormSchema[number]['rules'],
        options: columnOptions,
        default: field.initialValue,
        disabled: field.isEdit === false,
        remark: field.remark,
      })
    }
  }

  return {
    url: {
      page: `/virtual/${dynCode}/page`,
      exportTable: `/virtual/${dynCode}/exportTable`,
      importTable: `/virtual/${dynCode}/importTable`,
      importTemplate: `/virtual/${dynCode}/importTemplate`,
      save: `/virtual/${dynCode}/save`,
      update: `/virtual/${dynCode}/update`
    },
    columns,
    formConfigs: [
      {
        key: 'new',
        config: {
          formSchema,
          api: `/virtual/${dynCode}/save`,
        },
      },
      {
        key: 'edit',
        config: {
          formSchema,
          api: `/virtual/${dynCode}/update`,
        },
      },
    ],
    raw: {}
  }
}

export function refreshTableBff(dynCode: string) {
  bffCache.delete(dynCode)
}

export async function getDictOptionsAndRender(dictName: string, attrName: string): Promise<{ options: { label: string, value: string }[], render: (row: any) => string }> {
  console.log('获取字典选项', dictName, attrName)
  const { sysDict } = useDict()
  let options: { label: string, value: string }[] = []
  let render: (row: any) => string = () => ''
  if (dictName.startsWith('$.')) {
    const { data } = await useRequest<any>(`/sys/dict/type/dictTable/${dictName.replace('$.', '')}`)
    if (data.value) {
      options = data.value.map((item: { code: string, name: string }) => ({ label: t(item.name), value: item.code }))
      render = (row: any) => t(options.find((item: { value: string, label: string }) => item.value === row[attrName])?.label ?? '')
    }
  } else if (dictName.startsWith('#.')) {
    const { data } = await useRequest<any>(`${dictName.replace('#.', '')}`)
    if (data.value) {
      options = data.value.map((item: { code: string, name: string }) => ({ label: t(item.name), value: item.code }))
      render = (row: any) => t(options.find((item: { value: string, label: string }) => item.value === row[attrName])?.label ?? '')
    }
  } else {
    console.log('字典', dictName, '是系统字典')
    options = sysDict.value[dictName]?.map((item: { dictLabel: string, dictValue: string }) => ({ label: t(item.dictLabel), value: item.dictValue }))
    console.log('options', options)
    render = (row: any) => {
      return t(options?.find((item: { value: string, label: string }) => item.value === row[attrName])?.label ?? '')
    }
  }
  return { options, render }
}

export async function metaTableBff(data: any, dynCode: string, handleCheck?: (row: any) => void) {
  if (!data.value) {
    window.$message.error('fetch data error')
    return {
      url: {
        page: '',
      },
      columns: [],
      formConfigs: [],
      raw: {},
    }
  }

  const result = await process(data.value.fieldList, dynCode, handleCheck)
  result.raw = data.value
  bffCache.set(dynCode, result)
  if (data.value.childForm) {
    result.child = {
      ...await process(data.value.childForm.fieldList, data.value.childForm.dynCode),
      subMomKey: data.value.childForm.subMomKey,
      momKey: data.value.childForm.momKey,
      dynCode: data.value.childForm.dynCode,
    }
    bffCache.set(data.value.childForm.dynCode, {
      ...result.child,
      raw: data.value.childForm,
    })
  }
  return result
}