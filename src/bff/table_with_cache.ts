import { useDict } from '@/hooks/useDict'
import type { Columns } from '@/utils/entity'
import { t } from '@/modules/i18n'
import type { FormConfig } from '@/components/custom/Dialog/FormDialogV2'
import type { FormSchema } from '@/components'
import { useRequest } from '@/hooks/useRequest'
import type { URL } from '@/hooks/useTableType'
import { 
  getCachedDynTable, 
  cacheDynTable, 
  listenToTableUpdates,
  getDynTableWithCache,
  TableData 
} from '../../bff/dyn_table_worker'

export interface ChildExt {
  subMomKey: string
  momKey: string
  dynCode: string
}

export interface TableBffReturn {
  url: URL
  columns: Columns[]
  formConfigs: FormConfig[]
  child?: TableBffReturn & ChildExt
  raw: any
}

const serverFormTypeMap: Record<string, FormSchema[number]['type']> = {
  'text': 'input',
  'number': 'input-number',
  'select': 'select',
  'radio': 'radio',
  'checkbox': 'checkbox-group',
  'date': 'date',
  'date-time': 'date-time',
}

// 内存缓存，快速访问
const bffCache = new Map<string, any>()

// 注册更新监听器的映射
const updateListeners = new Map<string, () => void>()

/**
 * 注册表更新监听器
 * @param dynCode 动态表代码
 */
function registerUpdateListener(dynCode: string) {
  // 避免重复注册
  if (updateListeners.has(dynCode)) return;
  
  const removeListener = listenToTableUpdates(dynCode, (updatedData) => {
    // 当收到更新事件时，重新处理表数据
    metaTableBff({ value: updatedData }, dynCode, false).then((result) => {
      // 更新内存缓存
      bffCache.set(dynCode, result);
      console.log(`Dynamic table ${dynCode} updated from server`);
    });
  });
  
  updateListeners.set(dynCode, removeListener);
}

/**
 * 从服务器获取动态表定义
 * @param dynCode 动态表代码
 */
async function fetchDynTableFromServer(dynCode: string): Promise<TableData> {
  const { data } = await useRequest<any>(`/devtools/dynTable/form/${dynCode}`);
  return data;
}

/**
 * @param fieldList 动态表核心字段
 * @returns configList
 */
export async function tableBff(dynCode: string): Promise<TableBffReturn> {
  // 先检查内存缓存
  if (bffCache.has(dynCode)) {
    return bffCache.get(dynCode);
  }
  
  // 注册更新监听器
  registerUpdateListener(dynCode);
  
  // 获取数据，优先使用缓存
  const { data, fromCache } = await getDynTableWithCache(dynCode, fetchDynTableFromServer);
  
  if (!data) {
    throw new Error(`Failed to get dynamic table data for ${dynCode}`);
  }
  
  // 处理数据
  const result = await metaTableBff({ value: data }, dynCode, !fromCache);
  return result;
}

async function process(fieldList: any[], dynCode: string): Promise<TableBffReturn> {
  const columns: Columns[] = [];
  const formSchema: FormSchema = [];
  for (const field of fieldList) {
    let columnOptions;
    let columnRender;
    if (field.dictName) {
      const { options, render } = await getDictOptionsAndRender(field.dictName, field.attrName);
      columnOptions = options;
      columnRender = render;
    }
    if (field.isList) {
      const columnItem: Columns = {
        title: t(field.columnLabel),
        key: field.attrName,
        query:
          field.isQuery ?
            field.queryLink === 'eq' ?
              getQueryType(field.formType)
              : 'input'
            : undefined,
        width: field.columnWidth ? Number(field.columnWidth) : undefined,
        sort: field.isSortable ? 'on' : 'off',
        superQuery: field.superQuery ? field.dictName ? 'select' : 'input' : undefined,
        options: columnOptions,
        render: columnRender,
      };
      columns.push(columnItem);
    }
    if (field.isForm) {
      const rules: FormSchema[number]['rules'] = {};
      if (field.isRequired) {
        rules.required = true;
      }
      if (field.validator) {
        try {
          // 尝试将验证器转换为正则表达式
          // 如果验证器已经是合法的正则表达式字符串，则直接使用
          // 否则当作普通字符串处理
          const regex = field.validator;
          new RegExp(regex); // 测试是否可以构造正则表达式
          rules.regex = regex;
          rules.required = true;
        } catch (e) {
          // 可以设置一个默认的正则或者标记为无效
          // rules.regex = '.*' // 设置一个总是匹配的正则
        }
      }
      formSchema.push({
        label: t(field.columnLabel),
        prop: field.attrName,
        type: serverFormTypeMap[field.formType],
        rules: rules as FormSchema[number]['rules'],
        options: columnOptions,
        default: field.initialValue,
        disabled: field.isEdit === false,
      });
    }
  }

  return {
    url: {
      page: `/virtual/${dynCode}/page`,
      exportTable: `/virtual/${dynCode}/exportTable`,
      importTable: `/virtual/${dynCode}/importTable`,
      importTemplate: `/virtual/${dynCode}/importTemplate`,
    },
    columns,
    formConfigs: [
      {
        key: 'new',
        config: {
          formSchema,
          api: `/virtual/${dynCode}/save`,
        },
      },
      {
        key: 'edit',
        config: {
          formSchema,
          api: `/virtual/${dynCode}/update`,
        },
      },
    ],
    raw: {}
  };
}

function getQueryType(formType: string) {
  if (formType === 'select') {
    return 'select';
  }
  if (formType === 'date') {
    return 'date';
  }
  if (formType === 'date-range') {
    return 'date-range';
  }
  return 'input';
}

export function refreshTableBff(dynCode: string) {
  bffCache.delete(dynCode);
  // 强制从服务器重新获取
  return tableBff(dynCode);
}

async function getDictOptionsAndRender(dictName: string, attrName: string): Promise<{ options: { label: string, value: string }[], render: (row: any) => string }> {
  const { sysDict } = useDict();
  let options: { label: string, value: string }[] = [];
  let render: (row: any) => string = () => '';
  if (dictName.startsWith('$.')) {
    const { data } = await useRequest<any>(`/sys/dict/type/dictTable/${dictName.replace('$.', '')}`);
    if (data.value) {
      options = data.value.map((item: { code: string, name: string }) => ({ label: t(item.name), value: item.code }));
      render = (row: any) => t(options.find((item: { value: string, label: string }) => item.value === row[attrName])?.label ?? '');
    }
  } else {
    options = sysDict.value[dictName]?.map((item: { dictLabel: string, dictValue: string }) => ({ label: t(item.dictLabel), value: item.dictValue }));
    render = (row: any) => t(options?.find((item: { value: string, label: string }) => item.value === row[attrName])?.label ?? '');
  }
  return { options, render };
}

export async function metaTableBff(data: any, dynCode: string, shouldCache: boolean = true) {
  if (!data.value) {
    window.$message.error('fetch data error');
    return {
      url: {
        page: '',
      },
      columns: [],
      formConfigs: [],
      raw: {},
    };
  }

  const result = await process(data.value.fieldList, dynCode);
  result.raw = data.value;
  
  // 更新内存缓存
  bffCache.set(dynCode, result);
  
  // 如果需要，更新IndexedDB缓存
  if (shouldCache) {
    // 使用当前时间作为updateTime，如果服务器没有提供的话
    const updateTime = data.value.updateTime || Date.now();
    await cacheDynTable(dynCode, data.value, updateTime);
  }
  
  // 注册子表的更新监听器
  if (data.value.childForm) {
    registerUpdateListener(data.value.childForm.dynCode);
    
    result.child = {
      ...await process(data.value.childForm.fieldList, data.value.childForm.dynCode),
      subMomKey: data.value.childForm.subMomKey,
      momKey: data.value.childForm.momKey,
      dynCode: data.value.childForm.dynCode,
    };
    
    // 更新子表的内存缓存
    bffCache.set(data.value.childForm.dynCode, {
      ...result.child,
      raw: data.value.childForm,
    });
    
    // 如果需要，更新子表的IndexedDB缓存
    if (shouldCache) {
      const childUpdateTime = data.value.childForm.updateTime || Date.now();
      await cacheDynTable(data.value.childForm.dynCode, data.value.childForm, childUpdateTime);
    }
  }
  
  return result;
}

// 在组件卸载时清理监听器的方法
export function cleanupTableListeners() {
  updateListeners.forEach(removeListener => removeListener());
  updateListeners.clear();
} 