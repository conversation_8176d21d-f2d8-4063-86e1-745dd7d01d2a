import { useRequest } from '@/hooks/useRequest'
import type { TableBffReturn } from './table'
import { metaTableBff, bffCache } from './table'

/**
 * 基础表格BFF服务
 * @param dynCode 动态表格代码
 * @returns TableBffReturn
 */
export async function bareTableBff(dynCode: string): Promise<TableBffReturn> {
  if (bffCache.has(dynCode)) {
    return bffCache.get(dynCode)
  }
  const { data } = await useRequest<any>(`/devtools/dynTable/form/${dynCode}`)
  return metaTableBff(data, dynCode)
}

export { refreshTableBff } from './table' 