/// <reference path="../global.d.ts"/>

/** 用户数据库表字段 */
namespace Entity {
  interface User {
    id: string // 用户ID
    username: string // 用户名
    realName: string // 真实姓名
    email: string // 邮箱
    mobile: string // 手机号码
    gender: number // 性别 (0: 男, 1: 女)
    deptId: string // 部门ID
    roleIdList: string[] // 角色ID列表
    superAdmin: number // 是否超级管理员 (1: 是, 0: 否)
    wareHouseList: string[] // 仓库列表
    remark: string // 备注
    appLanguage: string // 应用语言
    appParams: {
      [prop: string]: {
        warehouseList: string[]
        currentWarehouse: string
      }
    }
    appList: string[] // 应用列表
  }

}
