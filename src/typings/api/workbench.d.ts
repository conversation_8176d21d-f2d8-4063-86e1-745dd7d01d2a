/// <reference path="../global.d.ts" />

namespace Api {
  namespace Workbench {
    interface Data {
      systemInfo: {
        activeUser: number
        totalUser: number
        visitNum: number
        collectionNum: number
        orderNum: number
        todoNum: number
      }
      announcement: {
        type: 'message' | 'notice' | 'activity'
        id: number
        title: string
      }[]
      timeline: {
        avatar: string
        type: 'success' | 'info' | 'warning' | 'error'
        title: string
        content: string
        time: string
      }[]
      transactionRecords: {
        id: number
        name: string
        startTime: string
        endTime: string
        progress: number
        status: string
      }[]
    }
  }
}
