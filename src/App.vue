<script setup lang="ts">
import { darkTheme } from 'naive-ui'
import { naiveI18nOptions } from '@/utils'
import { useApp, useWS } from '@/hooks'

const { state, colorMode } = useApp()

const naiveLocale = computed(() => {
  let lang = state.value.lang
  if (!lang)
    lang = 'zhCN'
  return naiveI18nOptions[lang as keyof typeof naiveI18nOptions] ? naiveI18nOptions[lang as keyof typeof naiveI18nOptions] : naiveI18nOptions.enUS
})

</script>

<template>
  <n-config-provider class="wh-full" :theme="colorMode === 'dark' ? darkTheme : null"
    :locale="naiveLocale.locale" :date-locale="naiveLocale.dateLocale" :theme-overrides="state.theme" :inline-theme-disabled="true">
    <n-modal-provider>
      <naive-provider>
        <router-view />
        <Watermark :show-watermark="state.showWatermark" />
      </naive-provider>
    </n-modal-provider>
  </n-config-provider>
</template>
