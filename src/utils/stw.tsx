import { useApp, useAuth, useRequest } from '@/hooks'
import { t } from '@/modules/i18n'
import { NButton, NForm, NFormItem, NInput, NCheckbox, NConfigProvider } from 'naive-ui'
import { local } from '@/utils'
import { initSystemFlag } from '@/init'

let isStop = false

const LoginForm = defineComponent({
  props: {
    onSuccess: Function,
  },
  setup(props) {
    const formRef = ref()
    const formValue = ref({
      username: '',
      password: '',
    })
    const isRemember = ref(false)
    const isLoading = ref(false)

    const rules = {
      username: {
        required: true,
        trigger: 'blur',
        message: t('login.accountRuleTip'),
      },
      password: {
        required: true,
        trigger: 'blur',
        message: t('login.passwordRuleTip'),
      },
    }

    onMounted(() => {
      checkUserAccount()
    })

    function checkUserAccount() {
      const loginAccount = local.get('loginAccount')
      if (!loginAccount)
        return

      formValue.value = loginAccount
      isRemember.value = true
    }

    return () => (
      <NForm ref={formRef} rules={rules} model={formValue.value}>
        <NFormItem path="username">
          <NInput 
            v-model:value={formValue.value.username}
            placeholder={t('login.accountPlaceholder')}
          />
        </NFormItem>
        <NFormItem path="password">
          <NInput
            v-model:value={formValue.value.password}
            type="password"
            placeholder={t('login.passwordPlaceholder')}
            showPasswordOn="click"
          />
        </NFormItem>
        <div class="flex justify-between items-center mb-4">
          <NCheckbox v-model:checked={isRemember.value}>
            {t('login.rememberMe')}
          </NCheckbox>
        </div>
        <NButton
          block
          loading={isLoading.value}
          type='primary'
          onClick={async () => {
            await formRef.value?.validate()
            isLoading.value = true
              const { data, error } = await useRequest<string>('/login')
                .post(formValue.value)

              if (!error.value && data.value) {
                if (isRemember.value)
                  local.set('loginAccount', formValue.value)
                else
                  local.remove('loginAccount')

                const { setToken } = useAuth()
                setToken(data.value)
                props.onSuccess?.()
                isLoading.value = false
                if (!initSystemFlag) {
                  window.location.reload()
                }
              }
          }}
        >
          {t('login.signIn')}
        </NButton>
      </NForm>
    )
  },
})

async function stw() {
  if (isStop)
    return
  isStop = true
  const { state } = useApp()
  const modal = window.$dialog.create({
    title: t('login.signInTitle'),
    closable: false,
    maskClosable: false,
    icon: () => h('div', { class: 'i-icon-park-outline-people-bottom-card text-#d03050' }),
    content: () => h(NConfigProvider, {
      themeOverrides: state.value.theme,
    }, {
      default: () => h(LoginForm, {
        onSuccess: () => {
          modal.destroy()
        },
      }),
    }),
  })
}

export { stw }
