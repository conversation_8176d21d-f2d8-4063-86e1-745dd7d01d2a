import type { NDateLocale, NLocale } from 'naive-ui'
import { dateZhCN, zhCN } from 'naive-ui'
import { i18n, t } from '@/modules/i18n'

export function setLocale(locale: App.lang) {
  i18n.global.locale.value = locale
  document.documentElement.lang = locale.toLowerCase()
}

export const naiveI18nOptions: Record<App.lang, { locale: NLocale | null, dateLocale: NDateLocale | null }> = {
  zhCN: {
    locale: zhCN,
    dateLocale: dateZhCN,
  },
  enUS: {
    locale: null,
    dateLocale: null,
  },
}

export function getAllKeys(obj: any, prefix = ''): string[] {
  return Object.entries(obj).reduce((acc: string[], [key, value]) => {
    const currentKey = prefix ? `${prefix}.${key}` : key
    if (typeof value === 'object' && value !== null) {
      return [...acc, ...getAllKeys(value, currentKey)]
    }
    return [...acc, currentKey]
  }, [])
}

export const i18nOptions = computed(() => {
  const zhMessages = i18n.global.getLocaleMessage('zhCN')
  const keys = getAllKeys(zhMessages)

  return keys.map(key => ({
    label: `${t(key, 'zhCN')} -- ${key}`,
    value: key,
  }))
})

export function splitKey(serverKey: string): {
  key: string
  rest: string[]
} {
  const [key, ...rest] = serverKey.split('|')
  if (!key) {
    return {
      key: '',
      rest: [],
    }
  }
  return {
    key,
    rest,
  }
}