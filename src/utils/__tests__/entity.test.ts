import { describe, expect, it } from 'vitest'
import { Columns } from '../entity'

describe('Columns type', () => {
  it('should have required properties', () => {
    const column: Columns = {
      title: 'Name',
      key: 'name',
      query: 'input',
      options: [{ label: 'Option 1', value: 1 }],
      translate: 'on',
      sort: 'on',
      queryWidth: 100,
      width: 200,
    }
    expect(column.title).toBe('Name')
    expect(column.key).toBe('name')
  })

  it('should allow optional properties to be undefined', () => {
    const column: Columns = {
      title: 'Age',
      key: 'age',
      query: 'input',
      options: [],
      translate: 'off',
      sort: 'off',
      queryWidth: undefined,
      width: undefined,
    }
    expect(column.title).toBe('Age')
    expect(column.queryWidth).toBeUndefined()
    expect(column.width).toBeUndefined()
  })
}) 