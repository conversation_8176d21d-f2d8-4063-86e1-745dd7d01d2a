import { describe, expect, it, beforeAll } from 'vitest'
import { encrypt } from '../crypto'

beforeAll(() => {
  // Mock window.crypto
  global.window = {
    crypto: {
      subtle: {
        digest: async () => new Uint8Array([1, 2, 3]).buffer
      }
    }
  } as any
})

describe('encrypt', () => {
  it('should return a promise', async () => {
    const result = await encrypt('test')
    expect(result).toBeDefined()
    expect(result instanceof ArrayBuffer).toBe(true)
  })
}) 