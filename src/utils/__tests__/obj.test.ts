import { describe, expect, it } from 'vitest'
import { filterEmptyValues, diffObj, diffObjDep } from '../obj'

describe('filterEmptyValues', () => {
  it('should filter out empty values', () => {
    const input = { a: 1, b: '', c: null, d: undefined, e: 2 }
    const result = filterEmptyValues(input)
    expect(result).toEqual({ a: 1, e: 2 })
  })
})

describe('diffObj', () => {
  it('should return differences between two objects', () => {
    const oldObj = { a: 1, b: 2 }
    const newObj = { a: 1, b: 3, c: 4 }
    const result = diffObj(newObj, oldObj)
    expect(result).toEqual({ b: 3, c: 4 })
  })
})

describe('diffObjDep', () => {
  it('basic type', () => {
    const oldObj = {
      a: 1,
      b: 2,
    }
    const newObj = { a: 1, b: 3, c: 4 }
    const result = diffObjDep(newObj, oldObj)
    expect(result).toEqual({ b: 3, c: 4 })
  })

  it('nested basic type', () => {
    const oldObj = {
      a: 1,
      b: 2,
      c: {
        d: 2,
        e: 3,
      }
    }
    const newObj = { a: 1, b: 3, c: { d: 4, e: 3 } }
    const result = diffObjDep(newObj, oldObj)
    expect(result).toEqual({ b: 3, c: { d: 4 } })
  })

  it('basic array', () => {
    const oldObj = {
      a: [1, 2, 3],
    }
    const newObj = { a: [1, 2, 3, 4] }
    const result = diffObjDep(newObj, oldObj)
    expect(result).toEqual({ a: [1, 2, 3, 4] })
  })

  it('basic object array', () => {
    const oldObj = {
      a: [{ id: 1, b: 2 }, { id: 2, b: 3 }],
    }
    const newObj = { a: [{ id: 1, b: 3 }, { b: 4 }] }
    const result = diffObjDep(newObj, oldObj)
    expect(result).toEqual({
      a: {
        insList: [{ b: 4 }],
        delList: [{ id: 2, b: 3 }],
        updList: [{ id: 1, b: 3 }],
      }
    })

    const oldObj2 = {
      // 进入 对象类型 分支
      a: {
        // 进入 数组(引用类型) 分支
        e: [
          { id: 1, b: 2 },
          { id: 2, b: 3 },
        ],
      }
    }
    const newObj2 = {
      a: {
        e: [{ id: 1, b: 3 }, { b: 4 }],
      }
    }
    const result2 = diffObjDep(newObj2, oldObj2)
    expect(result2).toEqual({
      a: {
        e: {
          insList: [{ b: 4 }],
          delList: [{ id: 2, b: 3 }],
          updList: [{ id: 1, b: 3 }],
        }
      }
    })
  })
})

describe('diffObjDep performance', () => {
  it('should handle complex nested objects efficiently', () => {
    const oldObj = {
      a: {
        b: {
          c: {
            d: {
              e: 1,
            },
            f: [
              { id: 1, g: 1 }
            ],
          }
        },
        g: [1, 2, 3],
      }
    }

    const newObj = {
      a: {
        b: {
          c: {
            d: {
              e: 2,
            },
            f: [
              { id: 1, g: 3 }
            ],
          }
        },
        g: [1, 2, 3, 4],
      }
    }

    const result = diffObjDep(newObj, oldObj)
    expect(result).toEqual({
      a: {
        b: {
          c: {
            d: {
              e: 2,
            },
            f: {
              updList: [{ id: 1, g: 3 }],
              delList: [],
              insList: [],
            }
          },
        },
        g: [1, 2, 3, 4],
      }
    })
  });
});
