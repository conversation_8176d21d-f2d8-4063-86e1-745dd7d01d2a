import { describe, expect, it } from 'vitest'
import { arrayToTree } from '../array'

describe('arrayToTree', () => {
  it('should convert flat array to tree structure', () => {
    const input = [
      { id: 1, pid: null, name: 'A' },
      { id: 2, pid: 1, name: 'B' },
      { id: 3, pid: 1, name: 'C' },
      { id: 4, pid: 2, name: 'D' },
    ]
    const expected = [
      {
        id: 1,
        pid: null,
        name: 'A',
        children: [
          {
            id: 2,
            pid: 1,
            name: 'B',
            children: [
              { id: 4, pid: 2, name: 'D' }
            ]
          },
          { id: 3, pid: 1, name: 'C' }
        ]
      }
    ]
    expect(arrayToTree(input)).toEqual(expected)
  })
}) 