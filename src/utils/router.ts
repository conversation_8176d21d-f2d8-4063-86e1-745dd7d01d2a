import type { MenuOption } from 'naive-ui'
import { clone, min, omit, pick } from 'radash'
import { RouterLink } from 'vue-router'
import type { Component } from 'vue'
import { createCustomComponent } from './comp'
import { useSystem, useTab } from '@/hooks'
import { IconRender, arrayToTree } from '@/utils'
import { t } from '@/modules/i18n'
import type { FlatRoute, MetaKeys, Route } from '@/types/routerType'
import { pageBucket } from '@/views/bucket'
import { router } from '@/router'
import { systemConfig } from '@/config/system'
import { DynTable } from '@/components'

const metaFields: MetaKeys[]
  = ['openStyle', 'title', 'icon', 'requiresAuth', 'roles', 'keepAlive', 'hide', 'order', 'href', 'activeMenu', 'withoutTab', 'pinTab', 'menuType', 'dynCode', 'menuId']

/** transform the flat route to the standard Vue route */
function standardizedRoutes(route: FlatRoute[]): Route[] {
  return clone(route).map((i) => {
    const route = omit(i, metaFields)

    Reflect.set(route, 'meta', pick(i, metaFields))
    return route
  }) as Route[]
}

export function recoverRoutes(routes: FlatRoute[]) {
  // Structure the meta field
  let resultRouter = standardizedRoutes(routes)
  console.log('resultRouter', resultRouter)

  setRedirect(resultRouter)
  // Get Vue page component from bucket
  resultRouter.forEach((item: Route) => {
    if (item.componentPath && !item.redirect) {
      item.component = createCustomComponent(item.path, pageBucket[item.componentPath] || DynTable)
    }
    try {
      router.addRoute('root', item as any)
    } catch (error) {
      // 检查是否存在路径冲突
      const existingRoutes = router.getRoutes()
      const conflictRoute = existingRoutes.find(route => route.path === item.path)
      if (conflictRoute) {
        console.error(`路径冲突: ${item.path} 已存在`, conflictRoute)
      }
    }
  })
  console.log('所有已注册的路由:', router.getRoutes().map(r => ({ path: r.path, name: r.name })))

  const { currentSystem } = useSystem()
  // const hash = window.location.hash
  // console.log(hash)
  // const redirect = hash.length > 2 ? hash.slice(1) : systemConfig.list?.find(i => i.key === currentSystem.value)?.homePath
  // Auto generate root route
  // Set the correct redirect path for the route
  router.addRoute('root', {
    path: '/appRoot',
    name: 'appRoot',
    redirect: systemConfig.list?.find(i => i.key === currentSystem.value)?.homePath,
    meta: {
      title: '',
      icon: 'icon-park-outline:home',
    },
    children: [],
  } as any)
}

function setRedirect(routes: Route[]) {
  routes.forEach((route) => {
    if (route.children) {
      if (!route.redirect) {
        // Filter out a collection of child elements that are not hidden
        const visibleChilds = route.children.filter(child => !child.meta.hide)

        // Redirect page to the path of the first child element by default
        let target = visibleChilds[0]

        // Filter out pages with the order attribute
        const orderChilds = visibleChilds.filter(child => child.meta.order)

        if (orderChilds.length > 0)
          target = min(orderChilds, i => i.meta.order!) as Route

        if (target)
          route.redirect = target.path
      }

      setRedirect(route.children)
    }
  })
}

/* Generate side menu data */
export function createMenus(userRoutes: FlatRoute[]) {
  const resultMenus = standardizedRoutes(userRoutes)

  // Filter menus that do not need to be displayed
  const visibleMenus = resultMenus.filter(route => !route.meta.hide)

  // Generate side menu
  return arrayToTree(transformRoutesToMenus(visibleMenus))
}

// Render the returned routing table as a sidebar
function transformRoutesToMenus(userRoutes: Route[]) {
  return userRoutes
    //  Sort the menu according to the order size
    .sort((a, b) => {
      if (a.meta && a.meta.order && b.meta && b.meta.order)
        return a.meta.order - b.meta.order
      else if (a.meta && a.meta.order)
        return -1
      else if (b.meta && b.meta.order)
        return 1
      else return 0
    })
    // Convert to side menu data structure
    .map((item) => {
      const target: MenuOption = {
        id: item.id,
        pid: item.pid,
        label:
          (!item.meta.menuType || item.meta.menuType === 'page')
            ? () =>
              h(
                RouterLink,
                {
                  to: {
                    path: item.path,
                  },
                },
                { default: () => t(item.name, item.meta.title) },
              )
            : () => t(item.name, item.meta.title),
        key: item.path,
        icon: item.meta.icon ? IconRender(item.meta.icon) : undefined,
      }
      return target
    })
}

interface GotoOptions {
  component?: Component
  query?: Record<string, any>
  params?: Record<string, any>
  title?: string
  meta?: Record<string, any>
  disableFrom?: boolean
  action?: 'replace' | 'push'
}

/**
 * Dynamic register route and navigate to the page
 * @param path - The path to navigate to
 * @param options - The options to navigate to, including title, query
 * @param options.title - The title of the page
 * @param options.query - The query parameters to navigate to
 */
export function goto(path: string, options: GotoOptions = {}) {
  // join path with current system
  const { currentSystem } = useSystem()
  if (!path.startsWith(`/${currentSystem.value}`))
    path = `/${currentSystem.value}${path}`
  // check if router already has the path
  const route = router.hasRoute(path)
  const action = options.action || 'push'
  const routerAction = action === 'replace' ? router.replace : router.push
  if (route) {
    routerAction({
      path,
      query: {
        ...options.query,
        from: router.currentRoute.value.path
      },
    })
    return
  }
  // Check if bucket has the component
  const component = options.component || pageBucket[path]
  if (!component) {
    window.$message.error(t('error.componentNotFound'))
    // Dynamic register route
    return
  }
  router.addRoute('appRoot', {
    path,
    name: path,
    component: createCustomComponent(path, component as Component),
    meta: {
      title: options.title || '',
      icon: 'i-icon-park-outline-browser',
      dynamic: true,
      ...options.meta,
    },
  })
  routerAction({
    path,
    query: {
      ...options.query,
      from: options.disableFrom ? undefined : router.currentRoute.value.path
    },
  })
}

/**
 * Dynamic register route and navigate to the page
 * @param path - The path to navigate to
 * @param options - The options to navigate to, including title, query
 * @param options.title - The title of the page
 * @param options.query - The query parameters to navigate to
 */
export function routerPush(path: string, options: GotoOptions = {}) {
  const action = options.action || 'push'
  const routerAction = action === 'replace' ? router.replace : router.push
  // check if router already has the path
  if (options.component) {
    router.addRoute('appRoot', {
      path,
      name: path,
      component: createCustomComponent(path, options.component as Component),
      meta: {
        title: options.title || '',
        icon: 'i-icon-park-outline-browser',
        dynamic: true,
        ...options.meta,
      },
    })
  } else {
    const route = router.hasRoute(path)
    if (route) {
      routerAction({
        path,
        query: {
          ...options.query,
          from: router.currentRoute.value.path
        },
      })
      return
    }
    // Check if bucket has the component
    const component = options.component || pageBucket[path]
    if (!component) {
      window.$message.error(t('error.componentNotFound'))
      // Dynamic register route
      return
    }
    router.addRoute('appRoot', {
      path,
      name: path,
      component: createCustomComponent(path, component as Component),
      meta: {
        title: options.title || '',
        icon: 'i-icon-park-outline-browser',
        dynamic: true,
        ...options.meta,
      },
    })
  }

  routerAction({
    path,
    query: {
      ...options.query,
      from: options.disableFrom ? undefined : router.currentRoute.value.path
    },
  })
}

export async function goback(closeTabSignal: boolean = false) {
  const { closeTab } = useTab()
  if (closeTabSignal) {
    await closeTab(router.currentRoute.value.fullPath)
  } else {
    router.back()
  }
}
