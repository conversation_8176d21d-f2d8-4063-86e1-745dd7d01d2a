const STORAGE_PREFIX = 'inform_'

interface StorageData<T> {
  value: T
  expire: number | null
}

// 创建一个内存存储作为 localStorage 的替代
const createMemoryStorage = () => {
  const storage = new Map<string, string>()
  return {
    getItem: (key: string) => storage.get(key) ?? null,
    setItem: (key: string, value: string) => storage.set(key, value),
    removeItem: (key: string) => storage.delete(key),
    clear: () => storage.clear(),
  }
}

// 判断是否在浏览器环境中
const isBrowser = typeof window !== 'undefined'
const localStorage = isBrowser ? window.localStorage : createMemoryStorage()
const sessionStorage = isBrowser ? window.sessionStorage : createMemoryStorage()

/**
 * LocalStorage部分操作
 */
function createLocalStorage<T extends Storage.Local>() {
  // 默认缓存期限为7天
  function set<K extends keyof T>(key: K, value: T[K], expire: number = 60 * 60 * 24 * 7) {
    const storageData: StorageData<T[K]> = {
      value,
      expire: new Date().getTime() + expire * 1000,
    }
    const json = JSON.stringify(storageData)
    localStorage.setItem(`${STORAGE_PREFIX}${String(key)}`, json)
  }

  function get<K extends keyof T>(key: K) {
    const json = localStorage.getItem(`${STORAGE_PREFIX}${String(key)}`)
    if (!json)
      return null

    const storageData: StorageData<T[K]> | null = JSON.parse(json)

    if (storageData) {
      const { value, expire } = storageData
      if (expire === null || expire >= Date.now())
        return value
    }
    remove(key)
    return null
  }

  function remove(key: keyof T) {
    localStorage.removeItem(`${STORAGE_PREFIX}${String(key)}`)
  }

  const clear = () => localStorage.clear()

  return {
    set,
    get,
    remove,
    clear,
  }
}

/**
 * sessionStorage部分操作
 */
function createSessionStorage<T extends Storage.Session>() {
  function set<K extends keyof T>(key: K, value: T[K]) {
    const json = JSON.stringify(value)
    sessionStorage.setItem(`${STORAGE_PREFIX}${String(key)}`, json)
  }

  function get<K extends keyof T>(key: K) {
    const json = sessionStorage.getItem(`${STORAGE_PREFIX}${String(key)}`)
    if (!json)
      return null

    const storageData: T[K] | null = JSON.parse(json)

    if (storageData)
      return storageData

    return null
  }

  function remove(key: keyof T) {
    sessionStorage.removeItem(`${STORAGE_PREFIX}${String(key)}`)
  }

  const clear = () => sessionStorage.clear()

  return {
    set,
    get,
    remove,
    clear,
  }
}

export const local = createLocalStorage()
export const session = createSessionStorage()
