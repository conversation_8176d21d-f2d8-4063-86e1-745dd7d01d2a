const isSupportVoice = 'speechSynthesis' in window

// 封装了一个浏览器的原生方法
function playVoice(text: string, duration: number = 5000) {
  if (!isSupportVoice) {
    console.warn('isSupportVoice', isSupportVoice)
    return
  }
  const utterance = new SpeechSynthesisUtterance(text)
  window.speechSynthesis.speak(utterance)

  // 停止语音播报
  setTimeout(() => {
    window.speechSynthesis.cancel()
  }, duration)
}

const isSupportBeep = 'AudioContext' in window || 'webkitAudioContext' in window

function playBeep(duration: number = 5) {
  // 检查浏览器是否支持 Web Audio API
  if (!isSupportBeep) {
    console.warn('isSupportBeep', isSupportBeep)
    return;
  }

  const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
  const audioContext = new AudioContext();

  // 创建一个振荡器节点，用于生成音频信号
  const oscillator = audioContext.createOscillator();
  // 创建一个增益节点，用于控制音量
  const gainNode = audioContext.createGain();

  // 设置振荡器类型 (例如，正弦波 sine, 方波 square, 锯齿波 sawtooth, 三角波 triangle)
  oscillator.type = 'sine';
  // 设置蜂鸣的频率 (Hz)
  oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // 例如 440 Hz (A4音)

  // 将振荡器连接到增益节点
  oscillator.connect(gainNode);
  // 将增益节点连接到音频输出 (扬声器)
  gainNode.connect(audioContext.destination);

  // 设置初始音量
  gainNode.gain.setValueAtTime(1, audioContext.currentTime);

  // 渐弱音量以避免咔嗒声
  gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);

  // 启动振荡器
  oscillator.start();

  // 在指定时间后停止振荡器
  oscillator.stop(audioContext.currentTime + duration);

  // 释放资源 (可选，但推荐)
  oscillator.onended = () => {
    audioContext.close();
  };
}

export {
  playVoice,
  playBeep,
}