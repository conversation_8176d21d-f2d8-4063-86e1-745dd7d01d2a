/**
 * 打印当前时间和上下文信息
 * @param message 要打印的消息
 * @param data 要打印的数据
 * @param level 日志级别，默认为 'log'
 */
export function printCurrent(message?: string, data?: any, level: 'log' | 'info' | 'warn' | 'error' = 'log') {
  const now = new Date().toLocaleString()
  
  // 获取调用栈信息
  const stackTrace = new Error().stack || ''
  const stackLines = stackTrace.split('\n').slice(2) // 移除前两行（Error和当前函数）
  const callerInfo = stackLines[0]?.trim() || '未知位置'
  
  // 提取文件名和行号信息（如果可用）
  let fileName = '未知文件'
  let lineNumber = '未知行号'
  
  // 尝试从调用栈中提取文件名和行号
  const fileMatch = callerInfo.match(/\((.+?):(\d+):(\d+)\)/) || callerInfo.match(/at (.+?):(\d+):(\d+)/)
  if (fileMatch && fileMatch.length >= 3) {
    fileName = fileMatch[1].split('/').pop() || '未知文件'
    lineNumber = fileMatch[2]
  }
  
  const contextInfo = `[${fileName}:${lineNumber}]`
  const formattedMessage = `${now} ${contextInfo} ${message || ''}`
  
  // 根据级别选择不同的日志方法
  switch (level) {
    case 'info':
      console.info(formattedMessage, data || '')
      break
    case 'warn':
      console.warn(formattedMessage, data || '')
      break
    case 'error':
      console.error(formattedMessage, data || '')
      break
    default:
      console.log(formattedMessage, data || '')
  }
  
  return { time: now, fileName, lineNumber, message, data }
}

/**
 * 创建一个带有命名空间的日志工具
 * @param namespace 命名空间名称，通常是模块名
 * @returns 日志工具对象
 */
export function createLogger(namespace: string) {
  return {
    log: (message?: string, data?: any) => printCurrent(`[${namespace}] ${message}`, data, 'log'),
    info: (message?: string, data?: any) => printCurrent(`[${namespace}] ${message}`, data, 'info'),
    warn: (message?: string, data?: any) => printCurrent(`[${namespace}] ${message}`, data, 'warn'),
    error: (message?: string, data?: any) => printCurrent(`[${namespace}] ${message}`, data, 'error')
  }
}

/**
 * 计时器类，用于记录不同阶段的耗时
 */
export class Timer {
  private startTime: number
  private marks: Record<string, number> = {}
  private namespace: string
  
  /**
   * 创建一个计时器
   * @param namespace 计时器名称
   */
  constructor(namespace: string) {
    this.startTime = performance.now()
    this.namespace = namespace
    this.marks['start'] = this.startTime
    printCurrent(`[${namespace}] 开始计时`, { time: new Date().toISOString() }, 'info')
  }
  
  /**
   * 标记一个时间点
   * @param name 时间点名称
   * @param data 附加数据
   * @returns 从开始到现在的毫秒数
   */
  mark(name: string, data?: any): number {
    const now = performance.now()
    const elapsed = now - this.startTime
    this.marks[name] = now
    
    printCurrent(
      `[${this.namespace}] 标记: ${name}`, 
      { 
        elapsed: `${elapsed.toFixed(2)}ms`, 
        ...data 
      }, 
      'info'
    )
    
    return elapsed
  }
  
  /**
   * 计算两个标记点之间的时间差
   * @param fromMark 起始标记名
   * @param toMark 结束标记名，如果不提供则使用当前时间
   * @returns 两点之间的毫秒数
   */
  between(fromMark: string, toMark?: string): number {
    const fromTime = this.marks[fromMark]
    if (!fromTime) {
      printCurrent(`[${this.namespace}] 警告: 找不到标记 ${fromMark}`, null, 'warn')
      return 0
    }
    
    const toTime = toMark ? this.marks[toMark] : performance.now()
    if (toMark && !this.marks[toMark]) {
      printCurrent(`[${this.namespace}] 警告: 找不到标记 ${toMark}`, null, 'warn')
      return 0
    }
    
    return toTime - fromTime
  }
  
  /**
   * 结束计时并输出所有标记点的耗时
   * @param data 附加数据
   * @returns 总耗时毫秒数
   */
  end(data?: any): number {
    const endTime = performance.now()
    const totalTime = endTime - this.startTime
    this.marks['end'] = endTime
    
    // 计算每个标记点之间的时间
    const markNames = Object.keys(this.marks).sort((a, b) => this.marks[a] - this.marks[b])
    const intervals: Record<string, string> = {}
    
    for (let i = 0; i < markNames.length - 1; i++) {
      const current = markNames[i]
      const next = markNames[i + 1]
      const time = this.between(current, next)
      intervals[`${current} -> ${next}`] = `${time.toFixed(2)}ms`
    }
    
    printCurrent(
      `[${this.namespace}] 计时结束`, 
      { 
        totalTime: `${totalTime.toFixed(2)}ms`, 
        intervals,
        ...data 
      }, 
      'info'
    )
    
    return totalTime
  }
}