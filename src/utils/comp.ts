import type { Component } from 'vue'
import { markRaw } from 'vue'

/**
 * 创建自定义组件
 * @param name 组件名称
 * @param component 组件
 * @returns 
 */
export function createCustomComponent (name: string, component: Component) {
  return {
    name,
    data () {
      return { component: null }
    },
    async created () {
      if (component instanceof Promise) {
        try {
          const module = await component
          this.component = markRaw(module?.default)
        } catch (error) {
        }

        return
      }
      this.component = markRaw(component)
    },
    render () {
      return this.component ? h(this.component) : null
    },
  }
}
