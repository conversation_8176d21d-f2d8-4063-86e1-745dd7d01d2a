import * as v from 'valibot'

/**
 * filter empty values (empty string, undefined, null)
 */
function filterEmptyValues<T extends Record<string, any>>(obj: T): Partial<T> {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (value !== '' && value !== undefined && value !== null) {
      acc[key as keyof T] = value
    }
    return acc
  }, {} as Partial<T>)
}

function diffObj<T extends Record<string, any>>(newObj: T, oldObj: T): Partial<T> {
  const result: Partial<T> = {}

  // 遍历新对象的所有属性
  for (const key in newObj) {
    // 如果是对象自身的属性（非原型链上的属性）
    if (Object.prototype.hasOwnProperty.call(newObj, key)) {
      // 如果值不相等，则记录到结果对象中
      if (newObj[key] !== oldObj[key]) {
        result[key] = newObj[key]
      }
    }
  }

  return result
}

const BasicTypeSchema = v.union([v.string(), v.number(), v.boolean(), v.null(), v.undefined()])
type BasicType = v.InferOutput<typeof BasicTypeSchema>
function isBasicType(value: unknown): value is BasicType {
  return v.is(BasicTypeSchema, value)
}

// Schema for plain objects that should be compared via JSON.stringify
const StringifiablePlainObjectSchema = v.custom(
  (input): input is Record<string, any> =>
    typeof input === 'object' &&
    input !== null &&
    !Array.isArray(input) &&
    Object.getPrototypeOf(input) === Object.prototype,
  'Must be a plain object'
);
// Helper function for this new schema
function isStringifiablePlainObject(value: unknown): value is Record<string, any> {
  return v.is(StringifiablePlainObjectSchema, value);
}

// Updated schema for the values within a BasicObject. Now includes stringifiable plain objects.
const ValueInBasicObjectSchema = v.union([BasicTypeSchema, StringifiablePlainObjectSchema]);

const BasicObjectSchema = v.record(v.string(), ValueInBasicObjectSchema)
type BasicObjectType = v.InferOutput<typeof BasicObjectSchema>
function isBasicObjectType(value: unknown): value is BasicObjectType {
  return v.is(BasicObjectSchema, value) && !Array.isArray(value)
}

function isArray(value: unknown): value is unknown[] {
  return Array.isArray(value)
}

const BasicArraySchema = v.array(BasicTypeSchema)
type BasicArrayType = v.InferOutput<typeof BasicArraySchema>
function isBasicArrayType(value: unknown): value is BasicArrayType {
  return v.is(BasicArraySchema, value)
}

const BasicObjectArraySchema = v.array(BasicObjectSchema)
type BasicObjectArrayType = v.InferOutput<typeof BasicObjectArraySchema>
function isBasicObjectArrayType(value: unknown): value is BasicObjectArrayType {
  return v.is(BasicObjectArraySchema, value)
}

const BasicObjectArrayDiffSchema = v.object({
  insList: BasicObjectArraySchema,
  delList: BasicObjectArraySchema,
  updList: BasicObjectArraySchema,
})
type BasicObjectArrayDiffType = v.InferOutput<typeof BasicObjectArrayDiffSchema>


const ObjectSchema = v.record(v.string(), v.union([
  BasicTypeSchema,
  StringifiablePlainObjectSchema, // Allow plain stringifiable objects as properties of complex objects
  BasicArraySchema,
  BasicObjectArraySchema,
  v.lazy(() => ObjectSchema)
]))
type ObjectType = v.InferOutput<typeof ObjectSchema>
function isObjectType(value: unknown): value is ObjectType {
  return v.is(ObjectSchema, value) && !Array.isArray(value)
}

function diffBasicArray(newArr: BasicArrayType, oldArr: BasicArrayType): BasicArrayType {
  if (!isBasicArrayType(newArr) || !isBasicArrayType(oldArr)) {
    throw new Error('newArr 和 oldArr 必须是基本类型数组')
  }

  const result: BasicArrayType = []

  for (const item of newArr) {
    if (!oldArr.includes(item)) {
      result.push(item)
    }
  }

  for (const item of oldArr) {
    if (!newArr.includes(item)) {
      result.push(item)
    }
  }

  return result
}

function diffObjDep(newObj: ObjectType, oldObj: ObjectType, keepId: boolean = true): Partial<ObjectType> {
  const result: Partial<ObjectType> = {}

  for (const key in newObj) {
    if (key === 'id' && keepId) {
      result[key] = oldObj[key]
      continue
    }
    const newVal = newObj[key]
    const oldVal = oldObj[key]

    if (newVal === undefined && oldVal === undefined) {
      continue
    }
    if (newVal === null && oldVal === null) {
      continue
    }
    if (newVal === undefined && oldVal === null) {
      continue
    }
    if (newVal === null && oldVal === undefined) {
      continue
    }
    if (newVal === '' && oldVal === '') {
      continue
    }
    if (newVal === oldVal) {
      continue
    }
    // 如果旧值不是 null或者undefined，新值是null或者undefined，将值置为null
    if (oldVal !== null && newVal === null) {
      result[key] = null
      continue
    }
    if (oldVal !== undefined && newVal === undefined) {
      result[key] = null
      continue
    }
    // 根据不同的值类型执行不同的操作
    // 1. 基本类型
    if (isBasicType(newVal)) {
      // The initial `newVal === oldVal` check covers this, but being explicit if types are known.
      result[key] = newVal
      continue
    }

    // NEW: 1.5. Stringifiable Plain Objects (e.g., lotJson's value)
    // These are compared as a whole by their stringified representation.
    if (isStringifiablePlainObject(newVal) && isStringifiablePlainObject(oldVal)) {
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
        result[key] = newVal
      }
      continue
    }
    
    // 2. 对象类型 (Complex objects for recursive diffing)
    if (isObjectType(newVal) && isObjectType(oldVal)) {
      const diff = diffObjDep(newVal, oldVal, false) // keepId should be false for nested generic objects
      if (Object.keys(diff).length > 0) {
        result[key] = diff
      }
      continue
    }
    

    // 3. 数组类型, 分别考虑基本类型数组和引用类型数组
    // 在进入数组分支之前，先判断数组长度
    if ((!isArray(newVal)) && (!isArray(oldVal))) {
      // 由于后续会进入数组分支，所以如果这俩都不是数组，就直接跳过
      // This case might indicate a type mismatch not caught above, or simple differing non-array, non-object values.
      // If they are not basic, not stringifiable plain objects, not complex objects, and not arrays,
      // and they are different (passed initial checks), they should be recorded.
      // However, this path is less likely if schemas are well-defined.
      // For safety, if they are different and not handled by specific types, consider assigning newVal.
      if (newVal !== oldVal) { // General fallback for unhandled differing types
          result[key] = newVal;
      }
      continue
    }
    // 如果newObj[key]是数组，oldObj[key]不是数组，则直接将newObj[key]直接插入 insList
    if (isArray(newVal) && !isArray(oldVal)) {
      result[key] = {
        insList: newVal,
        delList: [],
        updList: [],
      }
      continue
    }
    // 如果newObj[key]不是数组，oldObj[key]是数组，则直接将oldObj[key]的每个元素插入 delList
    if (!isArray(newVal) && isArray(oldVal)) {
      result[key] = {
        insList: [],
        delList: oldVal,
        updList: [],
      }
      continue
    }

    // At this point, both newVal and oldVal are arrays.
    const newArr = newVal as unknown[]
    const oldArr = oldVal as unknown[]

    const oldArrLength = oldArr.length
    let oldArrItemType
    const newArrLength = newArr.length
    let newArrItemType
    // 预期两个数组的长度都可以为 0
    // 两个都是 0 的话可以直接跳过
    if (oldArrLength === 0 && newArrLength === 0) {
      continue
    }
    if (oldArrLength > 0) {
      oldArrItemType = typeof oldArr[0]
    }
    if (newArrLength > 0) {
      newArrItemType = typeof newArr[0]
    }
    // 如果两个元素类型都存在，那么预期两个数组的元素类型一致
    if (oldArrItemType && newArrItemType && oldArrItemType !== newArrItemType) {
      // If item types mismatch, and it's not handled above (e.g. one is basic array, other object array)
      // Default to replacing the array if types are fundamentally different.
      // Or, throw error as before, if strict type consistency is required.
      // For now, let's keep the error for unexpected mixed-type arrays.
      throw new Error('oldArrItemType 和 newArrItemType 不一致')
    }
    // 接下来取其中一个存在的值作为类型
    const arrItemType = oldArrItemType || newArrItemType

    // 3.1 基本类型数组
    if (isBasicArrayType(newArr) && isBasicArrayType(oldArr) && arrItemType !== 'object') {
      const diff = diffBasicArray(newArr as BasicArrayType, oldArr as BasicArrayType)
      if (diff.length > 0) {
        // 只要有差异，基本数组的规则是直接将新数组作为新数据，而不是传递 diff
        result[key] = newArr
      }
      continue
    }

    // 3.2 引用类型数组 (now correctly handles arrays of BasicObjects with stringifiable sub-properties)
    if (isBasicObjectArrayType(newArr) && isBasicObjectArrayType(oldArr) && arrItemType === 'object') {
      const res: BasicObjectArrayDiffType = {
        insList: [],
        delList: [],
        updList: [],
      }
      const newObjArray = newArr as BasicObjectArrayType;
      const oldObjArray = oldArr as BasicObjectArrayType;
      // 新增
      for (const item of newObjArray) {
        if (!item.id || !oldObjArray.find(i => i.id === item.id)) { // Consider items without ID or not found in old as new
          res.insList.push(item)
        }
      }
      for (const item of oldObjArray) {
        const newItemInNewArray = newObjArray.find(i => i.id === item.id);
        // 删除
        if (!newItemInNewArray) {
          res.delList.push(item)
        } else {
          // 比对更新
          // newItemInNewArray is guaranteed to be defined here
            const diff = diffObjDep(newItemInNewArray, item, false) // keepId is false for array items
            if (Object.keys(diff).length > 0) {
              // The problem statement usually wants the newItem if there's a diff in updList for PUT/PATCH scenarios.
              // Storing the diff object itself (`diff`) or the `newItemInNewArray` in updList depends on API contract.
              // Current code for BasicObjectArrayDiffSchema expects full objects in updList.
              res.updList.push({...item, ...diff, id: item.id }) // Push the updated new item
            }
        }
      }
      if (res.insList.length > 0 || res.delList.length > 0 || res.updList.length > 0) {
        result[key] = res
      }
    }
  }
  return result
}

function isPromiseLike(value) {
  // 1. 首先确保 value 不是 null 且是一个对象或函数
  //    （因为基本类型如 string, number 不会有 .then 方法）
  // 2. 然后检查它是否有一个 'then' 属性，并且这个属性是一个函数
  // 3. 或者检查它是否是一个 async 函数
  return value !== null && 
         (typeof value === 'object' || typeof value === 'function') && 
         (typeof value.then === 'function' || 
          (typeof value === 'function' && value.constructor.name === 'AsyncFunction'));
}


export {
  filterEmptyValues,
  diffObj,
  diffObjDep,
  isPromiseLike,
}
