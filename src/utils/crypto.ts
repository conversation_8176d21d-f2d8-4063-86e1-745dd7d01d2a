const encoder = new TextEncoder()

async function encrypt(message: string) {
  const data = encoder.encode(message)
  const hash = await window.crypto.subtle.digest('SHA-256', data)
  return hash
}

/**
 * 为对象生成固定的 SHA-256 hash
 * @param obj 要生成 hash 的对象
 * @returns Promise<string> 返回十六进制格式的 hash 字符串
 */
async function objectHash(obj: any): Promise<string> {
  // 将对象转换为规范化的 JSON 字符串
  const normalizedString = normalizeObject(obj)

  // 使用 TextEncoder 编码字符串
  const data = encoder.encode(normalizedString)

  // 生成 SHA-256 hash
  const hashBuffer = await window.crypto.subtle.digest('SHA-256', data)

  // 将 ArrayBuffer 转换为十六进制字符串
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')

  return hashHex
}

/**
 * 规范化对象，确保相同内容的对象生成相同的字符串
 * @param obj 要规范化的对象
 * @returns string 规范化后的 JSON 字符串
 */
function normalizeObject(obj: any): string {
  if (obj === null) return 'null'
  if (obj === undefined) return 'undefined'

  const type = typeof obj

  if (type === 'boolean' || type === 'number') {
    return String(obj)
  }

  if (type === 'string') {
    return JSON.stringify(obj)
  }

  if (type === 'function') {
    return obj.toString()
  }

  if (obj instanceof Date) {
    return obj.toISOString()
  }

  if (Array.isArray(obj)) {
    return '[' + obj.map(item => normalizeObject(item)).join(',') + ']'
  }

  if (type === 'object') {
    // 获取所有键并排序，确保顺序一致
    const keys = Object.keys(obj).sort()
    const pairs = keys.map(key => {
      const value = normalizeObject(obj[key])
      return JSON.stringify(key) + ':' + value
    })
    return '{' + pairs.join(',') + '}'
  }

  return String(obj)
}

export { encrypt, objectHash }
