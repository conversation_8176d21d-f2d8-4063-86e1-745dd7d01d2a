import type { Component } from 'vue'

interface Columns {
  // column label
  title: string
  // column key(prop)
  key: string
  // query type
  query?:
    'select'
    | 'select-multiple'
    | 'input'
    | 'input-number'
    | 'date-range-time'
    | 'date-range'
    | 'excel'
    | 'excel-textarea'
  // query default value, 支持函数动态计算值
  defaultQueryValue?: string | number | boolean | any[] | (() => string | number | boolean | any[] | null)
  // super query
  superQuery?: 'select' | 'input'
  // query options
  options?: { label: string, value: string | number }[]
  // translate: need translate to i18n
  translate?: 'on' | 'off'
  // sort
  sort?: 'on' | 'off'
  // query width
  queryWidth?: number
  // column width
  width?: number
  // render: custom data render
  render?: Component
  // custom header render
  header?: Component
  // hide column
  hideColumn?: boolean
  // currently this will just work for quick query
  suffix?: string | Component
  prefix?: string | Component 
}

type EntityFromColumns<T extends Columns[]> = {
  [K in T[number]['key']]: string | number;
}

type DataListFromColumns<T extends Columns[]> = EntityFromColumns<T>[]

export type {
  Columns,
  EntityFromColumns,
  DataListFromColumns,
}
