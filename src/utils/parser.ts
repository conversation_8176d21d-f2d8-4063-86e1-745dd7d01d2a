/**
 * @param queryStr 查询字符串，like a=1&b=2
 */
function parseQuery(queryStr: string): Record<string, string> {
  if (!queryStr.includes('=')) {
    return {}
  }

  if (!queryStr.includes('&')) {
    const [key, value] = queryStr.split('=')
    return {
      [key]: value
    }
  }

  const query = {}
  queryStr.split('&').forEach(item => {
    const [key, value] = item.split('=')
    query[key] = value
  })
  return query
}

function makeQuery(query: Record<string, string | number | boolean>): string {
  return Object.entries(query)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&')
}

export { parseQuery, makeQuery }