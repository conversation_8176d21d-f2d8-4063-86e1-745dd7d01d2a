import { requestConfig } from '@/config/request'
import { useSystem } from '@/hooks'
import { t } from '@/modules/i18n'

interface FetcherOptions {
  method?: 'get' | 'post' | 'put' | 'delete'
  queryParams?: Record<string, any>
  body?: any
  // if true, body will be sent as raw
  keepRaw?: boolean
}

interface FetcherMethods {
  get: () => Promise<any | undefined>
  post: (body?: any) => Promise<any | undefined>
  put: (body?: any) => Promise<any | undefined>
  delete: () => Promise<any | undefined>
}

type FetcherReturn<T extends FetcherOptions> = T extends { method: keyof FetcherMethods }
  ? any | undefined
  : FetcherMethods

/**
 * how to use fetcher
 * const data = await fetcher('/api/user', { queryParams: { id: 1 } })
 *  .post({ foo: 'bar' })
 *
 * promise version
 * fetcher('/api/user', { queryParams: { id: 1 } })
 *  .get()
 *  .then(data => {
 *  })
 *
 * or you can use useWrapper
 * a reactive wrapper for fetcher
 * const { loading, data, refresh } = useWrapper(() => fetcher('/api/user', { queryParams: { id: 1 } }))
 */

async function fetcher<T extends FetcherOptions>(url: string, options: T): Promise<FetcherReturn<T>> {
  url = formatUrl(url, options)
  const methods = createMethods(url, options)
  if (options.method) {
    return methods[options.method]()
  }
  return methods
}

function formatUrl(url: string, options: FetcherOptions) {
  url = url.startsWith('http') ? url : `${requestConfig.baseUrl}/${useSystem().currentSystem.value}${url}`

  // process query params
  if (options.queryParams) {
    const searchParams = new URLSearchParams()
    for (const [key, value] of Object.entries(options.queryParams)) {
      searchParams.append(key, value.toString())
    }
    url += `?${searchParams.toString()}`
  }

  return url
}

function createMethods(url: string, opt: FetcherOptions): FetcherMethods {
  const commonFetch = async (method: string, body?: any): Promise<any> => {
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      }

      if (body && !opt.keepRaw) {
        options.body = JSON.stringify(body)
      }

      const response = await fetch(url, options)
      return processResponse(response)
    }
    catch (error: any) {
      if (error instanceof Error) {
        // handle specific error
        if (error.message === 'network') {
          window.$message?.error(t('error.network'))
        }
        else if (error.message === 'failed') {
          // do nothing, error message is handled in processResponse

        }
      }
      else {
        window.$message?.error(t('error.unknown'))
      }
    }
  }

  return {
    get: () => commonFetch('GET'),
    post: (body?: any) => commonFetch('POST', body),
    put: (body?: any) => commonFetch('PUT', body),
    delete: () => commonFetch('DELETE'),
  }
}

async function processResponse(response: Response): Promise<any | undefined> {
  if (!response.ok) {
    throw new Error('network')
  }
  const parsedResponse: {
    code: number
    data: any
    message: string
  } = await response.json()
  if (parsedResponse.code !== 0) {
    // const raw = parsedResponse.message
    // if (raw.includes('$$')) {
    // const splited = raw.split('$$')
    // const i18nKey = splited[0]
    // const i18nValue = splited.slice(1)
    // }
    // else {
    window.$message?.error(t(parsedResponse.message))
    // }
    throw new Error('failed')
  }
  return parsedResponse.data
}

export { fetcher }
