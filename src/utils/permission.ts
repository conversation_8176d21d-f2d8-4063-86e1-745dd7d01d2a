import { usePerm } from '@/hooks/usePerm';

interface Options {
	save?: string;
	edit?: string;
	delete?: string;
	importTable?: string;
	exportTable?: string;
	advancedQuery?: string;
	config?: string;
	groupSearch?: string;
	[key: string]: string | undefined;
}

const fullActionPerm = [
	"save",
	"edit",
	"delete",
	"deleteMul",
	"importTable",
	"exportTable",
	"advancedQuery",
	"config",
	"groupSearch",
];
function genActionHideList(options: Options): any {
	const actionsHideList: string[] = [];
	const { hasPerm } = usePerm();
	fullActionPerm.forEach((key) => {
		if (options[key]) {
			if (!hasPerm(options[key])) {
				actionsHideList.push(key);
			}
		} else {
			actionsHideList.push(key);
		}
	});
	return actionsHideList;
}

export { genActionHideList };

export type {
  Options
}
