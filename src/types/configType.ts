type System = {
  key: 'auth' | 'wcs' | 'wms' | string
  homePath?: string
}

interface SystemOptions {
  /**
   * 系统列表, 这会影响登录页和登录后的系统切换选项
   * 默认是 ['auth', 'wcs', 'wms']
   */
  list?: System[]
  /**
   * 项目名称
   * 默认是 'Inform'
   */
  appName?: string
  /**
   * 系统默认语言
   * 默认是 'zhCN'
   */
  defaultLanguage?: string
  /**
   * 页脚文本, 默认是 Copyright © 2025 Inform
   */
  footerText?: string
  /**
   * 分页表格配置
   */
  table?: {
    /**
     * 是否将第一列设为路由链接
     */
    firstColumnRouter?: boolean
  }
}

interface RequestOptions {
  // 请求基础路径
  baseUrl?: string
}

export type {
  SystemOptions,
  RequestOptions,
}
