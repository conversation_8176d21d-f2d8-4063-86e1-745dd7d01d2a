export interface RowData {
  id: string
  sort: number
  columnLabel: string
  columnName: string
  columnType: string
  attrName: string
  columnComment: string
  isList: boolean
  isQuery: boolean
  queryLink: 'like' | 'eq'
  superQuery: boolean
  isSortable: boolean
  isPk: boolean
  columnWidth: string
  fixed: 'left' | 'right' | ''
  isForm: boolean
  formType: 'text' | 'textarea' | 'number' | 'select' | 'radio' | 'checkbox' | 'date' | 'date-time'
  isRequired: boolean
  isEdit: boolean
  dictName: string
  // regex
  validator: string
}
