import type { MenuOption, TreeOption } from 'naive-ui'

/**
 * 在菜单设置中返回的完整菜单对象
 * 包含菜单类型
 */
export interface ServerMenuItem {
  pid: string
  url?: string
  firstRouter: 'wcs' | 'wms' | 'auth'
  menuName: string
  menuType: number
  icon: string
  orderNum: number
  openStyle: number
  isCache: number
  isEnable: number
  creator: string
  createDate: string
  updater: string
  updateDate: string
  dynCode: string
  id: string
  children: ServerMenuItem[]
}

export interface TreeSelectOption {
  label: string
  value: string
  children?: TreeSelectOption[]
}

export interface TreeOptionWithMeta extends TreeOption {
  meta: Partial<Omit<ServerMenuItem, 'children'>>
}

/**
 * sidebar 中需要的简略菜单对象
 * 不包含 button 类型
 */
export interface ServerPageMenuItem {
  // 菜单 id
  id: string
  // 父级 id
  pid: string
  // 菜单名称
  menuName: string
  // 菜单图标
  icon: string
  // 菜单打开方式
  openStyle: number
  // 是否开启页面缓存
  isCache: number
  /**
   * 路由路径
   * 决定页面展示的路径和组件
   */
  url: string
  // 数据 id
  dynCode?: string
  // 子菜单
  children?: ServerPageMenuItem[]
  // 菜单类型
  menuType: 0 | 1 | 2
}

export type LimitedMenuOption = Omit<MenuOption, 'children'> & {
  children?: Array<Omit<MenuOption, 'children'>>
}
