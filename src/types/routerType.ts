export type MenuType = 'dir' | 'page'
/** 路由所携带的meta标识 */
export interface RouteMeta {
  title: string
  /* 图标，一般配合菜单使用 */
  icon?: string
  /* 是否需要登录权限。 */
  requiresAuth?: boolean
  /* 可以访问的角色 */
  roles?: Entity.RoleType[]
  /* 是否开启页面缓存 */
  keepAlive?: boolean
  /* 有些路由我们并不想在菜单中显示，比如某些编辑页面。 */
  hide?: boolean
  /* 菜单排序。 */
  order?: number
  /* 嵌套外链  */
  href?: string
  /** 当前路由不在左侧菜单显示，但需要高亮某个菜单的情况 */
  activeMenu?: string
  /** 当前路由是否会被添加到Tab中 */
  withoutTab?: boolean
  /** 当前路由是否会被固定在Tab中,用于一些常驻页面 */
  pinTab?: boolean
  /** 当前路由在左侧菜单是目录还是页面,不设置默认为page */
  menuType?: MenuType
  /** 动态表标识 */
  dynCode?: string
  /** 菜单id */
  menuId?: string
  openStyle: 1 | 0
}

export type MetaKeys = keyof RouteMeta

export interface baseRoute {
  /** 路由名称(路由唯一标识) */
  name: string
  /** 路由路径 */
  path: string
  /** 路由重定向 */
  redirect?: string
  /* 页面组件地址 */
  componentPath?: string | null
  /* 路由id */
  id: string
  /* 父级路由id，顶级页面为null */
  pid: string | null
}

/** 后端返回的路由信息：将路由字段和 meta 字段打平后的结果 */
export type FlatRoute = RouteMeta & baseRoute

/**
 * 挂载到项目上的真实路由结构
 */
export interface Route extends baseRoute {
  /** 子路由 */
  children?: Route[]
  /* 页面组件 */
  component: any
  /** 路由描述 */
  meta: RouteMeta
}
