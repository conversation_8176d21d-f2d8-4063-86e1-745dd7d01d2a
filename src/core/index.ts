import type { Core } from './types'
import { createRouter } from './modules/router'
import { createDict } from './modules/dict'
import { createI18n } from './modules/i18n'
import { createRequest } from './modules/request'
import { createMessage } from './modules/message'
import { createDialog } from './modules/dialog'
import utils from './modules/utils'

// 单例实例
let core: Core | null = null

/**
 * 创建核心模块 - 主入口函数
 * 返回包含所有子模块的单例对象
 */
export function makeCore(): Core {
  if (!core) {
    core = {
      router: createRouter(),
      dict: createDict(),
      i18n: createI18n(),
      request: createRequest(),
      message: createMessage(),
      dialog: createDialog(),
      app: {},
      components: {},
      utils,
    }
  }
  
  return core
}

export type { Core } from './types'
export * from './types' 