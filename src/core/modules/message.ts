import { t as translate } from '@/modules/i18n'
import type { Message } from '../types'

/**
 * 创建消息模块
 */
export function createMessage(): Message {
  return {
    success(message: string) {
      window.$message.success(translate(message))
    },
    error(message: string) {
      window.$message.error(translate(message))
    },
    info(message: string) {
      window.$message.info(translate(message))
    }
  }
} 