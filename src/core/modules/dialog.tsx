import { h, Suspense, ref } from 'vue'
import { t as translate } from '@/modules/i18n'
import type { Dialog, UseTableOptions, CommonHooks, CreateOptions, DynamicTableDialogOptions, UploadDialogOptions } from '../types'
import BareDynTableV3 from '@/components/dynamic/BareDynTableV3.vue'
import { NButton, NImage } from 'naive-ui'

function create(opt: CreateOptions) {
  const { content, handleSubmit, style } = opt
  const dialog = window.$dialog.create({
    style: {
      ...style,
    },
    type: 'default',
    icon: () => '',
    content: () => h(content),
    positiveText: translate('common.submit'),
    negativeText: translate('common.cancel'),
    onPositiveClick: async () => {
      await handleSubmit?.()
      return false
    },
    onNegativeClick: () => {
      dialog.destroy()
    },
  })
  return {
    close: dialog.destroy
  }
}

function dynamicTableDialog(opt: DynamicTableDialogOptions) {
  const { dynCode, options, handleSubmit, style, bottomSlot, topSlot } = opt
  const checked = ref<any[]>([])
  const bareDynTableRef = ref<any>(null)

  return create({
    content: () => <div>
      <Suspense>
        {{
          default: () => h(defineComponent({
            setup: async () => {
              return () =>
                <div>
                  <BareDynTableV3
                    dynCode={dynCode}
                    hide={{
                      rowOperate: true,
                    }}
                    {...options}
                    onCheckedChange={(val) => {
                      checked.value = val
                    }}
                    ref={(el) => {
                      bareDynTableRef.value = el
                    }}
                  />
                  <div>
                    {bottomSlot ? h(bottomSlot) : null}
                  </div>
                </div>
            }
          })),
          fallback: () => <div>Loading...</div>
        }}
      </Suspense>
    </div>,
    handleSubmit: () => handleSubmit(checked.value, bareDynTableRef.value?.getExpose()),
    style: {
      ...style,
      width: '1000px',
    }
  })
}

function uploadDialog(opt: UploadDialogOptions) {
  const { value, handleUpload, handleSubmit, style, ...rest } = opt
  const { open, onChange } = useFileDialog(rest)

  // 上传多个和上传单个的组件不同
  const isMultiple = rest.multiple !== false

  // 初始化 url。如果是 multiple，即使 value 不是数组，也初始化为空数组。
  // 如果是 multiple 且 value 是逗号分隔的字符串，则拆分后作为初始值
  const url = ref<string | string[] | null>(
    isMultiple
      ? (typeof value === 'string' ? value.split(',') : (Array.isArray(value) ? value : []))
      : (value ?? null)
  )

  onChange(async (files) => {
    if (!files) return
    if (files.length === 0)
      return
    const result = await handleUpload?.(Array.from(files))
    if (!result) return
    if (isMultiple) {
      url.value = Array.isArray(result) ? [...(url.value as string[]), ...result] : [...(url.value as string[]), result]
    } else {
      url.value = Array.isArray(result) ? result[0] : result
    }
  })

  // 如果是图片则用 NImage 展示预览
  const acceptTypes = rest.accept?.split(',').map(type => type.trim()) || [];
  const commonImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/svg+xml'];
  const isImage = acceptTypes.some(type => type === 'image/*' || commonImageTypes.includes(type));

  // 只可上传单个，底部按钮为选择文件，选择后覆盖当前文件
  const singleContent = () => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <div class="w-250px h-160px flex justify-center items-center border border-gray-300 rounded-md border-dashed">
          {
            isImage ? (
              url.value ? (
                <NImage src={url.value as string} width={100} height={100} />
              ) : (
                <div>
                  {translate('common.noData')}
                </div>
              )
            ) : (
              url.value ? (
                <div>
                  {url.value as string}
                </div>
              ) : (
                <div>
                  {translate('common.noData')}
                </div>
              )
            )
          }
        </div>

        <NButton onClick={() => open()} type="primary">
          {translate('system.button.clickToSelect')}
        </NButton>
      </div>
    );
  }

  // 可上传多个，同时已有文件的右上角展示删除选项
  const multipleContent = () => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <div class="w-700px h-160px flex justify-center gap-4 items-center border border-gray-300 rounded-md border-dashed">
          {
            isImage ? (
              url.value ? (
                (url.value as string[]).map((item) => <div class="flex flex-col gap-2 justify-center items-center">
                  <NImage src={item} width={100} height={100} />
                  <NButton text type='primary' onClick={() => {
                    url.value = (url.value as string[]).filter(i => i !== item)
                  }}>
                    {translate('common.delete')}
                  </NButton>
                </div>)
              ) : (
                <div>
                  {translate('common.noData')}
                </div>
              )
            ) : (
              url.value ? (
                (url.value as string[]).map((item) => (
                  <div class="flex flex-col gap-2 justify-center items-center">
                    <div>{item}</div>
                    <NButton text type='primary' onClick={() => {
                      url.value = (url.value as string[]).filter(i => i !== item)
                    }}>
                      {translate('common.delete')}
                    </NButton>
                  </div>
                ))
              ) : (
                <div>
                  {translate('common.noData')}
                </div>
              )
            )
          }
        </div>

        <NButton onClick={() => open()} type="primary">
          {translate('system.button.clickToSelect')}
        </NButton>
      </div>
    );
  }

  return create({
    content: () => <div>
      <Suspense>
        {{
          default: isMultiple ? multipleContent : singleContent,
          fallback: () => <div>Loading...</div>
        }}
      </Suspense>
    </div>,
    handleSubmit: () => handleSubmit(url.value),
    style: {
      ...style,
      width: isMultiple ? '900px' : '500px',
    }
  })
}

/**
 * 创建弹窗模块
 */
export function createDialog(): Dialog {
  return {
    create,
    tableDialog(opt: {
      options: UseTableOptions,
      hooks?: CommonHooks
    }) {
      const { options, hooks } = opt
      const dialog = window.$dialog.create({
      })
      return {
        close: dialog.destroy
      }
    },
    dynamicTableDialog,
    uploadDialog,
  }
} 