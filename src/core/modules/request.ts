import { useRequest } from '@/hooks/useRequest'
import type { Request, RequestGlobalOptions } from '../types'
import { t } from '@/modules/i18n'

/**
 * 创建请求模块
 */
export function createRequest(): Request {
  return {
    async get<T extends any>(url: string, options?: RequestGlobalOptions): Promise<T> {
      const queryStr = options?.query ? `?${new URLSearchParams(options.query).toString()}` : ''
      const { data, error } = await useRequest(url + queryStr)
      if (!error.value) {
        if (options?.messageOnSuccess) {
          window.$message.success(t('common.operationSuccess'))
        }
        return data.value as T
      }
      throw error.value
    },
    
    async post<T extends any>(url: string, options?: {
      body?: Record<string, any> | string
    } & RequestGlobalOptions): Promise<T> {
      const queryStr = options?.query ? `?${new URLSearchParams(options.query).toString()}` : ''
      const fetchOptions = options?.body ? {
        method: 'POST',
        body: JSON.stringify(options.body),
      } : {
        method: 'POST',
      }
      const { data, error } = await useRequest(url + queryStr, fetchOptions)
      if (!error.value) {
        if (options?.messageOnSuccess) {
          window.$message.success(t('common.operationSuccess'))
        }
        return data.value as T
      }
      throw error.value
    },

       
    async upload<T extends any>(url: string, options: {
      files: Array<File>
    } & RequestGlobalOptions): Promise<T> {
      if (options.files.length === 0) {
        window.$message.error('system.tips.pleaseAtLeastSelectOne')
        throw new Error('system.tips.pleaseAtLeastSelectOne')
      }

      const queryStr = options?.query ? `?${new URLSearchParams(options.query).toString()}` : ''
      const formData = new FormData()
      options.files.forEach((file) => {
        formData.append('file', file)
      })
      const fetchOptions = {
        method: 'POST',
        body: formData,
      }
      const { data, error } = await useRequest(url + queryStr, fetchOptions, {
        beforeFetch(ctx) {
          if (ctx.options.headers) {
            delete ctx.options.headers['Content-Type']
          } 
        }
      })
      if (!error.value) {
        if (options?.messageOnSuccess) {
          window.$message.success(t('common.operationSuccess'))
        }
        return data.value as T
      }
      throw error.value
    }, 
  } 
} 