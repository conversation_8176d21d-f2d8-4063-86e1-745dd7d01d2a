import { LinkEnum, CompareEnum } from '@/constants/query'
import type { Utils } from '@/core/types'

type Query = string | number | {
  op: CompareEnum
  opval: string | number
}

function compactTableQuery(form: any[], query: Record<string, Query>) {
  if (!Array.isArray(form)) {
    return form
  }
  const copy = JSON.parse(JSON.stringify(form))

  if (copy.length === 0) {
    copy.push({
      data: [],
      link: LinkEnum.AND
    })
  }

  Object.keys(query).forEach((key) => {
    const val = query[key]
    if (val === undefined) {
      return
    }
    const valType = typeof val
    if (valType === 'string' || valType === 'number') {
      copy[0].data.push({
        colkey: key,
        op: CompareEnum.LIKE,
        link: LinkEnum.AND,
        opval: val,
      })
    } else if (valType === 'object') {
      const val_ = val as { op: CompareEnum; opval: string | number }
      const opr = val_.op
      const opval = val_.opval
      copy[0].data.push({
        colkey: key,
        op: opr,
        link: LinkEnum.AND,
        opval: opval,
      })
    }
  })
  return copy
}

const utils: Utils = {
  compactTableQuery,
}

export default utils
