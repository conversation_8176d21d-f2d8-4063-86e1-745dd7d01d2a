import { useDict } from '@/hooks/useDict'
import type { Dict } from '../types'
import { t } from '@/modules/i18n'

/**
 * 创建字典模块
 */
export function createDict(): Dict {
  const { sysDict } = useDict()
  
  const transformDict = (dictData: any[]) => {
    return dictData.map(item => ({
      label: t(item.dictLabel),
      value: item.dictValue
    }))
  }

  const dict: Dict = {}

  // 转换每个字典项
  Object.keys(sysDict.value).forEach(key => {
    if (Array.isArray(sysDict.value[key])) {
      dict[key] = transformDict(sysDict.value[key])
    } else {
      dict[key] = sysDict.value[key]
    }
  })

  return dict
} 