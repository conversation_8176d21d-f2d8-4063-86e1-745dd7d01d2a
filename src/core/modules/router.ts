import { router } from '@/router'
import { goto, goback } from '@/utils'
import type { Router } from '../types'
import type { Component } from 'vue'

/**
 * 创建路由模块
 */
export function createRouter(): Router {
  return {
    query() {
      return router.currentRoute.value.query
    },
    params() {
      return router.currentRoute.value.params
    },
    meta() {
      return router.currentRoute.value.meta
    },
    push(path: string, options?: {
      title?: string
      query?: Record<string, string>
      component?: Component
      meta?: Record<string, any>
    }) {
      goto(path, {
        ...options,
        action: 'push',
      })
    },
    replace(path: string, options?: {
      title?: string
      query?: Record<string, string>
      component?: Component
      meta?: Record<string, any>
    }) {
      goto(path, {
        ...options,
        action: 'replace',
      })
    },
    back(options?: {
      closeTab?: boolean
    }) {
      goback(options?.closeTab)
    }
  }
} 