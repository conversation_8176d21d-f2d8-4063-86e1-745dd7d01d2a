import type { Component, CSSProperties } from 'vue'
import type { LocationQuery, RouteMeta, RouteParamsGeneric } from 'vue-router'
import type { Columns as UseTableNativeColumns } from '@/utils/entity'
import type { Options as UseTableNativeOptions, URL as UseTableNativeURL } from '@/hooks/useTableType'
import type { TablePageOptions } from '@/components/dynamic/DynTableType'
import type {UseFileDialogOptions} from '@vueuse/core'

/**
 * 核心接口 - 集成所有模块
 */
export interface Core {
  i18n: I18n
  app: App
  router: Router
  request: Request
  components: Components
  dict: Dict
  message: Message
  dialog: Dialog
  utils: Utils
}

/**
 * 应用接口
 */
export interface App {
  // 预留应用级接口
}

export interface CreateOptions {
  content: Component
  handleSubmit: () => Promise<void> | void
  style?: CSSProperties
}

export interface DynamicTableDialogOptions {
  dynCode: string,
  options?: TablePageOptions,
  otherOptions?: Record<string, any>,
  beforeRequest?: (data: any) => any,
  handleSubmit: (checked: any[], getTableRef: undefined | (() => any)) => Promise<void> | void,
  style?: CSSProperties,
  bottomSlot?: Component,
  topSlot?: Component,
}

type UploadResult = string | string[] | null
export interface UploadDialogOptions extends UseFileDialogOptions {
  value?: string | string[],
  // 为了简化实现，参数一直是数组
  handleUpload?: (files: File[]) => Promise<UploadResult> | UploadResult,
  handleSubmit: (value: UploadResult) => Promise<void> | void,
  style?: CSSProperties
}

export interface CommonHooks {
  beforeRequest?: (data: any) => void
  beforeInit?: (data: any) => void
  beforeSubmit?: (form: any) => void
  afterSubmit?: (data?: any) => void
}

export interface UseTableOptions {
  url: UseTableNativeURL
  columns: UseTableNativeColumns[]
  options?: UseTableNativeOptions<any>
}

/**
 * 弹窗接口
 */
export interface Dialog {
  create(opt: CreateOptions): {
    close: () => void
  }
  tableDialog(opt: {
    options: UseTableOptions,
    hooks?: CommonHooks
  }): {
    close: () => void
  }
  dynamicTableDialog(opt: DynamicTableDialogOptions): {
    close: () => void
  }
  uploadDialog(opt: UploadDialogOptions): {
    close: () => void
  }
}

/**
 * 路由接口
 */
export interface Router {
  query(): LocationQuery
  params(): RouteParamsGeneric
  meta(): RouteMeta
  push(path: string, options?: {
    title?: string
    query?: Record<string, string>
    component?: Component
    meta?: Record<string, any>
  })
  replace(path: string, options?: {
    title?: string
    query?: Record<string, string>
    component?: Component
    meta?: Record<string, any>
  })
  back(options?: {
    closeTab?: boolean
  })
}

/**
 * 国际化接口
 */
export interface I18n {
  t(key: string): string
}

/**
 * 请求接口
 */
export interface Request {
  get<T extends any>(url: string, options?: RequestGlobalOptions): Promise<T>

  post<T extends any>(url: string, options?: {
    body?: Record<string, any>
  } & RequestGlobalOptions): Promise<T>

  upload<T extends any>(url: string, options: {
    files: Array<File>
  } & RequestGlobalOptions): Promise<T>
}

export interface RequestGlobalOptions {
  query?: Record<string, string>
  messageOnSuccess?: boolean
}

/**
 * 消息接口
 */
export interface Message {
  success(message: string): void
  error(message: string): void
  info(message: string): void
}

/**
 * 组件接口
 */
export interface Components {
  // 预留组件级接口
}

/**
 * 工具函数
 */
export interface Utils {
  compactTableQuery(form: any[], query: Record<string, any>): any[]
}

/**
 * 字典类型
 */
export type Dict = Record<string, any> 