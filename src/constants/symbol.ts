import { TableBffReturn } from '@/bff/table'
import type { ServerMenuItem } from '@/types/menuType'

export const RootRefreshSymbol = Symbol('RootRefresh') as InjectionKey<{
  refresh: () => void | Promise<void>
}>

export const AllMenusSymbol = Symbol('AllMenus') as InjectionKey<ServerMenuItem[]>

export const AvailableSysSymbol = Symbol('AvailableSys') as InjectionKey<Ref<string[]>>

export const DynContextSymbol = Symbol('DynContext') as InjectionKey<{
  dynCode: string
  bff: TableBffReturn
}>