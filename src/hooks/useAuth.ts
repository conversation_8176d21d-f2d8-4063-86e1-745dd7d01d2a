import { local } from '@/utils'
import { useTab } from '@/hooks'
import { useSystemRouter } from '@/hooks/useSystemRouter'
import { useRequest } from '@/hooks/useRequest'
import { requestConfig } from '@/config'

// 状态
const userInfo = ref<Entity.User | null>(null)
const token = useLocalStorage('token', '')

export function useAuth() {
  // getters
  const isLogin = computed(() => Boolean(token.value))

  // actions
  async function logout() {
    useRequest('/logout')
    // 清除本地缓存
    clearAuthStorage()

    // 清空路由、菜单等数据
    const { reset } = useSystemRouter()
    reset()

    // 清空标签栏数据
    const tabStore = useTab()
    await tabStore.clearAllTabs()

    // 重置状态
    userInfo.value = null
    token.value = ''

    // 重定向到登录页
    window.location.reload()
  }

  function clearAuthStorage() {
    local.remove('token')
  }

  async function login(form: {
    username: string
    password: string
    firstRouter: string
  }) {
    try {
      const { data } = await useRequest<string>('/login', {
        beforeFetch(ctx) {
          ctx.url = `/${requestConfig.baseUrl}/login`
        }
      })
        .post(form)

      if (data.value) {
        await handleLoginInfo(data.value)
        window.location.href = '/'
      }
    }
    catch (e) {
    }
  }

  async function handleLoginInfo(data: string) {
    // 保存 token
    token.value = data

    await fetchUserInfo()
  }

  async function fetchUserInfo() {
    /**
{
    "app": {},
    "auth": {
        "warehouseList": [
            "cainiao"
        ],
        "currentWarehouse": "cainiao"
    },
    "sn": {
        "warehouseList": [
            "tbr",
            "cainiao",
            "zhongmian"
        ],
        "currentWarehouse": "tbr"
    },
    "wms": {}
}
     */
    const { data } = await useRequest<any>('/sys/user/info')
    if (data.value) {
      userInfo.value = data.value
      if (userInfo.value?.appParams) {
        const params = userInfo.value?.appParams
        // k = sn/auth/wms
        Object.keys(params).forEach(k => {
          if (!params[k]) return
          let val = params[k].currentWarehouse
          if (!val) {
            if (!params[k].warehouseList) return
            val = params[k].warehouseList.length > 0 ? params[k].warehouseList[0] : ''
          }
          localStorage.setItem(`current${k.toUpperCase()}Tenant`, val)
        })
      }
    }
  }

  function setToken(newToken: string) {
    token.value = newToken
  }

  return {
    // 状态
    userInfo,
    token,

    // getters
    isLogin,

    // actions
    login,
    logout,
    fetchUserInfo,
    clearAuthStorage,
    setToken,
  }
}
