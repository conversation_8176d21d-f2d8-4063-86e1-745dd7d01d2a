import { systemConfig } from '@/config'
import { useLocalStorage } from '@vueuse/core'

function setTemp(temp: string) {
  localStorage.setItem('tempSystem', temp)
}
const lastSystem = useLocalStorage('lastSystem', systemConfig.list?.[0].key)
// 监听 focus 事件
window.addEventListener('focus', function() {
  lastSystem.value = currentSystem.value
});

function consumeTemp() {
  const temp = localStorage.getItem('tempSystem')
  localStorage.removeItem('tempSystem')
  if (temp) {
    return temp
  }
  return lastSystem.value
}
const temp = consumeTemp()
const currentSystem = ref(temp ? temp : lastSystem.value)
watch(currentSystem, async (newVal) => {
  if (!newVal)
    return

  setTemp(newVal)
  lastSystem.value = newVal
  if (window.location.href.includes('/login'))
    return

  window.location.reload()
})

function useSystem() {
  function toggleSystem(newVal: string) {
    currentSystem.value = newVal
  }

  return {
    currentSystem,
    toggleSystem,
    systemList: systemConfig.list,
    setTemp,
    consumeTemp,
    lastSystem,
  }
}

export {
  useSystem,
}