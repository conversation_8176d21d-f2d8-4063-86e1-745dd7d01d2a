import type { RouteMeta } from 'vue-router'
import { type ComputedRef, type Ref, computed, ref } from 'vue'
import { router } from '@/router'
import { useApp } from './useApp'
import { parseQuery } from '@/utils'

let refreshSet = new Set<string>()

export function setRefresh(path: string) {
  refreshSet.add(path.split('?')[0])
}
export function getRefresh(path: string) {
  if (refreshSet.has(path)) {
    refreshSet.delete(path)
    return true
  }
  return false
}

const tabs = ref<TabItem[]>([])
const currentTabPath = ref('')

export interface TabItem {
  meta: RouteMeta
  path: string
  fullPath: string
}
interface UseTabReturn {
  tabs: Ref<TabItem[]>
  currentTabPath: Ref<string>
  allTabs: Ref<TabItem[]>
  isLastTab: ComputedRef<boolean>
  addTab: (route: TabItem) => void
  closeTab: (path: string) => Promise<void>
  closeOtherTabs: (path: string) => Promise<void>
  closeLeftTabs: (path: string) => Promise<void>
  closeRightTabs: (path: string) => Promise<void>
  clearAllTabs: () => Promise<void>
  closeAllTabs: () => Promise<void>
  hasExistTab: (path: string) => boolean
  setCurrentTab: (path: string) => void
  getTabIndex: (path: string) => number
}

export function useTab(): UseTabReturn {

  // 计算所有标签
  const allTabs = tabs

  // 判断当前标签是否是最新打开的标签
  const isLastTab = computed(() => {
    const index = getTabIndex(currentTabPath.value)
    return index === tabs.value.length - 1
  })

  // 添加标签页
  function addTab(route: TabItem) {
    if (route.meta.withoutTab)
      return

    if (hasExistTab(route.path as string))
      return

    tabs.value.push(route)
  }

  // 关闭标签页
  async function closeTab(fullPath: string) {
    console.log('1、开始关闭标签页:', fullPath);
    const tabsLength = tabs.value.length

    const [path, query] = fullPath.split('?')
    const queryObj = query ? parseQuery(query) : {}
    const from = queryObj.from
    // 先获取索引和判断状态
    const index = getTabIndex(path)
    console.log('2、获取标签索引和状态:', { index, tabsLength, currentTabPath: currentTabPath.value });
    if (index === -1) {
      console.log('2.1、标签未找到，直接返回');
      return // 如果找不到标签，直接返回
    }

    const isLast = index + 1 === tabsLength
    const isCurrent = currentTabPath.value === path

    // 先移除标签页，再处理路由跳转
    const targetTab = tabs.value[index]
    const realPath = targetTab.fullPath.split('?')[0]

    // 保存跳转目标
    let jumpTarget = from
    console.log('3、确定跳转目标，初始目标:', jumpTarget, 'from:', from);

    // 不存在 from 时，需要判断往哪跳转
    if (!from) {
      if (isCurrent) {
        if (!isLast) {
          // 如果不是最后一个，跳转到下一个
          jumpTarget = tabs.value[index + 1].fullPath
          console.log('3.1、当前标签且不是最后一个，跳转到下一个:', jumpTarget);
        }
        else if (index > 0) {
          // 如果是最后一个且不是第一个，跳转到前一个
          jumpTarget = tabs.value[index - 1].fullPath
          console.log('3.2、当前标签且是最后一个但不是第一个，跳转到前一个:', jumpTarget);
        } else if (index === 0) {
          jumpTarget = '/'
          console.log('3.3、当前标签且是第一个也是最后一个，跳转到首页:', jumpTarget);
        }
      } else {
        console.log('3.4、不是当前标签，不自动跳转');
      }
    } else {
      // 如果存在 from，要往 refreshSet 中添加 from
      setRefresh(from)
      console.log('3.5、存在 from，设置 from 页面需要刷新:', from);
    }

    // 现在移除标签
    tabs.value = tabs.value.filter(item => {
      const realItemPath = item.fullPath.split('?')[0]
      return realItemPath !== realPath
    })
    console.log('4、移除标签页:', realPath, '剩余标签数:', tabs.value.length);


    // 如果需要跳转，在下一个事件循环中进行
    if (jumpTarget) {
      console.log('5、执行路由跳转:', jumpTarget);
      await nextTick()
      const [path, query] = jumpTarget.split('?')
      router.push({
        path,
        query: query ? Object.fromEntries(new URLSearchParams(query)) : undefined
      })
    } else {
      console.log('5、无需路由跳转');
    }

    console.log('6、处理页面刷新标记:', realPath);
    const { pageNeedRefresh } = useApp()
    pageNeedRefresh.value.push(realPath)
    await nextTick()
    setTimeout(() => {
      pageNeedRefresh.value.length = 0
      console.log('6.1、清除页面刷新标记');
    }, 100)
  }

  // 关闭其他标签页
  async function closeOtherTabs(path: string) {
    const currentTab = tabs.value[getTabIndex(path)]
    await Promise.all(
      tabs.value.map(async (tab) => {
        if (tab.path !== path) {
          await closeTab(tab.fullPath)
        }
      })
    )
    tabs.value = [currentTab]
  }

  // 关闭左侧标签页
  async function closeLeftTabs(path: string) {
    const index = getTabIndex(path)
    const tabsToClose = tabs.value.slice(0, index)
    await Promise.all(
      tabsToClose.map(async (tab) => {
        await closeTab(tab.fullPath)
      })
    )
    tabs.value = tabs.value.slice(index)
  }

  // 关闭右侧标签页
  async function closeRightTabs(path: string) {
    const index = getTabIndex(path)
    const tabsToClose = tabs.value.slice(index + 1)
    await Promise.all(
      tabsToClose.map(async (tab) => {
        await closeTab(tab.fullPath)
      })
    )
    tabs.value = tabs.value.slice(0, index + 1)
  }

  // 清除所有标签页
  async function clearAllTabs() {
    await Promise.all(
      tabs.value.map(async (tab) => {
        await closeTab(tab.fullPath)
      })
    )
    tabs.value = []
  }

  // 关闭所有标签页
  async function closeAllTabs() {
    await Promise.all(
      tabs.value.map(async (tab) => {
        await closeTab(tab.fullPath)
      })
    )
    tabs.value = []
    router.push('/')
  }

  // 检查标签是否存在
  function hasExistTab(path: string) {
    return tabs.value.some(item => item.path === path)
  }

  // 设置当前激活的标签
  function setCurrentTab(path: string) {
    currentTabPath.value = path
  }

  // 获取标签索引
  function getTabIndex(path: string) {
    return tabs.value.findIndex(item => item.path === path)
  }

  return {
    tabs,
    currentTabPath,
    allTabs,
    isLastTab,
    addTab,
    closeTab,
    closeOtherTabs,
    closeLeftTabs,
    closeRightTabs,
    clearAllTabs,
    closeAllTabs,
    hasExistTab,
    setCurrentTab,
    getTabIndex,
  }
}
