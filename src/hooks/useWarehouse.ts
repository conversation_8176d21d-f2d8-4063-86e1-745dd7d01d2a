import { usePerm } from './usePerm'
import { useRequest } from './useRequest'

// /sys/user/changeWarehouse
const currentWarehouse = useLocalStorage('currentWMSWarehouse', '')
const warehouseList = ref<{
  code: string
  id: string
  name: string
  spec: string | undefined
}[]>([])

function useWarehouse() {
  async function fetchWarehouseList() {
    const url = '/base/warehouse/getWarehouseListByUserId'
    const res = await useRequest<{
      id: string
      code: string 
      spec: string
      name: string
    }[]>(url)
    warehouseList.value = res.data.value || []
    if (currentWarehouse.value === '' || !warehouseList.value.find(i => i.code === currentWarehouse.value)) {
      currentWarehouse.value = warehouseList.value[0].code
    }
  }

  function toggleWarehouse(newVal: string) {
    currentWarehouse.value = newVal
  }

  const { hasPerm } = usePerm()
  const filteredWarehouseList = computed(() => {
    return warehouseList.value.filter(i => hasPerm(i.code))
  })
  return {
    currentWarehouse,
    toggleWarehouse,
    warehouseList: filteredWarehouseList,
    refreshWarehouseList: fetchWarehouseList,
  }
}

export {
  useWarehouse,
}