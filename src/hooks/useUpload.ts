import { t } from '@/modules/i18n'
import { fetcher } from '@/utils/fetcher'

interface Options {
  fileType: string
  api: string
  sizeLimit?: number
  numLimit?: number
  onSuccess?: (file: File) => void
  onError?: () => void
}

const defaultOptions: Options = {
  fileType: '',
  api: '',
  sizeLimit: 10,
  numLimit: 1,
  onSuccess: () => {},
  onError: () => {},
}

async function useUpload(options: Options) {
  const { fileType, api, sizeLimit = 10, numLimit = 1, onSuccess, onError } = { ...defaultOptions, ...options }

  // 创建文件选择器
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = fileType
  input.multiple = numLimit > 1

  // 处理文件选择
  input.onchange = async (e: Event) => {
    const files = (e.target as HTMLInputElement).files
    if (!files?.length)
      return

    // 检查文件类型
    if (fileType && !files[0].type.includes(fileType)) {
      window.$message?.error(t('hooks.useUpload.fileType'))
      return
    }

    // 检查文件大小
    if (sizeLimit && files[0].size > sizeLimit * 1024 * 1024) {
      window.$message?.error(t('hooks.useUpload.fileSize'))
      return
    }

    // 上传文件
    const formData = new FormData()
    formData.append('file', files[0])

    try {
      await fetcher(api, {
        method: 'post',
        body: formData,
        keepRaw: true,
      })

      onSuccess?.(files[0])
      window.$message?.success(t('common.operationSuccess'))
    }
    catch (e) {
      onError?.()
      window.$message?.error(t('hooks.useUpload.uploadFailed'))
    }
  }

  // 触发文件选择
  input.click()
}

export { useUpload }
