import type { MenuOption } from 'naive-ui'
import type { Ref } from 'vue'
import { useMenu } from './useMenu'
import { router } from '@/router'

import type { FlatRoute } from '@/types/routerType'
import { routerBff } from '@/bff/router'
import { createMenus, recoverRoutes } from '@/utils/router'
import type { LimitedMenuOption } from '@/types/menuType'
import { useSystem } from './useSystem'

interface UseRouterReturn {
  state: Ref<'waiting' | 'ready'>
  // the currently highlighted menu key
  activeMenu: Ref<string | null>
  menus: Ref<LimitedMenuOption[]>
  flatRoutes: Ref<FlatRoute[]>

  init: () => Promise<void>
  reset: () => void
  setActiveMenu: (key: string) => void
}

const state = ref<'waiting' | 'ready'>('waiting')
const activeMenu = ref<string | null>(null)
const flatRoutes = ref<FlatRoute[]>([])
const menus = ref<MenuOption[]>([])

watch(state, () => {
  if (state.value === 'ready') {
  }
  else {
  }
}, { immediate: true })

function useSystemRouter(): UseRouterReturn {
  const init = async () => {
    state.value = 'waiting'
    // fetch server routes
    // use routerBff to transform server routes to flat routes
    const { fetchMenu } = useMenu()
    const { currentSystem } = useSystem()
    const menuList = await fetchMenu[currentSystem.value as 'wcs' | 'wms' | 'auth']()
    flatRoutes.value = routerBff(menuList[0].children?.map((i) => {
      i.pid = '0'
      return i
    }) ?? [])
    console.log('flatRoutes', flatRoutes.value)

    recoverRoutes(flatRoutes.value)
    // generate side menu
    menus.value = createMenus(flatRoutes.value)
    // generate route cache
    state.value = 'ready'
  }
  // eslint-disable-next-line unicorn/consistent-function-scoping
  function reset() {
    if (router.hasRoute('appRoot'))
      router.removeRoute('appRoot')
  }
  const setActiveMenu = (key: string) => {
    activeMenu.value = key
  }

  return {
    state,
    activeMenu,
    flatRoutes,
    menus,
    init,
    reset,
    setActiveMenu,
  }
}

export { useSystemRouter }
