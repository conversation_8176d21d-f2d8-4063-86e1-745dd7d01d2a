import { NButton, NDropdown, timelineProps } from 'naive-ui'
import type { SortableEvent } from 'vue-draggable-plus'
import { useDownload } from './useDownload'
import { useRequest } from './useRequest'
import type { Options, QueryForm, QueryPatternItem, State, TableColumn, Toolbar, URL, UseTableReturn } from './useTableType'
import type { Columns, DataListFromColumns } from '@/utils/entity'
import { t } from '@/modules/i18n'
import { usePerm } from './usePerm'
import { CompareEnum, LinkEnum } from '@/constants'
import { uid } from 'radash'
import dayjs from 'dayjs'

/**
 * table hook
 * @param url request url
 * @param columns table columns config
 * @param options table options
 * @returns table state and methods
 */
function useTable<T extends Columns[]>(url: URL, columns: T, options: Options<T> = {}): UseTableReturn {
  let pageUrl = ''
  let exportTableUrl = ''
  if (typeof url === 'string') {
    pageUrl = url
  }
  else {
    pageUrl = url.page
    exportTableUrl = url.exportTable || ''
  }
  let needAdvancedQuery = false
  // convert columns to table columns
  const tableColumns = markRaw(columns.map(column => {
    if (column.superQuery !== undefined) {
      needAdvancedQuery = true
    }
    return {
      title: column.header ? markRaw(column.header) : column.title,
      key: column.key,
      translate: column.translate,
      sort: options.defaultSort?.columnKey === column.key ? 'on' : column.sort,
      superQuery: column.superQuery,
      render: column.render ? markRaw(column.render) : undefined,
      resizable: true,
      options: column.options,
      minWidth: 160,
      width: column.width ? column.width : undefined,
      ellipsis: {
        tooltip: !column.render,
      },
      ellipsisComponent: 'performant-ellipsis',
      hideColumn: column.hideColumn,
    }
  }) as TableColumn[])

  if (options.expandRender) {
    tableColumns.unshift({
      type: 'expand',
      renderExpand: (row, index) => h(options.expandRender!, { row, index }),
      width: 20,
    })
  }
  if (options.checkable) {
    tableColumns.unshift({
      type: 'selection',
      width: 20,
      fixed: 'left',
      disabled: (row: Record<string, any>) => options.checkableRow ? !options.checkableRow(row) : false,
    })
  }
  const { hasPerm } = usePerm()
  const RowActionsRender = (row: any) => {
    let actions = [
      ...(typeof options.rowActions === 'function'
        ? options.rowActions(row)
        : options.rowActions || []),
    ].filter(a => !a.perm ? true : hasPerm(a.perm))

    if (!options.actionHideList?.includes('edit')) {
      actions = [...actions, {
        label: t('common.edit'),
        click: () => {
          trigger('edit', row)
        },
      }]
    }

    if (!options.actionHideList?.includes('delete')) {
      actions = [...actions, {
        label: t('common.delete'),
        click: () => {
          trigger('delete', row)
        },
      }]
    }

    if (actions.length > 1) {
      return h(NDropdown, {
        options: actions.map(action => ({
          label: action.label,
          key: action.label,
        })),
        trigger: 'hover',
        onSelect: (action) => actions.find(a => a.label === action)?.click(row)
      }, () => h(NButton, {
        type: 'primary',
        size: 'small',
      }, { default: () => t('common.expand') }))
    }

    return h('div', {
      class: 'flex gap-3'
    }, actions.map(action => h(NButton, {
      type: 'primary',
      text: true,
      style: { color: action.color },
      class: action.class,
      size: 'small',
      onClick: () => action.click(row)
    }, { default: () => action.label })))
  }
  if ((options.rowActions || !options.actionHideList?.includes('delete') || !options.actionHideList?.includes('edit')) && !options.hide?.rowAction) {
    tableColumns.push({
      title: t('common.action'),
      key: 'action',
      fixed: 'right',
      width: 60,
      render: RowActionsRender,
    })
  }
  const colCopy = [...tableColumns]
  // convert columns to query pattern
  // 首先创建一个映射，用于存储 extQuery 中的项
  const extQueryMap = new Map()
  if (options.extQuery && options.extQuery.length) {
    options.extQuery.forEach(item => {
      if (item.key) {
        extQueryMap.set(item.key, item)
      }
    })
  }

  // 处理 columns，如果 key 在 extQueryMap 中存在，则使用 extQuery 中的项
  const mergedColumns = columns.map(column => {
    if (column.key && extQueryMap.has(column.key)) {
      // 用 extQuery 中的项替换 columns 中的项
      const extItem = extQueryMap.get(column.key)
      // 删除已使用的项，以便后续添加未匹配的 extQuery 项
      extQueryMap.delete(column.key)
      return extItem
    }
    return column
  })

  // 添加 extQuery 中未匹配的项
  if (extQueryMap.size > 0) {
    extQueryMap.forEach(item => {
      mergedColumns.push(item)
    })
  }

  const queryPattern = mergedColumns
    .map((column) => {
      if (column.query) {
        return {
          type: column.query,
          prop: column.key,
          label: column.title,
          options: column.options || [],
          width: column.queryWidth,
          // defaultQueryValue 支持传入函数，动态计算默认值
          defaultQueryValue: column.defaultQueryValue,
          suffix: column.suffix,
          prefix: column.prefix
        }
      }
      return null
    })
    .filter(Boolean) as QueryPatternItem[]

  const superQueryPattern = columns
    .filter(column => column.superQuery !== undefined)
    .map(column => ({
      type: column.superQuery,
      prop: column.key,
      label: column.title,
      options: column.options || [],
      width: column.queryWidth,
    })) as QueryPatternItem[]

  const cbs: Array<(event: string, ...args: any[]) => void | boolean | Promise<void | boolean>> = []
  cbs.unshift(handleExport)
  function on(callback: (event: string, ...args: any[]) => void | boolean | Promise<void | boolean>) {
    cbs.unshift(callback)
  }
  async function trigger(event: string, ...args: any[]) {
    for (const cb of cbs) {
      const stop = await cb(event, ...args)
      if (stop)
        break
    }
  }

  // filter toolbar depends on actionHideList
  let toolbar: Toolbar = ['save', 'importTable', 'exportTable', 'deleteMul', 'config']
  if (needAdvancedQuery) {
    toolbar.push('advancedQuery')
  }
  if (!options.expandRender && !options.actionHideList?.includes('groupSearch')) {
    toolbar.push('groupSearch')
  }
  toolbar = toolbar?.filter(t => !options.actionHideList?.includes(t)) || []
  const initQueryFormData = queryPattern.filter(item => item.defaultQueryValue !== undefined).reduce((acc: any, item) => {
    const defaultValue = resolveDefaultValue(item)
    if (Array.isArray(defaultValue)) {
      acc.push({
        colkey: item.prop,
        op: CompareEnum.GREATER_EQUAL,
        opval: defaultValue[0],
        link: LinkEnum.AND
      })

      // 处理日期范围类型，对结束日期加一天
      let endValue = defaultValue[1]
      if (item.type === 'date-range' || item.type === 'date-range-time') {
        // 日期时间格式为 yyyy-MM-dd HH:mm:ss
        // 日期格式为 yyyy-MM-dd
        // 需要使用 dayjs 为 endValue 加一天，并格式化回原格式
        endValue = item.type === 'date-range-time'
          ? dayjs(endValue).add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
          : dayjs(endValue).add(1, 'day').format('YYYY-MM-DD')
      }

      acc.push({
        colkey: item.prop,
        op: CompareEnum.LESS_EQUAL,
        opval: endValue,
        link: LinkEnum.AND
      })
    }
    else if (item.type === 'excel' || item.type === 'excel-textarea') {
      acc.push({
        colkey: item.prop,
        op: CompareEnum.IN,
        opval: Array.isArray(defaultValue) ? defaultValue.join(',') : (defaultValue?.toString()?.split('\n').join(',') || ''),
        link: LinkEnum.AND
      })
    }
    else if (item.type === 'select-multiple') {
      acc.push({
        colkey: item.prop,
        op: CompareEnum.IN,
        opval: Array.isArray(defaultValue) ? defaultValue.join(',') : String(defaultValue),
        link: LinkEnum.AND
      })
    }
    else {
      acc.push({
        colkey: item.prop,
        op: 'eq',
        opval: defaultValue,
        link: LinkEnum.AND
      })
    }
    return acc
  }, [])
  const state: State = reactive({
    loading: false,
    allowViewChange: options.cardView === 'on',
    url,
    searchBar: {
      height: 0,
    },
    toolbar,
    table: {
      view: 'table',
      draggable: options.draggable || false,
      columns: tableColumns,
      data: [],
      checked: [],
      inner: options.inner || false,
      expandedRow: []
    },
    sortStates: options.defaultSort ? [{
      columnKey: options.defaultSort.columnKey as string,
      order: options.defaultSort.order as 'ascend' | 'descend',
    }] : [{
      columnKey: 'createDate',
      order: 'descend'
    }],
    pagination: {
      page: 1,
      limit: options.defaultPageSize || 10,
      total: 0,
    },
    query: {
      pattern: queryPattern,
      form: [
        {
          link: 'and',
          data: [
            ...initQueryFormData
          ]
        }
      ] as QueryForm,
    },
    superQuery: {
      pattern: superQueryPattern,
      form: [],
    },
    queryWrapper,
    groupFieldList: {
      form: [],
    },
    trigger,
    on,
    handler: {
      toolbar: handleToolbarAction,
      rowDrag: handleRowDrag,
      columnDrag: handleColumnDrag,
      handleSort,
    },
    toolbarExt: options.toolbarExt || [],
    freeZone: options.freeZone,
    autoScroll: options.autoScroll || 'off',
    rawOptions: options,
    raw: {
      url: url,
      columns: columns,
      options: options,
    },
    fetchData
  })
  async function handleRowDrag(event: SortableEvent) {
    const { oldIndex, newIndex } = event
    if (oldIndex === newIndex || oldIndex === undefined || newIndex === undefined)
      return
    await fetchData()
  }

  async function handleColumnDrag(event: SortableEvent) {
    const { oldIndex, newIndex } = event
    if (oldIndex === newIndex || oldIndex === undefined || newIndex === undefined)
      return
    // reorder columns
    state.table.columns.splice(newIndex, 0, state.table.columns.splice(oldIndex, 1)[0])
  }
  let sortChanged = false
  interface Item {
    columnKey: string
    order: 'ascend' | 'descend' | false
  }
  function handleSort(item: Item | Item[] | null) {
    console.log('handleSort', item)

    // Always convert to array for consistent handling
    const items = Array.isArray(item) ? item : [item]

    // Process each sort item
    items.forEach(i => {
      if (!i)
        return

      const index = state.sortStates.findIndex(s => s.columnKey === i.columnKey)

      if (index !== -1) {
        // Update existing sort state
        state.sortStates[index].order = i.order

        // If order is false (no sorting), remove this item from sortStates
        if (i.order === false) {
          state.sortStates.splice(index, 1)
        }
      }
      else if (i.order !== false) {
        // Only add new sort state if it's not 'false' (no sorting)
        // state.sortStates.push(i)
        state.sortStates.unshift(i)
      }
    })

    fetchData()
  }

  function queryWrapper() {
    let form = [
      ...state.query.form,
      ...state.superQuery.form,
    ]
    if (options.beforeRequest) {
      form = options.beforeRequest(form)
    }
    const orders = state.sortStates
      .filter(s => s.order !== false)
      .map(s => ({
        column: s.columnKey,
        asc: s.order === 'ascend',
      }))
    // 如果 orders 的长度是 0，则要塞一个默认的排序
    if (orders.length === 0) {
      orders.push(options.defaultSort ? {
        column: options.defaultSort.columnKey as string,
        asc: options.defaultSort.order === 'ascend',
      } : {
        column: 'createDate',
        asc: false,
      })
    }

    if (Object.keys(form).length === 0) {
      return {
        page: state.pagination.page,
        limit: state.pagination.limit,
        orders,
        groupFieldList: state.groupFieldList.form,
      }
    }

    return {
      filterInfo: form,
      page: state.pagination.page,
      limit: state.pagination.limit,
      orders,
      groupFieldList: state.groupFieldList.form,
    }
  }
  function handleToolbarAction(key: string) {
    switch (key) {
      case 'download':
        useDownload(`${url}/download`, {
          params: queryWrapper(),
        })
        break
      case 'import':
        trigger('import')
        break
    }
  }

  async function fetchData() {
    state.loading = true
    try {
      // 首先基于 groupFieldList 处理列数据
      if (state.groupFieldList.form.length > 0) {
        // 直接从 groupFieldList 生成分组查询列
        const groupColumns = state.groupFieldList.form.map(item => {
          // 创建新的列配置
          return {
            ...columns.find(column => column.key === item.field),
            key: item.field + (item.op === 'query' ? '' : '|' + item.op),
            title: `${t('system.header.' + item.field)}(${t('common.' + item.op)})`,
          }
        })

        // 更新表格列配置
        state.table.columns = [...groupColumns]
      } else {
        // 如果没有分组查询，则恢复原始列配置
        state.table.columns = [...colCopy]
      }

      // 准备请求参数
      const queryParams = queryWrapper()

      // 发送请求
      const { data, error } = await useRequest<{
        list: DataListFromColumns<T>
        total: number
      }>(pageUrl, {
        method: 'POST',
        body: JSON.stringify(queryParams),
      })

      if (error.value) {
        throw error.value
      }
      if (!data.value) {
        return
      }

      // 处理响应数据
      const processedData = data.value
      if (options.afterRequest) {
        processedData.list = options.afterRequest(processedData.list) as DataListFromColumns<T> || processedData.list
      }

      // 翻译处理
      const translatedList = processedData.list.map((item) => {
        const translatedItem = { ...item }
        if (!('id' in item)) {
          // @ts-ignore
          translatedItem.id = uid(7)
        }
        state.table.columns.forEach((column) => {
          if (column.translate === 'on' && column.key) {
            const value = item[column.key as keyof typeof item]
            if (value) {
              translatedItem[column.key as keyof typeof item] = t(String(value))
            }
          }
        })
        return translatedItem
      })

      // 更新状态
      state.table.data = translatedList
      state.pagination.total = processedData.total
      state.table.expandedRow.length = 0
    }
    catch (error) {
      console.error(error)
      state.table.data = []
      window.$message?.error?.(t('http.defaultTip'))
    }
    finally {
      state.table.checked.length = 0
      state.loading = false
    }
  }
  watch(
    () => [state.pagination.page, state.query.form, state.superQuery.form, state.groupFieldList.form],
    () => {
      fetchData()
    },
    {
      deep: true,
    },
  )

  watch(
    () => state.pagination.limit,
    () => {
      state.pagination.page = 1
      fetchData()
    }
  )
  function handleExport(e: string) {
    if (e !== 'exportTable') {
      return
    }
    if (!exportTableUrl) {
      window.$message.error(t('error.exportUrlNotConfigured'))
      return
    }
    const orders = state.sortStates
      .filter(s => s.order !== false)
      .map(s => ({
        column: s.columnKey,
        asc: s.order === 'ascend',
      }))
    useDownload(exportTableUrl, {
      params: {
        filterInfo: queryWrapper().filterInfo,
        orders,
      },
    })
  }

  if (options.immediate !== 'off') {
    fetchData()
  }

  const checked = computed(() => {
    // get checked row data by id
    return state.table.checked.map(id => state.table.data.find(row => row.id === id))
  })

  return {
    state,
    on,
    trigger,
    refresh: fetchData,
    queryWrapper,
    checked,
  }
}

// 处理默认查询值，支持函数或直接值
function resolveDefaultValue(item: QueryPatternItem) {
  if (item.defaultQueryValue === undefined) {
    return undefined
  }

  // 如果defaultQueryValue是函数，则执行它获取值
  if (typeof item.defaultQueryValue === 'function') {
    return item.defaultQueryValue()
  }

  // 否则直接返回值
  return item.defaultQueryValue
}

export { useTable, resolveDefaultValue }

export type {
  TableColumn,
  Options,
  State,
  QueryPatternItem,
}
