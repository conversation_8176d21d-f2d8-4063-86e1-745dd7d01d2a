import type { Component, ComputedRef } from 'vue'
import type { SortableEvent } from 'vue-draggable-plus'
import type { CompareEnum, LinkEnum } from '@/constants/query'
import type { Columns, DataListFromColumns } from '@/utils/entity'
import { DataTableCreateSummary } from 'naive-ui'

export type URL = string | {
  page: string
  info?: string
  importTable?: string
  exportTable?: string
  importTemplate?: string
  save?: string
  update?: string
  delete?: string
}

export interface RowAction {
  label: string
  color?: string
  class?: string
  perm?: string
  click: (row: any) => void
}

export type RowActionType = RowAction[] | ((row: any) => RowAction[])

export type Toolbar = Array<'save' | 'deleteMul' | 'importTable' | 'exportTable' | 'advancedQuery' | 'config' | 'groupSearch'>

export interface TableColumn {
  title?: string
  header?: Component | string
  key?: string
  type?: string
  translate?: 'on' | 'off'
  superQuery?: 'select' | 'input'
  sort?: 'on' | 'off'
  fixed?: 'left' | 'right'
  render?: Component
  options?: { label: string, value: string | number }[]
  renderExpand?: Component
  width?: number
  minWidth?: number
  hideColumn?: boolean
  disabled?: (row: Record<string, any>) => boolean
}

export interface QueryPatternItem {
  type: string
  prop: string
  label: string
  options?: { label: string, value: string | number }[]
  width?: number
  defaultQueryValue?: string | number | boolean | (() => string | number | boolean | null)
  prefix: string | Component
  suffix: string | Component
}

export type ToolbarExt = ({
  label: string
  icon?: string
  type?: import('naive-ui').ButtonProps['type']
  click: string | (() => Promise<void>)
} | Component)[]

export interface Options<T extends Columns[]> {
  /**
   * show data in card view
   * @default 'off'
   */
  cardView?: 'on' | 'off'
  /**
   * enable table drag
   * @default false
   */
  draggable?: boolean
  // extend query, only show in search bar
  extQuery?: Columns[]
  rowActions?: RowActionType
  /**
   * show checkbox
   * selected value will be passed to operations on the toolbar
   */
  checkable?: boolean
  checkableRow?: (row: Record<string, any>) => boolean
  // before request
  beforeRequest?: (params: QueryForm) => QueryForm
  // after request
  afterRequest?: (dataList: DataListFromColumns<T>) => unknown
  // request immediately
  immediate?: 'on' | 'off'
  // hide button list
  actionHideList?: Array<
    'save'
    | 'edit'
    | 'delete'
    | 'deleteMul'
    | 'importTable'
    | 'exportTable'
    | 'advancedQuery'
    | 'config'
    | 'groupSearch'
  >
  expandRender?: Component
  defaultPageSize?: number
  // inner table
  inner?: boolean
  toolbarExt?: ToolbarExt
  freeZone?: Component
  autoScroll?: 'on' | 'off'
  defaultSort?: {
    columnKey?: string
    order?: 'ascend' | 'descend'
  }
  pageSizeOptions?: number[]
  tableScrollX?: number
  hide?: {
    rowAction?: boolean
  },
  getQueryFromUrl?: boolean,
  createSummary?: DataTableCreateSummary,
  rowProps?: (rowData: object, rowIndex: number) => Record<string, any>
}

export type GroupFieldListForm = {
  field: string
  op: string
}[]

export interface State {
  loading: boolean
  allowViewChange: boolean
  url: URL
  searchBar: {
    height: number
  }
  toolbar: Toolbar
  table: {
    view: 'table' | 'card'
    draggable: boolean
    columns: TableColumn[]
    data: any[]
    checked: any[]
    inner: boolean
    expandedRow: any[]
  }
  sortStates: Array<{ columnKey: string, order: 'ascend' | 'descend' | false }>
  query: {
    pattern: QueryPatternItem[]
    form: QueryForm
  }
  superQuery: {
    pattern: QueryPatternItem[]
    form: QueryForm
  }
  groupFieldList: {
    form: GroupFieldListForm
  }
  pagination: {
    page: number
    limit: number
    total: number
  }
  trigger: (event: string, ...args: any[]) => void
  on: (callback: (event: string, ...args: any[]) => void | boolean) => void
  handler: {
    toolbar: (key: string) => void
    rowDrag: (event: SortableEvent) => void
    columnDrag: (event: SortableEvent) => void
    handleSort: (item: { columnKey: string, order: 'ascend' | 'descend' | false }) => void
  }
  toolbarExt: ToolbarExt
  freeZone?: Component
  rawOptions: Options<any>
  raw: {
    url: URL
    columns: Columns[]
    options: Options<any>
  }
  fetchData: () => void
}

export interface UseTableReturn {
  state: State
  on: (callback: (event: string, ...args: any[]) => void | boolean | Promise<void | boolean>) => void
  trigger: (event: string, ...args: any[]) => void
  refresh: () => void
  queryWrapper: () => {
    filterInfo?: Record<string, any>
    page: number
    limit: number
    orders: Array<{
      column: string
      asc: boolean
    }>
  }
  checked: ComputedRef<any[]>
}

export type QueryForm = {
  link: LinkEnum
  data: Array<{
    colkey: string
    op: CompareEnum
    opval: any
    link: LinkEnum
  }>
}[]
