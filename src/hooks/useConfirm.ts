import { useRequest } from './useRequest'
import { t } from '@/modules/i18n'

interface UseConfirmOptions {
  content: string
  api: string
  params: any[]
  afterSuccess: () => void
}

export function useConfirm(opt: UseConfirmOptions) {
  const { api, params, afterSuccess, content } = opt
  window.$dialog.warning({
    title: t('common.tip'),
    content,
    positiveText: t('common.confirm'),
    negativeText: t('common.cancel'),
    onPositiveClick: async () => {
      const { error } = await useRequest(api)
        .post(JSON.stringify(params))
      if (error.value) {
        return
      }
      afterSuccess()
    },
  })
}
