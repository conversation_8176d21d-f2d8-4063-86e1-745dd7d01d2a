import { useRequest } from './useRequest'
import { t } from '@/modules/i18n'

interface UseDelDialogOptions {
  api: string
  params: any[]
  afterSuccess: () => void
}

function useDelDialog(opt: UseDelDialogOptions) {
  const { api, params, afterSuccess } = opt
  if (params.length === 0) {
    window.$message.error(t('system.tips.pleaseAtLeastSelectOne'))
    return
  }
  window.$dialog.warning({
    title: t('common.tip'),
    content: t('tip.delete'),
    positiveText: t('common.confirm'),
    negativeText: t('common.cancel'),
    onPositiveClick: async () => {
      const { error } = await useRequest(api)
        .post(params)
      if (error.value) {
        return
      }
      afterSuccess()
    },
  })
}

export { useDelDialog }
