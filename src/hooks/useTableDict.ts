import { useRequest } from './useRequest'
import { t } from '@/modules/i18n'

async function useTableDict(tableName: string) {
  const { data, error } = await useRequest<any>(`/sys/dict/type/dictTable/${tableName}`);

  if (!error.value && data.value) {
    return data.value.map((item: { code: string, name: string }) => ({ label: t(item.name), value: item.code }));
  }

  return []
}

export {
  useTableDict
}