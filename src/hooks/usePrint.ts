import { t } from '@/modules/i18n';
import {
  disAutoConnect,
  hiprint
} from "vue-plugin-hiprint";

disAutoConnect()
hiprint.init()

interface UsePrintOpt {
  /**
   * 模板名称
   * 比如: container.json
   */
  templateName: string
  /**
   * 数据
   */
  data: any
  /**
   * 模式
   * browser: 浏览器
   * client: 客户端
   * preview: 预览
   */
  mode: 'browser' | 'client' | 'preview'
  /**
   * 回调
   */
  cb?: (opt: Omit<UsePrintOpt, 'cb'>) => void | Promise<void>
}

async function usePrint(opt: UsePrintOpt) {
  const { templateName, data, mode } = opt

  if (opt.cb)
    await opt.cb({ templateName, data, mode })

  const template = await fetch(`/print/${templateName}`)
  if (!template.ok) {
    window.$message.error(`${templateName} ${t('common.notFound')}`)
    return
  }
  const templateJson = await template.json()
  const hiprintTemplate = new hiprint.PrintTemplate({ template: templateJson })

  if (mode === 'browser') {
    hiprintTemplate.print(data)
  }

  if (mode === 'client') {
    if (hiprint.hiwebSocket.opened)
      hiprint.refreshPrinterList(list => {
        const printer = list.filter(item => item.isDefault)[0]
        hiprintTemplate.print2(data, { printer, title: printer.displayName })
      })
    else
      hiprintTemplate.print(data)
  }

  if (mode === 'preview') {
    const html = hiprintTemplate.getHtml(data)
    hiprintTemplate.printByHtml(html)
  }
}

export {
  usePrint,
}

export type {
  UsePrintOpt
}