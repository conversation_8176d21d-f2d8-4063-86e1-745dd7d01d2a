import { useRequest } from './useRequest'

const permSet = new Set<string>()

let permLoaded = false
function usePerm() {
  async function init() {
    if (permLoaded) {
      return
    }
    const { data } = await useRequest<string[]>('/sys/user/permissions')
    if (data.value && Array.isArray(data.value)) {
      data.value.forEach((item: string) => {
        permSet.add(item)
      })
    }
    else {
      throw new Error('权限数据获取失败')
    }
    permLoaded = true
  }

  function hasPerm(perm: string) {
    // if perm set have '*:*:*', return true
    if (permSet.has('*:*:*'))
      return true

    return permSet.has(perm)
  }

  return {
    init,
    hasPerm,
  }
}

export { usePerm }
