import { t } from '@/modules/i18n'
import { router } from '@/router'
import { useSystem } from './useSystem'
import { requestConfig } from '@/config/request'
import { useRequest } from './useRequest'
import { playBeep, playVoice } from '@/utils/window'

interface Options {
  url: string
  onMessage?: (data: unknown) => void
}

const channel: Ref<any[]> = ref([])
const messageBar: Ref<any[]> = ref([])
const latest: Ref<any> = ref(null)
function emit(data: any) {
  const hasId = 'id' in data
  if (hasId) {
    // check if the data is already in the channel
    if (channel.value.some(item => item.id === data.id)) {
      return
    }
    channel.value.push(data)
    latest.value = data
  }
}
const errorCount = ref(0)

type Data = {
  path: string
  data: Record<string, any> | {
    code: number
    msg: string
  }
}

const baseUrl = (requestConfig.baseUrl ?? '') + `/${useSystem().currentSystem.value}`
export function useWS(opt: Options = {
  url: '',
}) {
  const { open, close } = useWebSocket(`${baseUrl}${opt.url}`, {
    autoReconnect: {
      retries: 3,
      delay: 3000,
    },
    immediate: false,
    heartbeat: {
      message: 'ping',
      interval: 5000,
    },
    onConnected,
    onError,
    onMessage: (ws: WebSocket, event: MessageEvent) => {
      if (event.data === 'pang') {
        return
      }
      const { path, data} = JSON.parse(event.data) as Data
      if ('code' in data) {
        if (data.code === 401) {
          window.$message?.error?.(t(data.msg))
          errorCount.value++
        }
      }
      switch (path) {
        case 'sys/reminder':
          emit(data)
          break
        case 'sys/messageBox':
          emit(data)
          handleMessageBox(data as Args)
          break
        default:
          return
      }
      if (errorCount.value > 3) {
        close()
      }
    }
  })

  return {
    open,
    close,
    channel,
    latest,
    messageBar,
    clearChannel: () => {
      channel.value = []
    }
  }
}

function onError() {
  window.$message?.error?.(t('notice.disconnectMessage'))
}
async function onConnected(ws: WebSocket) {
  ws.send(
    JSON.stringify({
      path: 'token',
      data: localStorage.getItem('token'),
    }),
  )
  const { data } = await useRequest('/sys/notice/noticeNoRead')
  if (!data.value) {
    return
  }
  if (!Array.isArray(data.value)) {
    return
  }
  data.value.forEach(i => {
    emit(i)
  })
}

interface Args {
  type?: MessageType;
  level?: string;
  duration?: number;
  voice?: number;
  title?: string;
  option?: string;
  sendTime?: string;
  extra?: any;
}

enum MessageType {
  // 弹窗
  MODAL = 1,
  // 进度条
  BAR = 2,
  // 通知
  NOTIFICATION = 3,
}

enum VoiceType {
  // 静音
  SILENT = 0,
  // 语音
  VOICE = 1,
  // 蜂鸣
  BEEP = 2,
}

function handleMessageBox(args: Args) {
  const { type, level, duration, voice, title, option, sendTime, extra } = args
  if (voice === VoiceType.VOICE && title) {
    playVoice(title)
  }
  if (voice === VoiceType.BEEP) {
    playBeep()
  }
  switch (args.type) {
    case MessageType.MODAL:
      const dialog = window.$dialog.info({
        // title: title,
        title: '提示',
        content: title,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          dialog.destroy()
        },
        onNegativeClick: () => {
          dialog.destroy()
        },
      })
      break
    case MessageType.BAR:
      messageBar.value.push(args)
      break
    case MessageType.NOTIFICATION:
      window.$notification.info({
        title: title,
        duration
      })
      break
  }
}


