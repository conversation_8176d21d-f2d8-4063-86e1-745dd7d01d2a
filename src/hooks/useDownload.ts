import { useRequest } from './useRequest'
import { useLoading } from './useLoading'

interface UseDownloadOptions {
  params?: Record<string, any>
  filename?: string
  method?: 'get' | 'post'
}

const { start, end } = useLoading()
async function useDownload(url: string, opt?: UseDownloadOptions) {
  start()
  try {
    let { params, filename, method = 'post' } = opt || {}

    const message = useMessage()
    const options = method === 'get' ? {
      method,
    } : {
      method,
      body: JSON.stringify({
        page: -1,
        ...params,
      }),
    }
    const { data, error, response} = await useRequest<Blob>(url, options, {})
      .blob()

    if (error.value !== null) {
      message.error(error.value.message)
      return
    }
    if (data.value === null) {
      return
    }

    // get filename from response
    const encodedName = filename = response.value?.headers.get('content-disposition')?.split('filename=')[1]
    if (encodedName) {
      filename = decodeURIComponent(encodedName)
    } else {
      filename = `${new Date().getTime().toString()}.xlsx`
    }

    const href = window.URL.createObjectURL(data.value)

    const downloadElement = document.createElement('a')
    downloadElement.href = href
    downloadElement.download = filename
    document.body.appendChild(downloadElement)
    downloadElement.click()
    document.body.removeChild(downloadElement)
    window.URL.revokeObjectURL(href)
  }
  finally {
    end()
  }
}

export {
  useDownload,
}
