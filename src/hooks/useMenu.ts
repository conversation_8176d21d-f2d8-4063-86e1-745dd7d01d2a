import type { Ref } from 'vue'
import { useRequest } from './useRequest'
import type { ServerPageMenuItem, TreeSelectOption } from '@/types/menuType'
import { t } from '@/modules/i18n'
import { systemConfig } from '@/config/system'

// 根据系统列表动态创建map
const menuListMap = ref<Record<string, ServerPageMenuItem[]>>(
  Object.fromEntries(systemConfig.list?.map(item => [item.key, []]) || [])
)

// 根据系统列表动态创建加载标志map
const loadedMap = ref<Record<string, boolean>>(
  Object.fromEntries(systemConfig.list?.map(item => [item.key, false]) || [])
)

function getMenuList(name: string) {
  return menuListMap.value[name] || []
}

function useMenu(): {
  wcs: Ref<ServerPageMenuItem[]>
  wms: Ref<ServerPageMenuItem[]>
  auth: Ref<ServerPageMenuItem[]>
  fetchMenu: Record<string, () => Promise<ServerPageMenuItem[]>>
  fetchMenuAll: () => Promise<void>
  getMenuList: (name: string) => ServerPageMenuItem[]
} {
  // 创建内部的通用fetchMenuByKey函数
  const fetchMenuByKey = async (key: string) => {
    if (loadedMap.value[key]) {
      return menuListMap.value[key]
    }
    const { data } = await useRequest<ServerPageMenuItem[]>(`/sys/menu/menuList/${key}`)
    if (data.value) {
      menuListMap.value[key] = data.value
      loadedMap.value[key] = true
      return data.value
    }
    throw new Error(`${key}菜单数据获取失败`)
  }

  // 动态创建fetchMenu对象
  const fetchMenu = Object.fromEntries(
    systemConfig.list?.map(item => [item.key, async () => fetchMenuByKey(item.key)]) || []
  ) as Record<string, () => Promise<ServerPageMenuItem[]>>

  const fetchMenuAll = async () => {
    await Promise.all(systemConfig.list?.map(item => fetchMenu[item.key]()) || [])
  }

  // 为了保持兼容性，为现有的三个系统创建计算属性
  // 如果系统配置中不存在特定系统，则返回空数组
  const wcs = computed(() => menuListMap.value.wcs || [])
  const wms = computed(() => menuListMap.value.wms || [])
  const auth = computed(() => menuListMap.value.auth || [])

  return {
    wcs,
    wms,
    auth,
    fetchMenu,
    fetchMenuAll,
    getMenuList
  }
}

export function menuToTreeSelectOption(menu: ServerPageMenuItem[]): TreeSelectOption[] {
  return menu.map(item => ({
    label: t(item.menuName),
    value: item.id,
    children: menuToTreeSelectOption(item.children ?? []),
  }))
}

export { useMenu }
