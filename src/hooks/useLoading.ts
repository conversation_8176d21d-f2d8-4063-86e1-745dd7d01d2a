import { ref } from 'vue'
import { t } from '@/modules/i18n'

/**
 * 全局 loading 遮罩
 */
function useLoading() {
  const loadingInstance = ref<HTMLElement | null>(null)

  // 开始 loading
  const start = () => {
    if (loadingInstance.value)
      return

    const container = document.createElement('div')
    container.className = 'fixed inset-0 z-999 flex flex-col items-center justify-center backdrop-blur-sm'

    const spinner = document.createElement('div')
    spinner.className = 'animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent'

    const text = document.createElement('span')
    text.className = 'mt-4 text-xl text-gray-600'
    text.textContent = t('common.loading')

    container.appendChild(spinner)
    container.appendChild(text)

    document.body.appendChild(container)
    loadingInstance.value = container
  }

  // 结束 loading
  const end = () => {
    if (!loadingInstance.value)
      return
    document.body.removeChild(loadingInstance.value)
    loadingInstance.value = null
  }

  return {
    start,
    end,
  }
}

export {
  useLoading,
}
