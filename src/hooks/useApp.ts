/* eslint-disable sort-imports */
import type { GlobalThemeOverrides } from 'naive-ui'
import { colord } from 'colord'
import { set } from 'radash'
import { useLocalStorage, useFullscreen, useColorMode } from '@vueuse/core'
import themeConfig from './theme.json'
import { local, setLocale } from '@/utils'
import { useRequest } from '@/hooks/useRequest'
import { systemConfig } from '@/config/system'
import { router } from '@/router'

export type TransitionAnimation = '' | 'fade-slide' | 'fade-bottom' | 'fade-scale' | 'zoom-fade' | 'zoom-out'
export type LayoutMode = 'leftMenu' | 'topMenu' | 'mixMenu'

// 状态
const state = useLocalStorage('app-state', {
  footerText: systemConfig.footerText,
  lang: systemConfig.defaultLanguage,
  theme: themeConfig as GlobalThemeOverrides,
  primaryColor: themeConfig.common.primaryColor,
  collapsed: false,
  grayMode: false,
  colorWeak: false,
  loadFlag: true,
  showLogo: true,
  showTabs: true,
  showFooter: true,
  showProgress: true,
  showBreadcrumb: true,
  showBreadcrumbIcon: true,
  showWatermark: false,
  showSetting: false,
  transitionAnimation: 'fade-slide' as TransitionAnimation,
  layoutMode: 'leftMenu' as LayoutMode,
  contentFullScreen: false,
})
const pageNeedRefresh = ref<string[]>([])
export function useApp() {
  const docEle = ref(document.documentElement)
  const { isFullscreen, toggle } = useFullscreen(docEle)
  const { system, store } = useColorMode({ emitAuto: true })

  // getters
  const storeColorMode = computed(() => store.value)
  const colorMode = computed(() => store.value === 'auto' ? system.value : store.value)
  const fullScreen = computed(() => isFullscreen.value)

  // actions
  function resetAllTheme() {
    state.value.theme = themeConfig
    state.value.primaryColor = '#6190E6'
    state.value.collapsed = false
    state.value.grayMode = false
    state.value.colorWeak = false
    state.value.loadFlag = true
    state.value.showLogo = true
    state.value.showTabs = true
    state.value.showFooter = true
    state.value.showBreadcrumb = true
    state.value.showBreadcrumbIcon = true
    state.value.showWatermark = false
    state.value.transitionAnimation = 'fade-slide'
    state.value.layoutMode = 'leftMenu'
    state.value.contentFullScreen = false

    setPrimaryColor(state.value.primaryColor)
  }

  async function setAppLang(lang: App.lang) {
    await useRequest('/sys/user/changeLanguage')
      .post({ appLanguage: lang === 'zhCN' ? 'zh_CN' : 'en_US' })

    setLocale(lang)
    local.set('lang', lang)
    state.value.lang = lang
    window.location.reload()
  }

  function setPrimaryColor(color: string) {
    const brightenColor = colord(color).lighten(0.05).toHex()
    const darkenColor = colord(color).darken(0.05).toHex()
    set(state.value.theme, 'common.primaryColor', color)
    set(state.value.theme, 'common.primaryColorHover', brightenColor)
    set(state.value.theme, 'common.primaryColorPressed', darkenColor)
    set(state.value.theme, 'common.primaryColorSuppl', brightenColor)
  }

  function setColorMode(mode: 'light' | 'dark' | 'auto') {
    store.value = mode
  }

  function toggleCollapse() {
    state.value.collapsed = !state.value.collapsed
  }

  function toggleFullScreen() {
    toggle()
  }

  function toggleContentFullScreen() {
    state.value.contentFullScreen = !state.value.contentFullScreen
  }

  async function reloadPage(delay = 600) {
    const path = router.currentRoute.value.path
    pageNeedRefresh.value.push(path)
    state.value.loadFlag = false

    await nextTick()

    if (delay) {
      setTimeout(() => {
        state.value.loadFlag = true
      }, delay)
    }
    else {
      state.value.loadFlag = true
    }
    pageNeedRefresh.value.length = 0
  }

  function toggleColorWeak() {
    docEle.value.classList.toggle('color-weak')
    state.value.colorWeak = docEle.value.classList.contains('color-weak')
  }

  function toggleGrayMode() {
    docEle.value.classList.toggle('gray-mode')
    state.value.grayMode = docEle.value.classList.contains('gray-mode')
  }

  return {
    // 状态
    state,
    pageNeedRefresh,

    // getters
    storeColorMode,
    colorMode,
    fullScreen,

    // actions
    resetAllTheme,
    setAppLang,
    setPrimaryColor,
    setColorMode,
    toggleCollapse,
    toggleFullScreen,
    toggleContentFullScreen,
    reloadPage,
    toggleColorWeak,
    toggleGrayMode,
  }
}
