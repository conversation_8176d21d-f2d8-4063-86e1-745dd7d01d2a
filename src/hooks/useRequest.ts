import { createFetch } from '@vueuse/core'
import { t } from '@/modules/i18n'
import { useAuth, useWarehouse } from '@/hooks'
import { requestConfig } from '@/config/request'
import { stw } from '@/utils'
import { useSystem } from './useSystem'

const useRequest = createFetch({
  baseUrl: (requestConfig.baseUrl ?? '') + `/${useSystem().currentSystem.value}`,
  options: {
    beforeFetch(ctx) {
      const { currentWarehouse } = useWarehouse()
      ctx.options.headers = {
        'Content-Type': 'application/json',
        'Authorization': localStorage.getItem('token') ?? '',
        'Inform-Current-Warehouse': useSystem().currentSystem.value === 'wms' ? currentWarehouse.value ?? '' : '',
        ...ctx.options.headers,
      }
    },
    async afterFetch(ctx) {
      if (ctx.response.headers.get('Content-Type')?.includes('application/json')) {
        const parsedData = await processResponse(ctx.response)
        if (parsedData) {
          ctx.data = parsedData
        }
        else {
          ctx.data = null
        }
      }
      return ctx
    },
    onFetchError(ctx) {
      if (ctx.error instanceof Error) {
        switch (ctx.error.message) {
          case 'network':
            window.$message?.error(t('error.network'))
            break
          case 'unauthorized':
            window.$message?.error(t('error.unauthorized'))
            useAuth().logout()
            break
          case 'failed':
            // do nothing, error message is handled in processResponse
            break
          default:
            window.$message?.error(t(ctx.error.message))
        }
      }
      return ctx
    },
  },
})

async function processResponse(response: Response): Promise<any | undefined> {
  if (!response.ok) {
    throw new Error('network')
  }
  const parsedResponse: {
    code: number
    data: any
    msg: string
  } = await response.json()
  const path = window.location.hash
  if (!path.includes('login') && parsedResponse.code === 401) {
    // await stw()
    window.location.hash = '/login'
    window.location.reload()
  }
  if ((parsedResponse.code < 200 || parsedResponse.code > 299) && parsedResponse.code !== 401) {
    throw new Error(parsedResponse.msg || 'error')
  }
  return parsedResponse.data ?? 'success'
}

export { useRequest }
