import { usePerm } from './usePerm'
import { useRequest } from './useRequest'
import { useSystem } from './useSystem'
import { useLocalStorage } from '@vueuse/core'

// /sys/user/changeTenant
const { currentSystem } = useSystem()
const currentTenant = useLocalStorage(`current${currentSystem.value?.toUpperCase()}Tenant`, '')
const tenantList = ref<{
  code: string
  name: string
}[]>([])

function useTenant() {
  function fetchTenantList() {
    // I don't know why backend use warehouseEnv to store tenant
    const url = '/sys/warehouse/warehouseEnv/' + currentSystem.value
    useRequest<{
      code: string
      name: string
    }[]>(url)
      .then((res) => {
        tenantList.value = res.data.value || []
        // if (currentTenant.value === '' || !tenantList.value.find(i => i.code === currentTenant.value)) {
        //   currentTenant.value = tenantList.value[0].code
        // }
      })
  }

  onMounted(() => {
    fetchTenantList()
  })

  async function toggleTenant(newVal: string) {
    const {error} = await useRequest('/sys/user/changeWarehouse').post({
      code: newVal,
      firstRouter: currentSystem.value
    })
    if (!error.value)
      currentTenant.value = newVal
  }

  const filteredTenantList = computed(() => {
    // return tenantList.value.filter(i => hasPerm(i.code))
    return tenantList.value
  })
  return {
    currentTenant,
    toggleTenant,
    tenantList: filteredTenantList,
    refreshTenantList: fetchTenantList,
  }
}

export {
  useTenant,
}