import { useRequest } from './useRequest'

interface DictState<T> {
  data: Ref<Record<string, T>>
  loaded: boolean
  url: string
}

// 定义字典类型
const dictStates: Record<string, DictState<any>> = {
  sys: {
    data: ref<Record<string, { dictLabel: string, dictValue: string }[]>>({}),
    loaded: false,
    url: '/sys/dict/type/all',
  },
  user: {
    data: ref<Record<string, { code: string, deptId: string, id: string, name: string }>>({}),
    loaded: false,
    url: '/sys/dict/type/dictTable/user',
  },
  department: {
    data: ref<Record<string, { name: string, id: string }>>({}),
    loaded: false,
    url: '/sys/dict/type/dictTable/dept',
  },
}

function useDict() {
  // 通用获取字典方法
  async function fetchDict(type: keyof typeof dictStates) {
    const state = dictStates[type]
    if (state.loaded)
      return

    const { data } = await useRequest<any>(state.url)
    if (!data.value)
      throw new Error('字典数据获取失败')

    // 对于 sys 字典，将整数字符串转换为数字
    // if (type === 'sys') {
    //   Object.keys(data.value).forEach((key) => {
    //     data.value[key] = data.value[key].map((item: { dictLabel: string, dictValue: string }) => ({
    //       ...item,
    //       dictValue: /^\d+$/.test(item.dictValue) ? parseInt(item.dictValue, 10) : item.dictValue
    //     }))
    //   })
    // }

    state.data.value = data.value
    state.loaded = true
  }

  async function fetchAllDict() {
    await Promise.all(Object.keys(dictStates).map(type => fetchDict(type as keyof typeof dictStates)))
  }

  return {
    sysDict: dictStates.sys.data,
    userDict: dictStates.user.data,
    departmentDict: dictStates.department.data,
    fetchSysDict: () => fetchDict('sys'),
    fetchUserDict: () => fetchDict('user'),
    fetchDepartmentDict: () => fetchDict('department'),
    fetchAllDict,
  }
}

export { useDict }
