import type { App } from 'vue'
import { createI18n } from 'vue-i18n'
import enUS from '../../locales/en_US.json'
import zhCN from '../../locales/zh_CN.json'
import { useRequest } from '@/hooks/useRequest'
import { local } from '@/utils/storage'
import { systemConfig } from '@/config/system'

const localMessages = {
  zhCN,
  enUS,
}
export function mergeLocalMessages(messages?: { zhCN?: Record<string, string>, enUS?: Record<string, string> }) {
  if (messages?.zhCN) {
    localMessages.zhCN = {
      ...localMessages.zhCN,
      ...messages.zhCN,
    }
  }
  if (messages?.enUS) {
    localMessages.enUS = {
      ...localMessages.enUS,
      ...messages.enUS,
    }
  }
}

export const i18n = createI18n({
  legacy: false,
  locale: local.get('lang') || systemConfig.defaultLanguage, // 默认显示语言
  messages: localMessages,
  missingWarn: false,
})

let isMergeRemoteI18n = false
export async function mergeRemoteI18n() {
  if (isMergeRemoteI18n) {
    return
  }
  const [{ data: zhData }, { data: enData }] = await Promise.all([
    useRequest<Record<string, string>>('/sys/language/getLanguageEV?languageEV=zh_CN'),
    useRequest<Record<string, string>>('/sys/language/getLanguageEV?languageEV=en_US'),
  ])
  if (!zhData.value || !enData.value) {
    return
  }

  i18n.global.mergeLocaleMessage('zhCN', {
    ...zhData.value,
  })
  i18n.global.mergeLocaleMessage('enUS', {
    ...enData.value,
  })
  isMergeRemoteI18n = true
}
export const t = i18n.global.t as (key: string, ...args: any[]) => string

export function installI18n(app: App) {
  app.use(i18n)
}
