import type { SystemOptions } from '@/types/configType'

const systemConfig: SystemOptions = reactive({
  list: [
    {
      key: 'auth',
      homePath: '/auth/dashboard/workbench',
    },
    {
      key: 'wcs',
      homePath: '/wcs/dashboard/workbench',
    },
    {
      key: 'wms',
      homePath: '/wms/dashboard/workbench',
    },
  ],
  appName: 'Inform',
  defaultLanguage: 'zhCN',
  footerText: 'Copyright © 2025 Inform',
  table: {
    firstColumnRouter: false,
  },
})

function setupSystemConfig(config?: SystemOptions) {
  if (!config)
    return

  Object.assign(systemConfig, config)
  console.log(systemConfig)
}

export {
  systemConfig,
  setupSystemConfig,
}
