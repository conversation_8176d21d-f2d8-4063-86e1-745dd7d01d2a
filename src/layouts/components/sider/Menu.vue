<script setup lang="ts">
import type { MenuInst } from 'naive-ui'
import { useSystemRouter } from '@/hooks/useSystemRouter'
import { useApp } from '@/hooks'

const route = useRoute()
const { state } = useApp()
const { activeMenu, menus } = useSystemRouter()

const menuInstRef = ref<MenuInst | null>(null)
watch(
  () => route.path,
  () => {
    menuInstRef.value?.showOption(activeMenu.value as string)
  },
  {
    immediate: true,
    deep: false,
  },
)
</script>

<template>
  <n-menu
    ref="menuInstRef"
    :collapsed="state.collapsed"
    :indent="20"
    :collapsed-width="64"
    :options="menus"
    :value="activeMenu"
  />
</template>
