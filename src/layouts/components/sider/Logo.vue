<script setup lang="ts">
import { useApp } from '@/hooks'

const router = useRouter()
const { state } = useApp()
</script>

<template>
  <div
    class="h-60px text-xl flex-center cursor-pointer gap-2 p-x-2"
    @click="router.push('/')"
  >
    <img src="/imgs/logo-with-bg.png" class="h-40px">
    <div v-if="!state.collapsed" class="flex-col flex text-red-8 font-600">
      <span class="text-lg mb-[-4px]">Inform</span>
      <span class="text-xl mt-[-4px]">System</span>
    </div>
  </div>
</template>
