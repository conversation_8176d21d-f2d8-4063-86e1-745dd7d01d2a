<script setup lang="ts">
import { useApp } from '@/hooks'

const { reloadPage } = useApp()

const loading = ref(false)

function handleReload() {
  loading.value = true
  reloadPage()
  setTimeout(() => {
    loading.value = false
  }, 800)
}
</script>

<template>
  <n-tooltip placement="bottom" trigger="hover">
    <template #trigger>
      <CommonWrapper @click="handleReload">
        <div :class="{ 'animate-spin': loading }" class="i-icon-park-outline-refresh h-1.2rem w-1.2rem" />
      </CommonWrapper>
    </template>
    <span>{{ $t('common.reload') }}</span>
  </n-tooltip>
</template>
