<script setup lang="ts">
import type { DropdownMixedOption } from 'naive-ui/es/dropdown/src/interface'
import { useTab } from '@/hooks'
import { IconRender } from '@/utils'

const { allTabs } = useTab()

const { t } = useI18n()

function renderDropTabsLabel(option: any) {
  return t(option.meta.title)
}
function renderDropTabsIcon(option: any) {
  return IconRender(option.meta.icon)!()
}

const router = useRouter()
function handleDropTabs(key: string, option: any) {
  router.push(option.fullPath)
}
</script>

<template>
  <n-dropdown
    :options="allTabs as unknown as DropdownMixedOption[]"
    :render-label="renderDropTabsLabel"
    :render-icon="renderDropTabsIcon"
    trigger="click"
    size="small"
    @select="handleDropTabs"
  >
    <CommonWrapper>
      <div class="i-icon-park-outline-application-menu h-1.2rem w-1.2rem" />
    </CommonWrapper>
  </n-dropdown>
</template>
