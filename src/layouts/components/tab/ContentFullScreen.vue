<script setup lang="ts">
import { useApp } from '@/hooks'

const { state, toggleContentFullScreen } = useApp()
</script>

<template>
  <n-tooltip placement="bottom" trigger="hover">
    <template #trigger>
      <CommonWrapper @click="toggleContentFullScreen">
        <div v-if="state.contentFullScreen" class="i-icon-park-outline-off-screen-one h-1.2rem w-1.2rem" />
        <div v-else class="i-icon-park-outline-full-screen-one h-1.2rem w-1.2rem" />
      </CommonWrapper>
    </template>
    <span>{{ $t('app.togglContentFullScreen') }}</span>
  </n-tooltip>
</template>
