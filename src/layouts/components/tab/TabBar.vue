<script setup lang="tsx">
import ContentFullScreen from './ContentFullScreen.vue'
import DropTabs from './DropTabs.vue'
import Reload from './Reload.vue'
import type { TabItem } from '@/hooks'
import { useApp, useTab } from '@/hooks'
import type { NTab } from 'naive-ui'

const { closeTab, closeOtherTabs, closeLeftTabs, closeRightTabs, closeAllTabs, currentTabPath, tabs, isLastTab } = useTab()
const { reloadPage } = useApp()

const router = useRouter()
function handleTab(route: TabItem) {
  router.push(route.fullPath)
}
async function handleClose(path: string) {
  await closeTab(path)
}
const { t } = useI18n()
const options = computed(() => {
  return [
    {
      label: t('common.reload'),
      key: 'reload',
      icon: () => h('div', { class: 'i-icon-park-outline-redo h-1.2rem w-1.2rem' }),
    },
    {
      label: t('common.close'),
      key: 'closeCurrent',
      icon: () => h('div', { class: 'i-icon-park-outline-close h-1.2rem w-1.2rem' }),
    },
    {
      label: t('app.closeOther'),
      key: 'closeOther',
      icon: () => h('div', { class: 'i-icon-park-outline-delete-four h-1.2rem w-1.2rem' }),
    },
    {
      label: t('app.closeLeft'),
      key: 'closeLeft',
      icon: () => h('div', { class: 'i-icon-park-outline-to-left h-1.2rem w-1.2rem' }),
    },
    {
      label: t('app.closeRight'),
      key: 'closeRight',
      icon: () => h('div', { class: 'i-icon-park-outline-to-right h-1.2rem w-1.2rem' }),
    },
    {
      label: t('app.closeAll'),
      key: 'closeAll',
      icon: () => h('div', { class: 'i-icon-park-outline-fullwidth h-1.2rem w-1.2rem' }),
    },
  ]
})
const showDropdown = ref(false)
const x = ref(0)
const y = ref(0)
const currentRoute = ref()

function handleSelect(key: string) {
  showDropdown.value = false
  interface HandleFn {
    [key: string]: any
  }
  const handleFn: HandleFn = {
    reload() {
      reloadPage()
    },
    async closeCurrent() {
      await closeTab(currentRoute.value.path)
    },
    async closeOther() {
      await closeOtherTabs(currentRoute.value.path)
    },
    async closeLeft() {
      await closeLeftTabs(currentRoute.value.path)
    },
    async closeRight() {
      await closeRightTabs(currentRoute.value.path)
    },
    async closeAll() {
      await closeAllTabs()
    },
  }
  handleFn[key]()
}
function handleContextMenu(e: MouseEvent, route: TabItem) {
  e.preventDefault()
  currentRoute.value = route
  showDropdown.value = false
  nextTick().then(() => {
    showDropdown.value = true
    x.value = e.clientX
    y.value = e.clientY
  })
}
function onClickoutside() {
  showDropdown.value = false
}

const tabRefs = ref<Array<InstanceType<typeof NTab>>>([])

watch(currentTabPath, async () => {
  await new Promise(resolve => setTimeout(resolve, 100))
  const currentTab = tabRefs.value?.find(i => {
    return i.name === currentTabPath.value
  })
  if (!currentTab) return
  await nextTick()
  if (isLastTab.value) {
    const contentDOM = document.querySelectorAll('.v-x-scroll')
    if (!contentDOM) return
    contentDOM.forEach(i => {
      if (!i.children[0].classList.contains('n-tabs-nav-scroll-content'))
        return
      i.scrollTo({
        left: 10000,
        behavior: 'smooth',
      })
    })
    return
  }
  try {
    // @ts-expect-error
    currentTab.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'nearest',
    })
  } catch (error) {
    console.warn(error)
  }
})
</script>

<template>
  <div class="wh-full flex items-end">
    <n-tabs type="card" size="small" :tabs-padding="10" :value="currentTabPath" @close="handleClose">
      <n-tab v-for="item in tabs" :key="item.path" closable ref="tabRefs" :name="item.path" @click="handleTab(item)"
        @contextmenu="handleContextMenu($event, item)">
        <div class="flex-x-center gap-2">
          <div :class="`${item.meta.icon} text-18px`" /> {{ $t(item.meta.title as string) }}
        </div>
      </n-tab>
      <template #suffix>
        <Reload />
        <ContentFullScreen />
        <DropTabs />
      </template>
    </n-tabs>
    <n-dropdown placement="bottom-start" trigger="manual" :x="x" :y="y" :options="options" :show="showDropdown"
      :on-clickoutside="onClickoutside" @select="handleSelect" />
  </div>
</template>
