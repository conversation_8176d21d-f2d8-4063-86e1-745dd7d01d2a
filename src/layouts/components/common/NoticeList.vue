<script setup lang="ts">
import { t } from '@/modules/i18n'

interface Props {
  list?: Array<{
    id: number
    type: number
    title: string
    content: string
    sendDate: string
    firstRouter: string
  }>
}
const { list } = defineProps<Props>()

const emit = defineEmits<Emits>()
interface Emits {
  (e: 'read', val: number): void
  (e: 'showMore'): void
}
</script>

<template>
  <n-scrollbar style="height: 400px; width: 300px;">
    <n-list hoverable clickable>
      <template v-if="list?.length">
        <n-list-item v-for="(item) in list" :key="item.id" @click="emit('read', item.id)" class="w-300px">
          <n-thing content-indented>
            <template #header>
              <n-ellipsis :line-clamp="1">
                {{ item.title }}
              </n-ellipsis>
            </template>
            <template v-if="item.title" #header-extra>
              <n-tag :bordered="false" type="info" size="small">
                {{ item.firstRouter.replace('/', '') }}
              </n-tag>
            </template>
            <template v-if="item.content" #description>
              <n-ellipsis :line-clamp="2">
                {{ item.content }}
              </n-ellipsis>
            </template>
            <template #footer>
              {{ item.sendDate.split(' ')[0] }}
            </template>
          </n-thing>

        </n-list-item>
      </template>
      <template v-else>
        <n-empty class="mt-40 h-180px" description="暂无消息" />
      </template>
      <template #footer>
        <n-button @click="emit('showMore')" text type="primary" class="w-full h-16px">
          {{ t('common.more') }}
        </n-button>
      </template>
    </n-list>

  </n-scrollbar>
</template>
