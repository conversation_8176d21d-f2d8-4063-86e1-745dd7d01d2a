<script setup lang="ts">
import { toRefs } from 'vue'
import LayoutSelector from './LayoutSelector.vue'
import { useApp } from '@/hooks'

const { state, resetAllTheme, setPrimaryColor, reloadPage, toggleColorWeak, toggleGrayMode } = useApp()
const { showSetting, showTabs, showFooter, showBreadcrumb, showBreadcrumbIcon, showWatermark } = toRefs(state.value)

const { t } = useI18n()

const transitionSelectorOptions = computed(() => {
  return [
    {
      label: t('app.transitionNull'),
      value: '',
    },
    {
      label: t('app.transitionFadeSlide'),
      value: 'fade-slide',
    },
    {
      label: t('app.transitionFadeBottom'),
      value: 'fade-bottom',
    },
    {
      label: t('app.transitionFadeScale'),
      value: 'fade-scale',
    },
    {
      label: t('app.transitionZoomFade'),
      value: 'zoom-fade',
    },
    {
      label: t('app.transitionZoomOut'),
      value: 'zoom-out',
    },
    {
      label: t('app.transitionSoft'),
      value: 'fade',
    },
  ]
})

const palette = [
  '#ffb8b8',
  '#d03050',
  '#F0A020',
  '#fff200',
  '#ffda79',
  '#18A058',
  '#006266',
  '#22a6b3',
  '#18dcff',
  '#2080F0',
  '#c56cf0',
  '#be2edd',
  '#706fd3',
  '#4834d4',
  '#130f40',
  '#4b4b4b',
]

function resetSetting() {
  window.$dialog.warning({
    title: t('app.resetSettingTitle'),
    content: t('app.resetSettingContent'),
    positiveText: t('common.confirm'),
    negativeText: t('common.cancel'),
    onPositiveClick: () => {
      resetAllTheme()
      window.$message.success(t('app.resetSettingMeaasge'))
    },
  })
}
</script>

<template>
  <n-drawer v-model:show="showSetting" :width="360">
    <n-drawer-content :title="t('app.systemSetting')" closable>
      <n-space vertical>
        <!-- <n-divider>{{ $t('app.layoutSetting') }}</n-divider>
        <LayoutSelector v-model:value="state.layoutMode" /> -->
        <n-divider>{{ $t('app.themeSetting') }}</n-divider>
        <n-space justify="space-between">
          {{ $t('app.colorWeak') }}
          <n-switch :value="state.colorWeak" @update:value="toggleColorWeak" />
        </n-space>
        <n-space justify="space-between">
          {{ $t('app.blackAndWhite') }}
          <n-switch :value="state.grayMode" @update:value="toggleGrayMode" />
        </n-space>
        <n-space align="center" justify="space-between">
          {{ $t('app.themeColor') }}
          <n-color-picker v-model:value="state.primaryColor" class="w-10em" :swatches="palette"
            @update:value="setPrimaryColor" />
        </n-space>
        <n-space align="center" justify="space-between">
          {{ $t('app.pageTransition') }}
          <n-select v-model:value="state.transitionAnimation" class="w-10em" :options="transitionSelectorOptions"
            @update:value="reloadPage" />
        </n-space>

        <n-divider>{{ $t('app.interfaceDisplay') }}</n-divider>
        <n-space justify="space-between">
          {{ $t('app.logoDisplay') }}
          <n-switch v-model:value="state.showLogo" />
        </n-space>
        <n-space justify="space-between">
          {{ $t('app.topProgress') }}
          <n-switch v-model:value="state.showProgress" />
        </n-space>
        <n-space justify="space-between">
          {{ $t('app.multitab') }}
          <n-switch v-model:value="showTabs" />
        </n-space>
        <n-space justify="space-between">
          {{ $t('app.bottomCopyright') }}
          <n-switch v-model:value="showFooter" />
        </n-space>
        <n-space justify="space-between">
          {{ $t('app.breadcrumb') }}
          <n-switch v-model:value="showBreadcrumb" />
        </n-space>
        <n-space justify="space-between">
          {{ $t('app.BreadcrumbIcon') }}
          <n-switch v-model:value="showBreadcrumbIcon" />
        </n-space>
        <n-space justify="space-between">
          {{ $t('app.watermake') }}
          <n-switch v-model:value="showWatermark" />
        </n-space>
      </n-space>

      <template #footer>
        <n-button type="error" @click="resetSetting">
          {{ $t('app.reset') }}
        </n-button>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
