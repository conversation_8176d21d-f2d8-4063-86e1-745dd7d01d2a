<script setup lang="ts">
import { useApp } from '@/hooks'

const { state } = useApp()
</script>

<template>
  <n-tooltip placement="bottom" trigger="hover">
    <template #trigger>
      <CommonWrapper @click="state.showSetting = !state.showSetting">
        <div>
          <div class="i-icon-park-outline-setting-two h-1.2rem w-1.2rem" />
        </div>
      </CommonWrapper>
    </template>
    <span>{{ $t('app.setting') }}</span>
  </n-tooltip>
</template>
