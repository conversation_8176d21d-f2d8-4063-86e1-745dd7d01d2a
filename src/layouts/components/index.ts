import BackTop from './common/BackTop.vue'
import Setting from './common/Setting.vue'

import SettingDrawer from './common/SettingDrawer.vue'
import Breadcrumb from './header/Breadcrumb.vue'
import CollapaseButton from './header/CollapaseButton.vue'
import FullScreen from './header/FullScreen.vue'
import Notices from './header/Notices.vue'
import Search from './header/Search.vue'

import UserCenter from './header/UserCenter.vue'

import Logo from './sider/Logo.vue'
import Menu from './sider/Menu.vue'
import TabBar from './tab/TabBar.vue'
import ToggleWarehouse from './header/ToggleWarehouse.vue'
import ToggleSystem from './header/ToggleSystem.vue'
import ToggleTenant from './header/ToggleTenant.vue'

export {
  BackTop,
  Breadcrumb,
  CollapaseButton,
  FullScreen,
  Logo,
  Menu,
  Notices,
  Search,
  Setting,
  SettingDrawer,
  TabBar,
  UserCenter,
  ToggleWarehouse,
  ToggleSystem,
  ToggleTenant
}
