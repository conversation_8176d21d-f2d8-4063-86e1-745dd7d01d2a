<script setup lang="ts">
import { useSystem, useAuth } from '@/hooks'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { currentSystem, systemList } = useSystem()
const { userInfo } = useAuth()
const systemFilterList = computed(() => userInfo.value?.appList)
</script>

<template>
  <div class="mx-1">
    <n-select size="small" class="w-160px" v-model:value="currentSystem"
      :options="systemList?.filter(item => item.homePath).filter(item => systemFilterList?.includes(item.key)).map(item => ({ label: item.key, value: item.key }))"
      :render-tag="() => `${t('tip.currentSystem')}：${currentSystem}`" trigger="click" />
  </div>
</template>
