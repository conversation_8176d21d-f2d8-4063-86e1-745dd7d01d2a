<script setup lang="ts">
import { useApp } from '@/hooks'

const { state, toggleCollapse } = useApp()
</script>

<template>
  <n-tooltip placement="bottom" trigger="hover">
    <template #trigger>
      <CommonWrapper @click="toggleCollapse()">
        <div v-if="state.collapsed" class="i-icon-park-outline-menu-unfold h-1.2rem w-1.2rem" />
        <div v-else class="i-icon-park-outline-menu-fold h-1.2rem w-1.2rem" />
      </CommonWrapper>
    </template>
    <span>{{ $t('app.toggleSider') }}</span>
  </n-tooltip>
</template>
