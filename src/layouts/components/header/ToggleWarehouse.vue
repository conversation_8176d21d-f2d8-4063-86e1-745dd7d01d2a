<script setup lang="ts">
import { useWarehouse } from '@/hooks'
import { t } from '@/modules/i18n';

const { currentWarehouse, warehouseList, toggleWarehouse, refreshWarehouseList } = useWarehouse()
const options = computed(() => warehouseList.value.map(i => ({
  label: t(i.name),
  value: i.code,
  key: i.code,
})))
onMounted(() => {
  refreshWarehouseList()
})
</script>

<template>
  <n-popselect :value="currentWarehouse" @update-value="toggleWarehouse" :options="options" trigger="click">
    <n-button class="rounded-2xl overflow-hidden">{{currentWarehouse ?
      `${$t('tip.currentStorage')}：${t(warehouseList.find(i => i.code === currentWarehouse)?.name ?? '')}`
      : $t('tip.selectStorage')
    }}</n-button>
  </n-popselect>
</template>

