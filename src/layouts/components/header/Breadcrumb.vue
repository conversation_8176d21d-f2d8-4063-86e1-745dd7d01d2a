<script setup lang="ts">
import type { RouteLocationMatched } from 'vue-router'
import { t } from '@/modules/i18n'
import { useApp } from '@/hooks'

const router = useRouter()
const route = useRoute()
const routes = computed(() => {
  return route.matched.filter(i => i.name !== 'root')
})
const { state } = useApp()

function renderTitle(route: RouteLocationMatched) {
  if (route.name === 'appRoot')
    return t('route.appRoot')
  return t(route.meta.title as string)
}
</script>

<template>
  <TransitionGroup v-if="state.showBreadcrumb" name="list" tag="ul" style="display: flex; gap:1em;">
    <n-el
      v-for="(item) in routes"
      :key="item.path"
      tag="li" style="
            color: var(--text-color-2);
            transition: 0.3s var(--cubic-bezier-ease-in-out);
          "
      class="flex-center gap-2 cursor-pointer split"
      @click="router.push(item.path)"
    >
      <div :class="`${item.meta.icon} text-14px`" />
      <span class="whitespace-nowrap">{{ renderTitle(item) }}</span>
    </n-el>
  </TransitionGroup>
</template>

<style>
.split:not(:first-child)::before {
   content: '/';
   padding-right:0.6em;
}

.list-move,
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from,.list-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.list-leave-active {
  position: absolute;
}
</style>
