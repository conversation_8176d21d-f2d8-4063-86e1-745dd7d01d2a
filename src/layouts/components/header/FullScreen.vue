<script setup lang="ts">
import { useApp } from '@/hooks'

const { toggleFullScreen, fullScreen } = useApp()

useMagicKeys({
  passive: false,
  onEventFired(e) {
    if (e.key === 'F11' && e.type === 'keydown') {
      e.preventDefault()
      toggleFullScreen()
    }
  },
})
</script>

<template>
  <n-tooltip placement="bottom" trigger="hover">
    <template #trigger>
      <CommonWrapper @click="toggleFullScreen">
        <div v-if="fullScreen" class="i-icon-park-outline-off-screen h-1.2rem w-1.2rem" />
        <div v-else class="i-icon-park-outline-full-screen h-1.2rem w-1.2rem" />
      </CommonWrapper>
    </template>
    <span>{{ $t('app.toggleFullScreen') }}</span>
  </n-tooltip>
</template>
