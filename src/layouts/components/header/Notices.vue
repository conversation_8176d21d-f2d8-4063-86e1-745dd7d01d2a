<script setup lang="ts">
import NoticeList from '../common/NoticeList.vue'
import { useAuth, useRequest, useWS } from '@/hooks'
import { router } from '@/router'
import { useSystem } from '@/hooks'
import { useTable } from '@/hooks/useTable'
import { t } from '@/modules/i18n'
import { CompareEnum, LinkEnum } from '@/constants/query'
import type { QueryForm } from '@/hooks/useTableType'

const { channel, clearChannel } = useWS()
const { setTemp, currentSystem } = useSystem()

async function handleRead(id: number) {
  let data
  channel.value = channel.value.filter(i => {
    if (i.id === id) {
      data = i
      return false
    }
    return true
  })
  if (data) {
    try {
      await useRequest(`/sys/notice/read/${data.id}`)
    } catch (e) {
      console.error(e)
    }
    if (data.menuUrl) {
      const target = `${data.firstRouter}${data.menuUrl}`
      const params = data.noticeParams
      const query = Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&')
      if (data.firstRouter.replace('/', '') === currentSystem.value) {
        router.push(`${target}?${query}`)
      } else {
        const temp = data.firstRouter.replace('/', '')
        setTemp(temp)
        localStorage.setItem('temp_router', `${target}?${query}`)
        window.open(`${window.location.origin}`)
      }
    }
  }
}
const massageCount = computed(() => {
  return channel.value.length
})

const { userInfo } = useAuth()
const { state, refresh } = useTable(
  {
    page: '/sys/noticeUser/page',
    info: '/sys/noticeUser/info',
  },
  [
    {
      title: t("system.menu.title"),
      key: "title",
      query: "input",
    },
    {
      title: t('system.menu.readStatus'),
      key: 'readStatus',
      query: 'select',
      options: [
        {
          label: t('system.options.unread'),
          value: 0,
        },
        {
          label: t('system.options.read'),
          value: 1,
        },
      ],
      defaultQueryValue: 0,
    },
    {
      title: t("system.menu.content"),
      key: "content",
    },
    {
      title: t("system.menu.firstRouter"),
      key: "firstRouter",
    },
    {
      title: t("system.menu.remark"),
      key: "remark",
    },
    {
      title: t("system.menu.sendDate"),
      key: "sendDate",
    },
  ],
  {
    actionHideList: ["edit", "save", "config", "deleteMul", "advancedQuery", "delete", "groupSearch", "importTable", "exportTable"],
    beforeRequest: (params) => {
      if (params.length > 0)
        return [
          {
            link: LinkEnum.AND,
            data: [
              ...(params[0].data ?? []),
              {
                colkey: 'receiver',
                op: CompareEnum.EQUAL,
                opval: userInfo.value?.id,
                link: LinkEnum.AND
              }
            ]
          }
        ] as unknown as QueryForm
      else
        return [
          {
            link: LinkEnum.AND,
            data: [
              {
                colkey: 'receiver',
                op: CompareEnum.EQUAL,
                opval: userInfo.value?.id,
                link: LinkEnum.AND
              }
            ]
          }
        ]
    },
    defaultSort: {
      columnKey: 'id',
      order: 'descend'
    },
    immediate: 'off'
  },
);

const visible = ref(false)
const handleShowMore = () => {
  visible.value = true
  refresh()
}
const handleReadAll = async () => {
  const { error } = await useRequest('/sys/notice/readAll')
  if (!error.value) {
    refresh()
    clearChannel()
  }
}
</script>

<template>
  <n-popover placement="bottom" trigger="click" arrow-point-to-center class="!p-0">
    <template #trigger>
      <n-tooltip placement="bottom" trigger="hover">
        <template #trigger>
          <CommonWrapper>
            <n-modal v-model:show="visible" :title="t('system.menu.notice')" class="w-1000px" closable preset="card"
              :z-index="1000">
              <mar-wrapper v-model="state">
                <template #toolbarExt>
                  <n-button type="warning" @click="handleReadAll">
                    {{ t('common.markReadAll') }}
                  </n-button>
                </template>
              </mar-wrapper>
            </n-modal>
            <n-badge :value="massageCount" :max="99" style="color: unset">
              <div class="i-icon-park-outline-remind h-1.2rem w-1.2rem" />
            </n-badge>
          </CommonWrapper>
        </template>
        <span>{{ $t('app.notificationsTips') }}</span>
      </n-tooltip>
    </template>
    <NoticeList :list="channel" @read="handleRead" @showMore="handleShowMore" />
    <!-- <n-tabs v-model:value="currentTab" v-if="false" type="line" animated justify-content="space-evenly" class="w-390px">
      <n-tab-pane :name="0">
        <template #tab>
          <n-space class="w-390px" justify="center">
            {{ $t('app.notifications') }}
            <n-badge type="info" :value="groupMessage[0]?.filter(i => !i.isRead).length" :max="99" />
          </n-space>
        </template>
      </n-tab-pane>
      <n-tab-pane :name="1">
        <template #tab>
          <n-space class="w-130px" justify="center">
            {{ $t('app.messages') }}
            <n-badge type="warning" :value="groupMessage[1]?.filter(i => !i.isRead).length" :max="99" />
          </n-space>
        </template>
        <NoticeList :list="groupMessage[1]" @read="handleRead" />
      </n-tab-pane>
      <n-tab-pane :name="2">
        <template #tab>
          <n-space class="w-130px" justify="center">
            {{ $t('app.todos') }}
            <n-badge type="error" :value="groupMessage[2]?.filter(i => !i.isRead).length" :max="99" />
          </n-space>
        </template>
        <NoticeList :list="groupMessage[2]" @read="handleRead" />
      </n-tab-pane> -->
  </n-popover>
</template>
