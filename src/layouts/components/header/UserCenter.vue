<script setup lang="tsx">
import { useAuth } from '@/hooks'

const { t } = useI18n()

const { logout } = useAuth()
const router = useRouter()

const options = computed(() => {
  return [
    {
      label: t('app.userCenter'),
      key: 'userCenter',
      icon: () => h('div', { class: 'i-icon-park-outline-user h-1.2rem w-1.2rem' }),
    },
    {
      type: 'divider',
      key: 'd1',
    },
    {
      label: t('app.loginOut'),
      key: 'loginOut',
      icon: () => h('div', { class: 'i-icon-park-outline-logout h-1.2rem w-1.2rem' }),
    },
  ]
})
function handleSelect(key: string | number) {
  if (key === 'loginOut') {
    window.$dialog?.create({
      title: t('app.loginOutTitle'),
      content: t('app.loginOutContent'),
      positiveText: t('common.confirm'),
      negativeText: t('common.cancel'),
      onPositiveClick: () => {
        logout()
      },
    })
  }
  if (key === 'userCenter')
    router.push('/user')
}
</script>

<template>
  <n-dropdown
    trigger="click"
    :options="options"
    @select="handleSelect"
  >
    <n-tooltip>
      <template #trigger>
        <CommonWrapper>
          <div>
            <div class="i-icon-park-outline-user h-1.2rem w-1.2rem" />
          </div>
        </CommonWrapper>
      </template>
      <span>{{ $t('app.userCenter') }}</span>
    </n-tooltip>
  </n-dropdown>
</template>
