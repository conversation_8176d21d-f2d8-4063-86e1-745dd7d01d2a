<script setup lang="ts">
import { useTenant } from '@/hooks/useTenant';
import { t } from '@/modules/i18n';

const { currentTenant, tenantList, toggleTenant } = useTenant()
const options = computed(() => tenantList.value.map(i => ({
  label: t(i.name),
  value: i.code,
  key: i.code,
})))
watch(currentTenant, (val) => {
  window.location.reload()
})
</script>

<template>
  <div class="dade-border-node flex justify-center items-center border border-solid border-1 border-gray-200 dark:border-gray-600 px-3 rounded-3xl">
      {{ $t('tip.currentStorage') }}:
    <n-select :value="currentTenant" @update:value="toggleTenant" :options="options" class="w-24"
      :placeholder="$t('tip.selectStorage')" filterable />
  </div>
</template>

<style scoped>
::v-deep(.n-base-selection) {
    border-radius: 0;
    --n-border: none;
    --n-border-active: none;
    --n-border-focus: none;
    --n-border-hover: none;
    --n-border-radius: 0;
    --n-box-shadow-active: none;
    --n-box-shadow-focus: none;
    --n-box-shadow-hover: none;
}
</style>
