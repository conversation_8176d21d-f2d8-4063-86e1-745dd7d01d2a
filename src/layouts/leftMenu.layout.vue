<script lang="ts" setup>
import {
  BackTop,
  Breadcrumb,
  CollapaseButton,
  FullScreen,
  Logo,
  Menu,
  Notices,
  Setting,
  TabBar,
  UserCenter,
  ToggleWarehouse,
  ToggleSystem,
  ToggleTenant
} from './components'
import { useLayout } from '@/hooks/useLayout'
import { useApp, useSystem, useWS } from '@/hooks'
import { router } from '@/router'
import { useRequest } from '@/hooks/useRequest'
import { NMarquee } from 'naive-ui'

const { state, pageNeedRefresh } = useApp()
const headerRef = ref<HTMLElement | null>(null)
const footerRef = ref<HTMLElement | null>(null)
const { height: headerH } = useElementSize(headerRef)
const { height: footerH } = useElementSize(footerRef)
const { headerHeight, footerHeight } = useLayout()
watch(headerH, (newHeight) => {
  headerHeight.value = newHeight
})
watch(footerH, (newHeight) => {
  footerHeight.value = newHeight
})
const { currentSystem } = useSystem()

const { open, close, channel, latest, messageBar } = useWS({
  url: '/websocket',
})

const currentMessage = ref<any>({})
const messageBarVisible = ref(false)
const isProcessingMessageBar = ref(false)

// 处理消息栈的函数
const processMessageBarStack = async () => {
  if (isProcessingMessageBar.value || messageBar.value.length === 0) {
    return
  }
  
  isProcessingMessageBar.value = true
  
  while (messageBar.value.length > 0) {
    // 从栈顶取出消息（后进先出）
    const message = messageBar.value.pop()
    currentMessage.value = message
    messageBarVisible.value = true
    
    // 等待3秒
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    messageBarVisible.value = false
    
    // 短暂延迟后处理下一个消息，避免闪烁
    if (messageBar.value.length > 0) {
      await new Promise(resolve => setTimeout(resolve, 200))
    }
  }
  
  isProcessingMessageBar.value = false
}

// 监听 messageBar 变化，当有新消息时开始处理
watch(messageBar, (newMessageBar) => {
  if (newMessageBar.length > 0 && !isProcessingMessageBar.value) {
    processMessageBarStack()
  }
}, { deep: true })

const unreadCount = computed(() => {
  return channel.value.length + (latest.value?.title ? 1 : 0)
})

const { setTemp } = useSystem()
async function handleMessageClick() {
  try {
    if (messageBarVisible.value && currentMessage.value.menuUrl) {
      try {
        await useRequest(`/sys/notice/read/${currentMessage.value.id}`)
      } catch (e) {
        console.error(e)
      }
      const target = `${currentMessage.value.firstRouter}${currentMessage.value.menuUrl}`
      const params = currentMessage.value.noticeParams
      const query = Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&')
      if (currentMessage.value.firstRouter.replace('/', '') === currentSystem.value) {
        router.push(`${target}?${query}`)
      } else {
        const temp = currentMessage.value.firstRouter.replace('/', '')
        setTemp(temp)
        localStorage.setItem('temp_router', `${target}?${query}`)
        window.open(`${window.location.origin}`)
      }
      channel.value = channel.value.filter(i => i.id !== currentMessage.value.id)
      
      // 点击后立即隐藏当前消息，继续处理栈中的下一个消息
      messageBarVisible.value = false
      // 清空当前消息的定时器，继续处理下一个消息
      if (messageBar.value.length > 0) {
        setTimeout(() => {
          processMessageBarStack()
        }, 200)
      }
    }
  } catch (e) {
    console.error(e)
  }
}

const floatBtnVisible = computed(() => state.value.contentFullScreen)

onMounted(() => {
  open()
  const tempRouter = localStorage.getItem('temp_router')
  localStorage.removeItem('temp_router')
  if (tempRouter) {
    router.push(tempRouter)
  }
})

function handleFloatBtnClick() {
  state.value.contentFullScreen = !state.value.contentFullScreen
}

onUnmounted(() => {
  close()
})
</script>

<template>
  <n-layout has-sider class="wh-full" embedded>
    <n-float-button v-if="floatBtnVisible" :right="120" :bottom="120" type="primary" class="z-999"
      @click="handleFloatBtnClick">
      <div class="w-20px h-20px i-icon-park-outline-off-screen-two"></div>
    </n-float-button>
    <n-layout-sider v-if="!state.contentFullScreen" bordered :collapsed="state.collapsed" collapse-mode="width"
      :collapsed-width="64" :width="240" content-style="display: flex;flex-direction: column;min-height:100%;">
      <Logo v-if="state.showLogo" />
      <n-scrollbar class="flex-1">
        <Menu />
      </n-scrollbar>
    </n-layout-sider>
    <n-layout class="h-full flex flex-col" content-style="display: flex;flex-direction: column;min-height:100%;"
      embedded :native-scrollbar="false">
      <n-layout-header ref="headerRef" bordered position="absolute" class="z-999">
        <div v-if="!state.contentFullScreen" class="h-60px flex-y-center justify-between">
          <div class="flex-y-center h-full">
            <CollapaseButton />
            <ToggleSystem />
            <Breadcrumb />
          </div>
          <div class="text-16px text-red-500" v-if="messageBarVisible">
            <div
              class="flex-y-center h-32px gap-2 min-w-60 justify-center px-4 py-2 rounded-full bg-red-50 dark:bg-red-800/20 transition-all duration-300 hover:bg-red-100 dark:hover:bg-red-800/30 cursor-pointer hover:shadow-sm"
              :class="{ 'pulse-animation': unreadCount > 0 }" @click="handleMessageClick">
              <div class="text-15px text-red-600 dark:text-red-400 font-medium overflow-hidden">
                <transition name="fade" mode="out-in">
                  <span v-if="messageBarVisible" class="inline-block whitespace-nowrap overflow-hidden text-ellipsis max-w-100"
                    :key="'latest'">
                    <NMarquee>
                    {{ currentMessage?.title }}
                    </NMarquee>
                  </span>
                </transition>
              </div>
            </div>
          </div>
          <div class="flex-y-center gap-1 h-full p-x-xl">
            <!-- <Search /> -->
            <ToggleWarehouse v-if="currentSystem === 'wms'" />
            <ToggleTenant v-else/>
            <Notices />
            <FullScreen />
            <DarkModeSwitch />
            <LangsSwitch />
            <Setting />
            <UserCenter />
          </div>
        </div>
        <TabBar v-if="!state.contentFullScreen && state.showTabs" class="h-45px" />
      </n-layout-header>
      <!-- 121 = 16 + 45 + 60 45是面包屑高度 60是标签栏高度 -->
      <!-- 56 = 16 + 40 40是页脚高度 -->
      <div class="flex-1 p-16px flex flex-col" :class="state.contentFullScreen ? 'p-0' : {
        'p-t-121px': state.showTabs,
        'p-b-56px': state.showFooter && !state.contentFullScreen,
        'p-t-76px': !state.showTabs,
        'p-t-61px': state.contentFullScreen,
      }">
        <router-view v-slot="{ Component, route }" class="flex-1">
          <transition :name="state.transitionAnimation" mode="out-in">
            <KeepAlive :exclude="pageNeedRefresh">
              <Suspense>
                <component :is="Component" v-if="state.loadFlag" :key="route.path" />
                <template #fallback>
                  <div class="flex-center flex-1">
                    <n-spin size="large" />
                  </div>
                </template>
              </Suspense>
            </KeepAlive>
          </transition>
        </router-view>
      </div>
      <n-layout-footer v-if="state.showFooter && !state.contentFullScreen" bordered position="absolute"
        class="h-40px flex-center">
        {{ state.footerText }}
      </n-layout-footer>
      <BackTop />
    </n-layout>
  </n-layout>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(-5px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(5px);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
  }

  70% {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}
</style>
