import type { Router } from 'vue-router'
import { useApp, useTab } from '@/hooks'
import { useSystemRouter } from '@/hooks/useSystemRouter'
import { t } from '@/modules/i18n'

export function setupRouterGuard(router: Router) {
  const { state, setActiveMenu, init } = useSystemRouter()
  const { state: appState } = useApp()
  const tabStore = useTab()

  router.beforeEach(async (to, from) => {
    try {
      // 如果 openStyle 为 0，表示新窗口打开
      if (!to.query.skip && to.meta.openStyle === 0) {
        window.open(`#/${to.fullPath.replace(/^\//, '')}?skip=true`)
        return false
      }
      // 开始 loadingBar
      appState.value.showProgress && window.$loadingBar?.start()

      // 判断有无TOKEN,登录鉴权
      const isLogin = Boolean(localStorage.getItem('token'))
      if (!isLogin) {
        if (to.name === 'login')
          return true

        if (to.name !== 'login') {
          const redirect = to.name === '404' ? undefined : to.fullPath
          return {
            path: '/login',
            query: { redirect },
          }
        }
        return false
      }

      // 判断路由有无进行初始化
      if (state.value === 'waiting') {
        await init()
        if (!router.getRoutes().find(item => item.path === to.path)) {
          // clear all tabs
          await tabStore.clearAllTabs()
          return {
            path: '/',
            replace: true,
          }
        }
        // 动态路由加载完回到根路由
        if (to.name === '404') {
          return {
            path: to.fullPath,
            replace: true,
            query: to.query,
            hash: to.hash,
          }
        }
      }

      return true
    }
    catch (error) {
      return false
    }
  })
  router.beforeResolve((to) => {
    try {
      // 设置菜单高亮
      const am = to.meta.activeMenu as string | undefined
      setActiveMenu(am ?? to.fullPath)
      // 添加tabs
      tabStore.addTab({
        meta: to.meta,
        path: to.path,
        fullPath: to.fullPath,
      })
      // 设置高亮标签;
      tabStore.setCurrentTab(to.path as string)
    }
    catch (error) {
      return false
    }
  })

  router.afterEach((to) => {
    try {
      // 修改网页标题
      document.title = t(to.meta.title as string)
      // 结束 loadingBar
      appState.value.showProgress && window.$loadingBar?.finish()
    }
    catch (error) {
      return false
    }
  })
}
