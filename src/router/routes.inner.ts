import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/layouts/index.vue'

export const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'root',
    redirect: '/appRoot',
    component: Layout,
    children: [
      {
        path: '/user',
        name: 'user',
        component: () => import('@/views/user/index.page.vue'),
        meta: {
          title: '用户中心',
        },
      },
      {
        path: '/403',
        name: '403',
        component: () => import('@/views/error/403/index.vue'),
        meta: {
          title: '用户无权限',
          withoutTab: true,
        },
      },
      {
        path: '/404',
        name: '404',
        component: () => import('@/views/error/404/index.vue'),
        meta: {
          title: '找不到页面',
          icon: 'i-icon-park-outline:ghost',
          withoutTab: true,
        },
      },
      {
        path: '/500',
        name: '500',
        component: () => import('@/views/error/500/index.vue'),
        meta: {
          title: '服务器错误',
          icon: 'i-icon-park-outline:close-wifi',
          withoutTab: true,
        },
      },
      {
        path: '/:pathMatch(.*)*',
        component: () => import('@/views/error/404/index.vue'),
        name: '404',
        meta: {
          title: '找不到页面',
          icon: 'i-icon-park-outline:ghost',
          withoutTab: true,
        },
      }
    ],
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.page.vue'),
    meta: {
      title: '登录',
      withoutTab: true,
    },
  },
]
