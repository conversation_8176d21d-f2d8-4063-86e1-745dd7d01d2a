.root-item {
  --jtv-key-color: #0977e6;
  --jtv-valueKey-color: #073642;
  --jtv-string-color: #268bd2;
  --jtv-number-color: #2aa198;
  --jtv-boolean-color: #cb4b16;
  --jtv-null-color: #6c71c4;
  --jtv-arrow-size: 6px;
  --jtv-arrow-color: #444;
  --jtv-hover-color: rgba(0, 0, 0, .1);
}

/* 深色模式样式 */
html.dark .root-item {
  --jtv-key-color: #80d8ff;
  --jtv-valueKey-color: #e3e3e3;
  --jtv-string-color: #9cdcfe;
  --jtv-number-color: #b5cea8;
  --jtv-boolean-color: #ff8b50;
  --jtv-null-color: #bd93f9;
  --jtv-arrow-color: #e3e3e3;
  --jtv-hover-color: rgba(255, 255, 255, .1);
}

.value-key.can-select:hover {
  background-color: var(--jtv-hover-color);
}

.data-key:hover {
  background-color: var(--jtv-hover-color);
}
