import { useDict } from './hooks/useDict'
import { useMenu } from './hooks/useMenu'
import { usePerm } from './hooks/usePerm'
import { mergeRemoteI18n } from './modules/i18n'
import { mergePageBucket } from './index'
import { controllerBucket } from '@/decorator/controller'
import { makeCore } from '@/core'

let initSystemFlag = false

async function initSystem() {
  try {
    const { init } = usePerm()
    await init()
    const { fetchAllDict } = useDict()
    const { fetchMenuAll } = useMenu()

    await Promise.all([fetchAllDict(), fetchMenuAll(), mergeRemoteI18n()])
    initSystemFlag = true

    /**
      * Import all controller files to activate decorators.
      * This is required for the decorator pattern to work.
      */
    const controllerFiles = import.meta.glob('./**/*.controller.tsx', {
      eager: true,
    })

    const controllerPages = new Map<string, Component>()
    const core = makeCore()
    for (const [path, Controller] of controllerBucket.entries()) {
      controllerPages.set(path, new Controller(core).view)
    }

    mergePageBucket(Object.fromEntries(controllerPages))
  }
  catch (error) {
    console.error(error)
    initSystemFlag = false
  }
}

export { initSystem, initSystemFlag }
