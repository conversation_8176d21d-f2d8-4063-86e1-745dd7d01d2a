<script setup lang="ts">
import { getRefresh, goto, useTable, useRequest, useDelDialog } from "@/index";

const { state, on, refresh, checked } = useTable(
  {
    page: "/gen/template/page",
  },
  [
    {
      title: "模板名称",
      key: "name",
      query: "input",
    },
    {
      title: "文件名",
      key: "fileName",
    },
    {
      title: "生成路径",
      key: "path",
    },
    {
      title: "创建时间",
      key: "createDate",
    },
    {
      title: "状态",
      key: "status",
      render: (row) => {
        return row.status === 0 ? "启用" : "禁用";
      },
    },
  ],
  {
    checkable: true,
    actionHideList: [
      "config",
      "importTable",
      "exportTable",
      "groupSearch"
    ],
    rowActions: (row) => {
      const acs: any[] = [];
      if (row.status === 0) {
        acs.push({
          label: "禁用",
          click: async () => {
            const { error } = await useRequest(
              `/gen/template/disabled`,
            ).post(JSON.stringify([row.id]))
            if (error.value) return;
            window.$message.success("禁用成功");
            refresh();
          },
        });
      } else {
        acs.push({
          label: "启用",
          click: async () => {
            const { error } = await useRequest(
              `/gen/template/enabled`,
            ).post(JSON.stringify([row.id]))
            if (error.value) return;
            window.$message.success("启用成功");
            refresh();
          },
        });
      }
      return acs;
    },
  },
);
on(async (e, row) => {
  if (e === "edit") {
    goto('/code_gen/template/form', {
      title: "编辑模板",
      query: row
    })
  }
  if (e === "new") {
    goto('/code_gen/template/form', {
      title: "新建模板",
    })
  }
  if (e === "delete") {
    useDelDialog({
      api: "/gen/template/deleteMul",
      params: [row.id],
      afterSuccess: () => {
        window.$message.success("删除成功");
        refresh();
      },
    });
  }
  if (e === "deleteMul") {
    useDelDialog({
      api: "/gen/template/deleteMul",
      params: checked.value.map(item => item.id),
      afterSuccess: () => {
        window.$message.success("删除成功");
        refresh();
      },
    });
  }
});

const path = useRoute().path
onActivated(() => {
  if (getRefresh(path.split('?')[0])) {
    refresh()
  }
})
</script>

<template>
  <mar-wrapper v-model="state">
  </mar-wrapper>
</template>