<script setup lang="ts">
import { CodeEditor, goback, useRequest } from '@/index'
import { NForm } from 'naive-ui'
import { ref } from 'vue'

const route = useRoute()
const query = route.query
const isEdit = !!query.id

// 表单数据
const formData = reactive({
  name: '',
  fileName: '',
  path: '',
  status: 0,
  content: ''
})
if (isEdit) {
  const { data } = await useRequest(`/gen/template/info/${query.id}`)
  Object.assign(formData, data.value)
}

// 表单规则
const rules = {
  name: [{ required: true, message: '请输入模板名', trigger: 'blur' }],
  fileName: [{ required: true, message: '请输入文件名', trigger: 'blur' }],
  path: [{ required: true, message: '请输入生成路径', trigger: 'blur' }],
}

const formRef = ref<InstanceType<typeof NForm>>()
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    const { error } = await useRequest(isEdit ? '/gen/template/update' : '/gen/template').post(formData)
    if (!error.value) {
      window.$message.success('操作成功')
      goback(true)
    }
  } catch (error) {
    console.log(error)
  }
}

const handleCancel = () => {
  goback(true)
}
</script>

<template>
  <NCard title="代码生成模板">
    <NForm :model="formData" :rules="rules" ref="formRef" label-placement="left" label-width="80">
      <NGrid :cols="24" :x-gap="24">
        <NFormItemGi :span="12" label="模板名" path="name">
          <NInput v-model:value="formData.name" placeholder="模板名" />
        </NFormItemGi>
        
        <NFormItemGi :span="12" label="文件名" path="fileName">
          <NInput v-model:value="formData.fileName" placeholder="文件名" />
        </NFormItemGi>
      </NGrid>
      
      <NGrid :cols="24" :x-gap="24">
        <NFormItemGi :span="12" label="生成路径" path="path">
          <NInput v-model:value="formData.path" placeholder="生成路径" />
        </NFormItemGi>
        <NFormItemGi :span="12" label="状态" path="status">
          <NRadioGroup v-model:value="formData.status">
            <NRadio :value="0">禁用</NRadio>
            <NRadio :value="1">启用</NRadio>
          </NRadioGroup>
        </NFormItemGi>
      </NGrid>
      
      <NFormItem label="内容" path="content">
        <div class="min-h-400px w-full border border-solid border-gray-200 dark:border-gray-800 rounded-sm overflow-hidden">
          <CodeEditor v-model:model-value="formData.content" language="java" />
        </div>
      </NFormItem>
      
      <NFormItem>
        <div style="width: 100%; display: flex; justify-content: flex-end; gap: 12px;">
          <NButton @click="handleCancel">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </div>
      </NFormItem>
    </NForm>
  </NCard>
</template>
