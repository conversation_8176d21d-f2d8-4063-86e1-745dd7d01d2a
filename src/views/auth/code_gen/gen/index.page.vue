<script setup lang="ts">
import { goto, useTable, getRefresh, useRequest, useDelDialog } from "@/index";

const { state, on, refresh, checked } = useTable(
  {
    page: "/gen/table/page",
  },
  [
    {
      title: "表名",
      key: "tableName",
      query: "input",
    },
    {
      title: "表说明",
      key: "tableComment",
    },
    {
      title: "类名",
      key: "className",
    },
    {
      title: "创建时间",
      key: "createDate",
    },
  ],
  {
    checkable: true,
    actionHideList: [
      "config",
      "importTable",
      "exportTable",
      "save",
      "groupSearch"
    ],
    rowActions: [
      {
        label: '生成代码',
        click: (row) => {
          const m = window.$modal.create({
            title: '生成代码',
            content: () => h('div', '确认生成代码吗？'),
            positiveText: '确定',
            preset: 'confirm',
            negativeText: '取消',
            onPositiveClick: async () => {
              console.log(row)
              const { data } = await useRequest('/gen/table/' + row.id)
              console.log(data.value)
              if (data.value) {
                const { error } = await useRequest('/gen/generator').post(data.value)
                if (!error.value) {
                  window.$message.success('生成代码成功')
                }
              }
              m.destroy()
            },
            onNegativeClick: async () => {
              m.destroy()
            }
          })
        }
      }
    ]
  },
);
on(async (e, row) => {
  if (e === "edit") {
    goto('/code_gen/gen/form', {
      title: "代码生成-编辑",
      query: row
    })
  }
  if (e === "delete") {
    useDelDialog({
      api: "/gen/table/deleteMul",
      params: [row.id],
      afterSuccess: () => {
        window.$message.success("删除成功");
        refresh();
      },
    });
  }
  if (e === "deleteMul") {
    useDelDialog({
      api: "/gen/template/deleteMul",
      params: checked.value.map(item => item.id),
      afterSuccess: () => {
        window.$message.success("删除成功");
        refresh();
      },
    });
  }
});

const path = useRoute().path
onActivated(() => {
  if (getRefresh(path.split('?')[0])) {
    refresh()
  }
})

const importDatabaseTable = async () => {
  const {} = await useRequest('')
}
</script>

<template>
  <mar-wrapper v-model="state">
    <template #toolbarExt>
      <NButton type="info" size="small" @click="importDatabaseTable">
        <template #icon>
          <div class="i-heroicons-outline-database"></div>
        </template>
        导入数据库表
      </NButton>
    </template>
  </mar-wrapper>
</template>