<script setup lang="ts">
import { goback, useDict, useTableDrag, useRequest } from '@/index'
import { i18nOptions } from "@/index"
import {
  NForm,
  NFormItem,
  NInput,
  NButton,
  NSpace,
  NCard,
  NTabPane,
  NTabs,
  NSelect,
  NDataTable,
  NAutoComplete,
  NSwitch
} from 'naive-ui'
import { DataTableColumns } from "naive-ui"
import { RowData } from "naive-ui/es/data-table/src/interface"

// 基础信息表单
const formData: {
  tableName: string,
  tableComment: string,
  className: string,
  packageName: string,
  moduleName: string,
  author: string,
  email: string,
  version: string,
  backendPath: string,
  frontendPath: string,
  baseclassId: string,
  subModuleName: string,
  datasourceId: string,
  fields: Array<{
    id: string,
    tableName: string,
    tableId: string,
    columnName: string,
    columnType: string,
    columnComment: string,
    attrName: string,
    attrType: string,
    packageName: string,
    pk: boolean,
    list: boolean,
    sort: number,
  }>
} = reactive({
  tableName: '',
  tableComment: '',
  className: '',
  packageName: '',
  moduleName: '',
  author: '',
  email: '',
  version: '',
  backendPath: '',
  frontendPath: '',
  baseclassId: '',
  subModuleName: '',
  datasourceId: '',
  fields: []
})

// 字段配置数据
const formRef = ref<InstanceType<typeof NForm>>()
const tableRef = ref<InstanceType<typeof NDataTable>>()
const activeTab = ref('baseInfo')

const route = useRoute()
const query = route.query

const { data } = await useRequest(`/gen/table/${query.id}`)
// 更新基础信息表单
if (data.value) {
  Object.assign(formData, data.value)
}

// 字典配置
const { sysDict } = useDict()
const searchValue = ref("")
const dictOptions = computed(() => {
  return Object.keys(sysDict.value)
    .filter((item) =>
      item.toLowerCase().includes((searchValue.value || "").toLowerCase())
    )
    .map((item) => ({ label: item, value: item }))
})

// 创建表格列配置
const createColumns = (): DataTableColumns<RowData> => {
  return [
    {
      title: "序号",
      key: "sort",
      width: 60,
      fixed: "left",
      render(row) {
        return row.sort + 1
      }
    },
    {
      title: "拖动",
      key: "drag",
      width: 60,
      fixed: "left",
      render() {
        return h("div", {
          class: [
            "drag-handle",
            "text-black",
            "dark:text-white",
            "cursor-move",
            "w-1.2rem",
            "h-1.2rem",
            "i-icon-park-outline-application-menu",
          ]
        })
      }
    },
    {
      title: () =>
        h(
          "div",
          {
            class: "text-lg font-bold text-red-7",
          },
          "基础信息",
        ),
      key: "basic",
      children: [
        {
          title: "国际化映射",
          key: "columnLabel",
          width: 220,
          fixed: "left",
          render(row) {
            return h(NSelect, {
              value: row.columnLabel,
              size: "small",
              filterable: true,
              options: i18nOptions.value,
              onUpdateValue(v) {
                row.columnLabel = v
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "字段名",
          key: "columnName",
          render(row) {
            return h(NInput, {
              value: row.columnName,
              size: "small",
              onUpdateValue(v) {
                row.columnName = v
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "属性名",
          key: "attrName",
          render(row) {
            return h(NInput, {
              value: row.attrName,
              size: "small",
              onUpdateValue(v) {
                row.attrName = v
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "字段说明",
          key: "columnComment",
          width: 220,
          fixed: "left",
          render(row) {
            return h(NInput, {
              value: row.columnComment,
              size: "small",
              onUpdateValue(v) {
                row.columnComment = v
                row.comment = v // 同步comment字段
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "是否主键",
          key: "isPk",
          width: 60,
          render(row) {
            return h(NSwitch, {
              value: row.isPk || row.pk,
              size: "small",
              onUpdateValue(v) {
                row.isPk = v
                row.pk = v // 同步pk字段
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "列表",
          key: "isList",
          width: 60,
          render(row) {
            return h(NSwitch, {
              value: row.isList || row.list,
              size: "small",
              onUpdateValue(v) {
                row.isList = v
                row.list = v // 同步list字段
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "列宽",
          key: "columnWidth",
          width: 80,
          render(row) {
            return h(NInput, {
              value: row.columnWidth,
              size: "small",
              onUpdateValue(v) {
                row.columnWidth = v
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "浮动",
          key: "fixed",
          width: 100,
          render(row) {
            return h(NSelect, {
              value: row.fixed,
              size: "small",
              options: [
                { label: "左", value: "left" },
                { label: "右", value: "right" },
                { label: "无", value: "" }
              ],
              onUpdateValue(v) {
                row.fixed = v
                handleUpdateRow(row)
              }
            })
          }
        }
      ]
    },
    {
      title: () =>
        h(
          "div",
          {
            class: "text-lg font-bold text-red-7",
          },
          "查询配置",
        ),
      key: "query",
      children: [
        {
          title: "快速查询",
          key: "isQuery",
          width: 60,
          render(row) {
            return h(NSwitch, {
              value: row.isQuery || row.query,
              size: "small",
              onUpdateValue(v) {
                row.isQuery = v
                row.query = v // 同步query字段
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "快速查询方式",
          key: "queryLink",
          width: 150,
          render(row) {
            return h(NSelect, {
              value: row.queryLink || row.queryType,
              size: "small",
              options: [
                { label: "eq", value: "=" },
                { label: "like", value: "like" }
              ],
              onUpdateValue(v) {
                row.queryLink = v
                row.queryType = v // 同步queryType字段
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "高级查询",
          key: "superQuery",
          width: 60,
          render(row) {
            return h(NSwitch, {
              value: row.superQuery,
              size: "small",
              onUpdateValue(v) {
                row.superQuery = v
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "允许排序",
          key: "isSortable",
          width: 60,
          render(row) {
            return h(NSwitch, {
              value: row.isSortable,
              size: "small",
              onUpdateValue(v) {
                row.isSortable = v
                handleUpdateRow(row)
              }
            })
          }
        }
      ]
    },
    {
      title: () =>
        h(
          "div",
          {
            class: "text-lg font-bold text-red-7",
          },
          "表单配置",
        ),
      key: "form",
      children: [
        {
          title: "表单",
          key: "isForm",
          width: 60,
          render(row) {
            return h(NSwitch, {
              value: row.isForm || row.form,
              size: "small",
              onUpdateValue(v) {
                row.isForm = v
                row.form = v // 同步form字段
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "必填",
          key: "isRequired",
          width: 60,
          render(row) {
            return h(NSwitch, {
              value: row.isRequired || row.required,
              size: "small",
              onUpdateValue(v) {
                row.isRequired = v
                row.required = v // 同步required字段
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "允许编辑",
          key: "isEdit",
          width: 60,
          render(row) {
            return h(NSwitch, {
              value: row.isEdit,
              size: "small",
              onUpdateValue(v) {
                row.isEdit = v
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "表单类型",
          key: "formType",
          width: 160,
          render(row) {
            return h(NSelect, {
              value: row.formType,
              size: "small",
              options: [
                { label: "输入框", value: "text" },
                { label: "数字输入框", value: "number" },
                { label: "下拉框", value: "select" },
                { label: "单选按钮", value: "radio" },
                { label: "多选按钮", value: "checkbox" },
                { label: "日期", value: "date" },
                { label: "日期时间", value: "date-time" },
                { label: "日期范围", value: "date-range" }
              ],
              onUpdateValue(v) {
                row.formType = v
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "字典名称",
          key: "dictName",
          width: 180,
          render(row) {
            return h(NAutoComplete, {
              value: row.dictName,
              size: "small",
              clearable: true,
              options: dictOptions.value,
              onUpdateValue(v) {
                row.dictName = v
                searchValue.value = v
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "表单校验",
          key: "validator",
          width: 100,
          render(row) {
            return h(NInput, {
              value: row.validator || row.validatorType,
              size: "small",
              onUpdateValue(v) {
                row.validator = v
                row.validatorType = v // 同步validatorType字段
                handleUpdateRow(row)
              }
            })
          }
        },
        {
          title: "初始值",
          key: "initialValue",
          width: 120,
          render(row) {
            return h(NInput, {
              value: row.initialValue,
              size: "small",
              onUpdateValue(v) {
                row.initialValue = v
                handleUpdateRow(row)
              }
            })
          }
        }
      ]
    },
    {
      title: "备注",
      key: "remark",
      width: 100,
      render(row) {
        return h(NInput, {
          value: row.remark,
          size: "small",
          onUpdateValue(v) {
            row.remark = v
            handleUpdateRow(row)
          }
        })
      }
    }
  ]
}

// 表格行操作
function handleUpdateRow(row: RowData) {
  const index = formData.fields.findIndex((item) => item.id === row.id)
  if (index > -1) {
    const newData = [...formData.fields]
    formData.fields = newData
  }
}

// 表格配置和拖拽
const columns = createColumns()
useTableDrag({
  tableRef,
  data: formData.fields as unknown as Ref<RowData[]>
})

// 监听排序
watch(formData, () => {
  formData.fields.forEach((item, index) => {
    item.sort = index
  })
})

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    const { error } = await useRequest('/gen/table').post(formData)

    if (!error.value) {
      window.$message.success('操作成功')
      goback(true)
    }
  } catch (error) {
    console.error('提交失败:', error)
  }
}

// 取消操作
const handleCancel = () => {
  goback(true)
}

const baseclassOptions = [
  {
    label: 'BaseEntity',
    value: '1302875019642159105'
  }
]
</script>

<template>
  <NCard>
    <NTabs v-model:value="activeTab" type="line" animated>
      <NTabPane name="baseInfo" tab="基础信息">
        <NForm ref="formRef" :model="formData" label-placement="left" label-width="100px">
          <NFormItem label="表名" path="tableName" :rule="{ required: true, message: '请输入表名' }">
            <NInput v-model:value="formData.tableName" placeholder="请输入表名" />
          </NFormItem>
          <NFormItem label="功能名称" path="tableComment" :rule="{ required: true, message: '请输入功能名称' }">
            <NInput v-model:value="formData.tableComment" placeholder="请输入功能名称" />
          </NFormItem>
          <NFormItem label="类名" path="className" :rule="{ required: true, message: '请输入类名' }">
            <NInput v-model:value="formData.className" placeholder="请输入类名" />
          </NFormItem>
          <NFormItem label="基类" path="baseclassId">
            <NSelect v-model:value="formData.baseclassId" placeholder="请选择基类" :options="baseclassOptions" clearable />
          </NFormItem>
          <NFormItem label="模块名" path="moduleName" :rule="{ required: true, message: '请输入模块名' }">
            <NInput v-model:value="formData.moduleName" placeholder="请输入模块名" />
          </NFormItem>
          <NFormItem label="子模块名" path="subModuleName">
            <NInput v-model:value="formData.subModuleName" placeholder="请输入子模块名" />
          </NFormItem>
          <NFormItem label="项目包名" path="packageName">
            <NInput v-model:value="formData.packageName" placeholder="请输入包名" />
          </NFormItem>
          <NFormItem label="版本" path="version">
            <NInput v-model:value="formData.version" placeholder="请输入版本" />
          </NFormItem>
          <NFormItem label="作者" path="author">
            <NInput v-model:value="formData.author" placeholder="请输入作者" />
          </NFormItem>
          <NFormItem label="邮箱" path="email">
            <NInput v-model:value="formData.email" placeholder="请输入邮箱" />
          </NFormItem>
          <NFormItem label="后端路径" path="backendPath">
            <NInput v-model:value="formData.backendPath" placeholder="请输入后端路径" />
          </NFormItem>
          <NFormItem label="前端路径" path="frontendPath">
            <NInput v-model:value="formData.frontendPath" placeholder="请输入前端路径" />
          </NFormItem>
        </NForm>
      </NTabPane>
      <NTabPane name="fieldConfig" tab="字段配置">
        <NDataTable ref="tableRef" :columns="columns" :data="formData.fields" :pagination="false" :scroll-x="2360"
          :single-line="false" striped size="small" single-column />
      </NTabPane>
    </NTabs>

    <div class="mt-8 flex justify-end">
      <NSpace>
        <NButton @click="handleCancel">取消</NButton>
        <NButton type="primary" @click="handleSubmit">保存</NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.drag-handle {
  cursor: move;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>