<script setup lang="ts">
import { RootRefreshSymbol, type TreeOptionWithMeta, useRequest } from "@/index";
import { usePerm } from '@/hooks/usePerm'
import { onClickOutside } from "@vueuse/core";
import ButtonDialog from "./ButtonDialog.vue";
import MenuDialog from "./MenuDialog.vue";
import { t } from "@/modules/i18n";

const props = defineProps<{
	data: TreeOptionWithMeta;
	name: "wcs" | "wms" | "auth"| "sn";
}>();
const rootRefresh = inject(RootRefreshSymbol);

const { hasPerm } = usePerm();
const rawOpt = [
	{ label: t("common.create"), key: "create", perm: "sys:menu:save" },
	{ label: t("common.edit"), key: "edit", perm: "sys:menu:update" },
	{ label: t("common.delete"), key: "delete", perm: "sys:menu:deleteMul" },
	{
		label: t("common.banOrOpen"),
		key: "banOrOpen",
		perm: "sys:menu:modifyMenuStatus",
	},
];
const opt = computed(() => {
	return rawOpt.filter((item) => hasPerm(item.perm));
});
const showDropdown = ref(false);
const x = ref(0);
const y = ref(0);
const dropdownRef = ref<HTMLElement>();

function handleContextMenu(e: MouseEvent) {
	e.preventDefault();
	showDropdown.value = true;
	x.value = e.clientX;
	y.value = e.clientY;
}

const menuDialogRef = ref<InstanceType<typeof MenuDialog> | null>(null);
const menuDialogApi = ref("/sys/menu/update");
const buttonDialogRef = ref<InstanceType<typeof ButtonDialog> | null>(null);
async function handleSelect(key: string | number) {
	showDropdown.value = false;
	if (key === "edit") {
		if (props.data.meta.menuType === 3) {
			buttonDialogRef.value?.open(props.data);
		} else {
			menuDialogRef.value?.open(props.data);
			menuDialogApi.value = "/sys/menu/update";
		}
	}
	if (key === "create") {
		menuDialogRef.value?.open(props.data, {
			overrideFetchData: async () => ({
				pid: props.data.key,
				firstRouter: props.name,
			}),
		});
		menuDialogApi.value = "/sys/menu/save";
	}
	if (key === "delete") {
		window.$dialog.warning({
			title: t("common.delete"),
			content: t("common.deleteConfirm"),
			positiveText: t("common.confirm"),
			negativeText: t("common.cancel"),
			onPositiveClick: async () => {
				const { error } = await useRequest("/sys/menu/deleteMul").post([
					props.data.key,
				]);
				if (error.value) return;
				window.$message.success(t("common.operationSuccess"));
				rootRefresh?.refresh();
			},
		});
	}
	if (key === "banOrOpen") {
		const { error } = await useRequest("/sys/menu/modifyMenuStatus").post({
			id: props.data.key,
			isEnable: props.data.meta.isEnable === 1 ? 0 : 1,
		});
		if (error.value) return;
		window.$message.success(t("common.operationSuccess"));
		rootRefresh?.refresh();
	}
}

// 点击其他地方关闭下拉菜单
onClickOutside(dropdownRef, (event) => {
	if (
		event.target &&
		"closest" in event.target &&
		!(event.target as Element).closest(".n-dropdown-option")
	) {
		showDropdown.value = false;
	}
});

const menuTypeOpt = [
	{
		label: t("pages.config.menu.dir"),
		value: 1,
		icon: "i-icon-park-outline-folder-close",
	},
	{
		label: t("pages.config.menu.pageType"),
		value: 2,
		icon: "i-icon-park-outline-web-page",
	},
	{
		label: t("pages.config.menu.button"),
		value: 3,
		icon: "i-icon-park-outline-click",
	},
];
</script>

<template>
  <section class="w-full h-30px flex items-center gap-2 hover:bg-[var(--n-node-color-hover)]"
    @contextmenu="handleContextMenu">

    <MenuDialog ref="menuDialogRef" :api="menuDialogApi" :name="data.meta.firstRouter ?? 'wcs'" />
    <ButtonDialog ref="buttonDialogRef" api="/sys/menu/update" :name="data.meta.firstRouter ?? 'wcs'" />
    <div class="w-200px truncate">{{ $t(data.meta.menuName ?? '') }}</div>
    <div class="absolute left-300px top-0 h-30px flex items-center gap-2">

      <div class="flex items-center gap-2">
        <n-tag size="small" round :bordered="false" :type="data.meta.isEnable === 1 ? 'success' : 'warning'">
          <template #icon>
            <div :class="menuTypeOpt.find(item => item.value === data.meta.menuType)?.icon" />
          </template>
          {{menuTypeOpt.find(item => item.value === data.meta.menuType)?.label}}
          {{ data.meta.isEnable === 1 ? t('common.open') : t('common.ban') }}
        </n-tag>
        <div class="text-gray mr-2">排序: {{ data.meta.orderNum }}</div>
        <div class="text-gray truncate">{{ data.meta.url }}</div>
      </div>
    </div>

    <n-dropdown ref="dropdownRef" :show="showDropdown" :options="opt" :x="x" :y="y" placement="bottom-start"
      trigger="manual" @select="handleSelect" />
  </section>
</template>
