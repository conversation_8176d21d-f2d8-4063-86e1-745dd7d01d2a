<script setup lang="ts">
import { t } from "@/modules/i18n";
import {
	FormDialog,
	type FormSchema,
	ServerPageMenuItem,
	i18nOptions,
	menuToTreeSelectOption,
	useRequest,
	useMenu,
} from "@/index";
import { RootRefreshSymbol } from "@/index";

defineProps<{
	api: string;
	name: "wcs" | "wms" | "auth"| "sn";
}>();

const { fetchMenuAll } = useMenu();
const menuTypeOpt = [
	{ label: t("pages.config.menu.dir"), value: 1 },
	{ label: t("pages.config.menu.pageType"), value: 2 },
	{ label: t("pages.config.menu.button"), value: 3 },
];
const schema = computed(
	() =>
		[
			{
				prop: "menuType",
				label: t("pages.config.menu.menuType"),
				type: "select",
				options: menuTypeOpt,
				disabled: true,
			},
			{
				prop: "menuName",
				label: t("pages.config.menu.menuName"),
				type: "select",
				options: i18nOptions.value,
				rules: {
					required: true,
				},
			},
			{
				prop: "permissions",
				label: t("pages.config.menu.permissions"),
				type: "input",
			},
			{
				prop: "orderNum",
				label: t("pages.config.menu.orderNum"),
				type: "input-number",
			},
			{
				prop: "isEnable",
				label: t("pages.config.menu.isEnable"),
				type: "radio",
				options: [
					{ label: t("common.open"), value: 1 },
					{ label: t("common.ban"), value: 0 },
				],
			},
		] satisfies FormSchema,
);

const formDialogRef = ref<any | null>(null);
async function fetchData(args: Record<string, any>) {
	const { data } = await useRequest(`/sys/menu/info/${args.key}`);
	if (data.value) {
		return data.value;
	}
	return {};
}

const rootRefresh = inject(RootRefreshSymbol);
async function onSuccess() {
	await fetchMenuAll();
	await nextTick();
	await rootRefresh?.refresh();
}

defineExpose({
	open: (
		args?: Record<string, any>,
		opt?: {
			noFetch?: boolean;
			overrideFetchData?: (args?: Record<string, any>) => Promise<any>;
		},
	) => {
		formDialogRef.value?.open(args, opt);
	},
});
</script>

<template>
  <FormDialog ref="formDialogRef" :title="$t('common.edit')" :form-schema="schema" :fetch-data="fetchData" :api="api"
    :on-success="onSuccess" />
</template>
