<script setup lang="ts">
import { t } from "@/modules/i18n";
import {
	FormDialog,
	type FormSchema,
	type ServerPageMenuItem,
	i18nOptions,
	menuToTreeSelectOption,
	useRequest,
	useMenu,
} from "@/index";
import { AvailableSysSymbol, RootRefreshSymbol } from "@/index";
import { systemConfig } from '@/config';

defineProps<{
	api: string;
	name: "wcs" | "wms" | "auth"| "sn";
}>();

const systemList = computed(() => systemConfig.list)
const { getMenuList, fetchMenuAll } = useMenu();
const menuTypeOpt = [
	{ label: t("pages.config.menu.dir"), value: 1 },
	{ label: t("pages.config.menu.pageType"), value: 2 },
	{ label: t("pages.config.menu.button"), value: 3 },
];
const selectedSys = ref<"wcs" | "wms" | "auth" | string>("auth");
const menuList = ref<ServerPageMenuItem[]>(getMenuList(selectedSys.value));
watch(selectedSys, () => {
	menuList.value = getMenuList(selectedSys.value);
});
const schema = computed(
	() =>
		[
			{
				prop: "menuType",
				label: t("pages.config.menu.menuType"),
				type: "select",
				options: menuTypeOpt,
			},
			{
				prop: "buttonType",
				label: t("pages.config.menu.buttonType"),
				type: "select",
				hide: (formValue: Record<string, any>) => formValue.menuType !== 3,
				options: [
					{ label: t("pages.config.menu.customButton"), value: 1 },
					{ label: t("pages.config.menu.commonButton"), value: 2 },
				],
			},
			{
				prop: "commonButtonPrefix",
				label: t("pages.config.menu.commonButtonPrefix"),
				type: "input",
				hide: (formValue: Record<string, any>) => formValue.buttonType !== 2,
			},
			{
				prop: "commonButtonList",
				label: t("pages.config.menu.commonButtonList"),
				type: "checkbox-group",
				hide: (formValue: Record<string, any>) => formValue.buttonType !== 2,
				options: [
					{ label: t("common.page"), value: "page" },
					{ label: t("common.save"), value: "save" },
					{ label: t("common.update"), value: "update" },
					{ label: t("common.deleteMul"), value: "deleteMul" },
					{ label: t("common.importTable"), value: "importTable" },
					{ label: t("common.exportTable"), value: "exportTable" },
				],
        remark: `数据权限注解
默认值说明 需要校验的权限码，默认值为controller类路径+方法名，分隔符由/ 替换为 : 
例如接口地址为 /user/list ,权限字符串为: user:list
加此注解后，意为此接口受权限管控，需要有user:list权限才能访问`
			},
			{
				prop: "menuName",
				label: t("pages.config.menu.menuName"),
				type: "select",
				options: i18nOptions.value,
				rules: {
					required: true,
				},
				hide: (formValue: Record<string, any>) =>
					formValue.menuType === 3 && formValue.buttonType === 2,
			},
			{
				prop: "firstRouter",
				label: t("pages.config.menu.firstRouter"),
				type: "radio",
				options: systemList.value?.map((item) => ({
					label: item.key,
					value: item.key,
				})),
				rules: {
					required: true,
				},
			},
			{
				prop: "pid",
				label: t("pages.config.menu.pid"),
				type: "tree-select",
				options: menuToTreeSelectOption(menuList.value),
				rules: {
					required: true,
				},
			},
			{
				prop: "url",
				label: t("pages.config.menu.url"),
				type: "input",
				hide: (formValue: Record<string, any>) => formValue.menuType === 3,
				rules: {
					required: true,
				},
			},
			{
				prop: "orderNum",
				label: t("pages.config.menu.orderNum"),
				type: "input-number",
			},
			{
				prop: "openStyle",
				label: t("pages.config.menu.openStyle"),
				type: "radio",
				options: [
					{ label: t("pages.config.menu.newWindow"), value: 0 },
					{ label: t("pages.config.menu.sameWindow"), value: 1 },
				],
				hide: (formValue: Record<string, any>) => formValue.menuType === 3,
			},
			{
				prop: "icon",
				label: t("pages.config.menu.icon"),
				type: "icon-select",
				hide: (formValue: Record<string, any>) => formValue.menuType === 3,
			},
			{
				prop: "permissions",
				label: t("pages.config.menu.permissions"),
				type: "input",
				hide: (formValue: Record<string, any>) =>
					formValue.menuType !== 3 || formValue.buttonType !== 1,
			},
			{
				prop: "dynCode",
				label: t("pages.config.menu.dynCode"),
				type: "input",
				hide: (formValue: Record<string, any>) => formValue.menuType !== 2,
			},
			{
				prop: "isEnable",
				label: t("pages.config.menu.isEnable"),
				type: "radio",
				options: [
					{ label: t("common.open"), value: 1 },
					{ label: t("common.ban"), value: 0 },
				],
			},
		] satisfies FormSchema,
);

const formDialogRef = ref<any | null>(null);
async function fetchData(args: Record<string, any>) {
	const { data } = await useRequest(`/sys/menu/info/${args.key}`);
	if (data.value) {
		return data.value;
	}
	return {};
}

function watchForm(
	changedProps: Record<string, any>,
	form: Ref<Record<string, any>>,
	initializing: boolean,
) {
	if (changedProps.firstRouter) {
		if (!initializing) {
			form.value.pid = "";
		}
		selectedSys.value = changedProps.firstRouter;
	}
}

const rootRefresh = inject(RootRefreshSymbol);
async function onSuccess() {
	await fetchMenuAll();
	await nextTick();
	menuList.value = getMenuList(selectedSys.value);
	await rootRefresh?.refresh();
}

function beforeSubmit(form: Record<string, any>) {
	if (form.buttonType === 2) {
		return [
			form.commonButtonList.map((item: string) => {
				return {
					pid: form.pid,
					firstRouter: form.firstRouter,
					permissions: `${form.commonButtonPrefix}:${item}`,
					menuName: `common.${item}`,
					menuType: 3,
					orderNum: 0,
					isEnable: 1,
				};
			}),
			"/sys/menu/saveBatchButton",
		];
	}
	return [form];
}

defineExpose({
	open: (
		args?: Record<string, any>,
		opt?: {
			noFetch?: boolean;
			overrideFetchData?: (args?: Record<string, any>) => Promise<any>;
		},
	) => {
		formDialogRef.value?.open(args, opt);
	},
});
</script>

<template>
  <FormDialog ref="formDialogRef" :title="$t('common.edit')" :form-schema="schema" :fetch-data="fetchData" :api="api"
    :watch-form="watchForm" :on-success="onSuccess" :before-submit="beforeSubmit" />
</template>
