<script setup lang="ts">
import { TreeOptionWithMeta } from "@/index";
import { Key } from "naive-ui/es/tree/src/interface";
import { h } from "vue";
import MenuTreeLabelRender from "./MenuTreeLabelRender.vue";
import MenuTreePrefixRender from "./MenuTreePrefixRender.vue";

const props = defineProps<{
	treeList: TreeOptionWithMeta[];
	name: "wcs" | "wms" | "auth"| "sn";
}>();

const search = ref("");
const defaultExpandedKeys = computed(() => {
	return props.treeList.map((item) => item.meta.id) as Key[];
});
</script>

<template>
  <div>
    <n-input v-model:value="search" :placeholder="$t('common.search')" class="max-w-400px mb-2" />
    <n-tree block-line :data="treeList" :pattern="search" :show-irrelevant-nodes="false" expand-on-click
      class="menu-tree" :render-label="(info) => h(MenuTreeLabelRender, {
        data: info.option as TreeOptionWithMeta,
        name: props.name
      })" :render-prefix="(info) => h(MenuTreePrefixRender, { data: info.option as TreeOptionWithMeta })" />
  </div>
</template>
