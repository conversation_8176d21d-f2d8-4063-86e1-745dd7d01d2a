<script setup lang="tsx">
import { t } from "@/modules/i18n";
import { AvailableSysSymbol, RootRefreshSymbol, useRequest } from "@/index";
import { ServerMenuItem, TreeOptionWithMeta } from "@/index";
import MenuTree from "./MenuTree.vue";
import { systemConfig } from '@/config';

const systemList = computed(() => systemConfig.list)
const tree = ref<Record<string, TreeOptionWithMeta[]>>({});
function clearTree() {
	for (const key in tree.value) {
		delete tree.value[key];
	}
}
const { execute, isFetching } = useRequest<ServerMenuItem[]>(
	"/sys/menu/treePage",
	{
		method: "POST",
	},
	{
		immediate: true,
		afterFetch(ctx) {
			const { data } = ctx as { data: ServerMenuItem[] };
			clearTree();
			data.forEach((item) => {
				tree.value[item.firstRouter] = [];
				tree.value[item.firstRouter].push(convertToTreeOption(item));
			});
			return ctx;
		},
	},
);

function convertToTreeOption(menuItem: ServerMenuItem): TreeOptionWithMeta {
	return {
		key: menuItem.id,
		label: t(menuItem.menuName ?? ""),
		children: menuItem.children?.map((child) => convertToTreeOption(child)),
		meta: {
			id: menuItem.id,
			pid: menuItem.pid,
			menuName: menuItem.menuName,
			icon: menuItem.icon,
			openStyle: menuItem.openStyle,
			isCache: menuItem.isCache,
			menuType: menuItem.menuType,
			orderNum: menuItem.orderNum,
			url: menuItem.url,
			isEnable: menuItem.isEnable,
		},
	};
}

provide(RootRefreshSymbol, {
	refresh: execute,
});
</script>

<template>
  <n-card content-style="padding: 0;">
    <n-tabs type="line" size="large" :tabs-padding="20" pane-style="padding: 20px;">
      <n-tab-pane v-for="item in systemList" :name="item.key">
        <MenuTree v-show="!isFetching" :tree-list="tree[item.key]" name="auth" />
      </n-tab-pane>
    </n-tabs>
  </n-card>
</template>
