<script setup lang="tsx">
import { t } from "@/modules/i18n";
import { genActionHideList } from "@/utils";
import {
	FormDialog,
	FormDialogV2,
	type FormSchema,
	TableDialog,
	UploadDialog,
	i18nOptions,
	useTable,
	useRequest,
	useDelDialog,
} from "@/index";

const currentDictType = ref("");
const DictTypeRender = (props: { id: string; dictType: string }) => {
	return (
		<n-button
			type="primary"
			text
			size="small"
			onClick={() => {
				currentDictType.value = props.id;
				trigger("dictData");
			}}
		>
			{props.dictType}
		</n-button>
	);
};

const { state, on, trigger, refresh, checked } = useTable(
	{
		page: "/sys/dict/type/page",
		exportTable: "/sys/dict/type/exportTable",
	},
	[
		{
			title: t("pages.config.dictionary.dictName"),
			key: "dictName",
			translate: "on",
		},
		{
			title: t("pages.config.dictionary.dictType"),
			key: "dictType",
			query: "input",
			render: DictTypeRender,
		},
		{
			title: t("pages.config.dictionary.sort"),
			key: "sort",
			sort: "on",
		},
		{
			title: t("pages.config.dictionary.remark"),
			key: "remark",
		},
		{
			title: t("pages.config.dictionary.createDate"),
			key: "createDate",
			sort: "on",
		},
	],
	{
		checkable: true,
		extQuery: [
			{
				title: t("pages.config.dictionary.dictName"),
				key: "dictName",
				query: "select",
				options: i18nOptions.value,
				queryWidth: 300,
			},
		],
		actionHideList: [
			...genActionHideList({
				save: "sys:dict:type:save",
				edit: "sys:dict:type:edit",
				delete: "sys:dict:type:delete",
				importTable: "sys:dict:type:import",
				exportTable: "sys:dict:type:export",
			}),
		],
	},
);

const formSchema = [
	{
		prop: "dictName",
		label: t("pages.config.dictionary.dictName"),
		type: "select",
		options: i18nOptions.value,
	},
	{
		prop: "dictType",
		label: t("pages.config.dictionary.dictType"),
		type: "input",
	},
	{
		prop: "sort",
		label: t("pages.config.dictionary.sort"),
		type: "input-number",
	},
	{
		prop: "remark",
		label: t("pages.config.dictionary.remark"),
		type: "input",
	},
] satisfies FormSchema;

const formDialogRef = ref<InstanceType<typeof FormDialog>>();
const editFormDialogRef = ref<InstanceType<typeof FormDialog>>();
const uploadDialogRef = ref<InstanceType<typeof UploadDialog>>();
const tableDialogRef = ref<InstanceType<typeof TableDialog>>();

on((event: string, args: any) => {
	if (event === "new") {
		formDialogRef.value?.open({});
	}
	if (event === "importTable") {
		uploadDialogRef.value?.open();
	}
	if (event === "edit") {
		editFormDialogRef.value?.open(args.id);
	}
	if (event === "delete") {
		useDelDialog({
			api: "/sys/dict/type/deleteMul",
			params: [args.id],
			afterSuccess: refresh,
		});
	}
	if (event === "dictData") {
		tableDialogRef.value?.open();
	}
	if (event === "deleteMul") {
		useDelDialog({
			api: "/sys/dict/type/deleteMul",
			params: checked.value.map((item) => item.id),
			afterSuccess: refresh,
		});
	}
});

async function fetchData(...args: any[]) {
	const { data } = await useRequest(`/sys/dict/type/info/${args[0]}`).get();
	return data.value;
}

const tableConfig = useTable(
	"/sys/dict/data/page",
	[
		{
			title: t("pages.config.dictionary.dictValue"),
			key: "dictValue",
		},
		{
			title: t("pages.config.dictionary.dictTag"),
			key: "dictLabel",
		},
		{
			title: t("pages.config.dictionary.sort"),
			key: "sort",
			sort: "on",
		},
		{
			title: t("pages.config.dictionary.remark"),
			key: "remark",
		},
		{
			title: t("pages.config.dictionary.createDate"),
			key: "createDate",
			sort: "on",
		},
	],
	{
		beforeRequest: (params) => {
			params = [
				{
					link: "and",
					data: [
						{
							colkey: "dictTypeId",
							link: "and",
							op: "like",
							opval: currentDictType.value,
						},
					],
				},
			] as any;

			return params;
		},
		immediate: "off",
		actionHideList: ["importTable", "exportTable"],
		tableScrollX: 700,
	},
);
const detailDialogRef = ref<InstanceType<typeof FormDialogV2>>();
tableConfig.on((e, row) => {
	if (e === "new") {
		detailDialogRef.value?.open(e);
	}
	if (e === "edit") {
		detailDialogRef.value?.open(
			e,
			{},
			{
				overrideFetchData: async () => {
					const { data } = await useRequest(
						`/sys/dict/data/info/${row.id}`,
					).get();
					if (data.value) {
						return data.value;
					}
					return {};
				},
			},
		);
	}
	if (e === "delete") {
		useDelDialog({
			api: "/sys/dict/data/deleteMul",
			params: [row.id],
			afterSuccess: tableConfig.refresh,
		});
	}
});
watch(currentDictType, () => {
	tableConfig.refresh();
});

const detailFormSchema = [
	{
		prop: "dictValue",
		label: t("pages.config.dictionary.dictValue"),
		type: "input",
		rules: {
			required: true,
		},
	},
	{
		prop: "dictLabel",
		label: t("pages.config.dictionary.dictTag"),
		type: "select",
		options: i18nOptions.value,
		rules: {
			required: true,
		},
	},
	{
		prop: "sort",
		label: t("pages.config.dictionary.sort"),
		type: "input-number",
		rules: {
			required: true,
		},
	},
	{
		prop: "remark",
		label: t("pages.config.dictionary.remark"),
		type: "input",
	},
] satisfies FormSchema;
const formConfigs = [
	{
		key: "new",
		config: {
			formSchema: detailFormSchema,
			api: "/sys/dict/data/save",
			beforeSubmit: (form) => {
				form.dictTypeId = currentDictType.value;
				return [form];
			},
			onSuccess: () => {
				tableConfig.refresh();
			},
		},
	},
	{
		key: "edit",
		config: {
			formSchema: detailFormSchema,
			api: "/sys/dict/data/update",
			beforeSubmit: (form) => {
				form.dictTypeId = currentDictType.value;
				return [form];
			},
			onSuccess: () => {
				tableConfig.refresh();
			},
		},
	},
];
</script>

<template>
  <mar-wrapper v-model="state">
    <template #free-zone>
      <FormDialog api="/sys/dict/type/update" :form-schema="formSchema" :title="t('common.edit')"
        ref="editFormDialogRef" :fetch-data="fetchData" @success="refresh" />
      <FormDialog api="/sys/dict/type/save" :form-schema="formSchema" :title="t('common.create')" ref="formDialogRef"
        @success="refresh" />
      <UploadDialog upload-api="/wcs/sys/dict/type/upload" template-api="/wcs/sys/dict/type/download"
        :title="t('common.upload')" ref="uploadDialogRef" />
      <TableDialog title="字典数据" :table-config="tableConfig" ref="tableDialogRef" />
      <FormDialogV2 :form-configs="formConfigs" title="" ref="detailDialogRef" />
    </template>
  </mar-wrapper>
</template>
