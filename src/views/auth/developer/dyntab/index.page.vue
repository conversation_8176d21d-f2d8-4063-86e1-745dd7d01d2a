<script setup lang="tsx">
import { t } from "@/modules/i18n";
import { genActionHideList } from "@/utils";
import { goto, useTable, useDelDialog, useRequest } from "@/index";
import NewOrUpdateFormDialog from "./NewOrUpdateFormDialog.vue";

const { state, on, refresh, trigger } = useTable(
    {
      page: "/devtools/dynTable/page",
      exportTable: "/virtual/dyn_table/exportTable",
      importTable: "/virtual/dyn_table/importTable",
      importTemplate: "/virtual/dyn_table/importTemplate",
    },
	[
		{
			title: "ID",
			key: "id",
		},
		{
			title: t("pages.dyntab.dyn_code"),
			key: "dynCode",
		},
		{
			title: t("pages.dyntab.table_name"),
			key: "tableName",
			query: "input",
		},
		{
			title: t("pages.dyntab.table_comment"),
			key: "tableComment",
      query: "input",
		},
		{
			title: t("pages.dyntab.create_date"),
			key: "createDate",
		},
	],
	{
		rowActions: [
			{
				label: t("common.update"),
				click: (row: any) => {
					trigger("update", row);
				},
			},
		],
		actionHideList: [
			...genActionHideList({
				save: "devtools:dynTable:save",
				edit: "devtools:dynTable:edit",
				delete: "devtools:dynTable:deleteMul",
				importTable: "devtools:dynTable:importTable",
				exportTable: "devtools:dynTable:exportTable",
			}),
		],
		defaultSort: {
			columnKey: "createDate",
			order: "descend",
		},
	},
);

const dyntableFormDialogRef = ref<InstanceType<typeof NewOrUpdateFormDialog>>();
on((event, args) => {
	if (event === "new") {
		dyntableFormDialogRef.value?.open?.(
			{},
			{
				noFetch: true,
			},
		);
	}
	if (event === "update") {
		dyntableFormDialogRef.value?.open?.(args.id);
	}
	if (event === "edit") {
		goto("/developer/dyntab/edit", {
			query: {
				id: args.id,
			},
			title: t("common.edit"),
		});
	}
	if (event === "delete") {
		useDelDialog({
			api: "/devtools/dynTable/deleteMul",
			params: [args.id],
			afterSuccess: refresh,
		});
	}
});
</script>

<template>
  <mar-wrapper v-model="state">
    <template #free-zone>
      <NewOrUpdateFormDialog
          ref="dyntableFormDialogRef"
          :refresh="refresh"
      />
    </template>
  </mar-wrapper>
</template>

