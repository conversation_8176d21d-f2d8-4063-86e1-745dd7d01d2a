<script setup lang="ts">
import { goback, useRequest } from "@/index";
import { RowData } from "naive-ui/es/data-table/src/interface";
import EditableTable from "./EditComponent.vue";
import { uid } from 'radash'

const { id } = useRoute().query;

const dynamicConfig = ref<RowData[]>([]);
await useRequest<RowData[]>(
	`/devtools/dynTableField/table/${id}`,
	{},
	{
		immediate: true,
		afterFetch: (ctx) => {
			dynamicConfig.value = ctx.data.map(item => ({
        ...item,
        _id: uid(7),
      }));
			return ctx;
		},
	},
);

async function handleSave() {
	const { error } = await useRequest(
		`/devtools/dynTableField/updateList/${id}`,
	).post(JSON.stringify(dynamicConfig.value));
	if (!error.value) {
		window.$message.success("保存成功");
    goback(true)
	}
}

function handleNewRow() {
	dynamicConfig.value.push({
    _id: uid(7),
		id: "",
		sort: dynamicConfig.value.length + 1,
		columnLabel: "",
		columnName: "",
		columnType: "",
		attrName: "",
		columnComment: "",
		isList: false,
		isQuery: false,
		queryLink: "like",
		superQuery: false,
		isSortable: false,
		isPk: false,
		columnWidth: "",
		fixed: "",
		isForm: false,
		formType: "text",
		isRequired: false,
		isEdit: false,
		dictName: "",
		validator: "",
	});
}
</script>

<template>
  <n-card title="动态表配置">
    <div class="flex justify-start mb-4 gap-2">
      <n-button @click="handleNewRow" type="success">
        新增自定义属性
      </n-button>
      <n-button type="primary" @click="handleSave">
        {{ $t('common.saveLabel') }}
      </n-button>
    </div>
    <editable-table v-if="dynamicConfig" size="small" v-model="dynamicConfig" max-height="65vh" />
  </n-card>
</template>
