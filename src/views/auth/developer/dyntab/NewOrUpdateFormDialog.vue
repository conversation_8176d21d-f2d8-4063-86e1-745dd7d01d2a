<script setup lang="ts">
import { t } from "@/modules/i18n";
import { FormSchema } from "@/index";
import { useRequest } from "@/index";
import { FormDialog } from "@/index";

defineProps<{
	refresh: () => void;
}>();

const { data: datasourceList, execute: queryDatasourceList } = useRequest<
	[
		{
			connName: string;
			id: string;
		},
	]
>("/sys/dict/type/dictTable/dataSource", {
	immediate: false,
});
const tableListUrl = ref("/devtools/datasource/tableList/auth");
const { data: tableList } = useRequest<
	[
		{
			tableComment: string;
			tableName: string;
		},
	]
>(tableListUrl, {
	refetch: true,
});
function watchForm(
	changedProps: Record<string, any>,
	form: Ref<Record<string, any>>,
	initializing: boolean,
) {
	if (changedProps.datasource) {
		tableListUrl.value = `/devtools/datasource/tableList/${changedProps.datasource}`;
	}
}
function beforeSubmit(form: Record<string, any>) {
	if (form.tableNameInput) {
		form.tableName = form.tableNameInput;
		form.tableNameInput = undefined;
	}
	if (form.tableNameSelect) {
		form.tableName = form.tableNameSelect;
		form.tableNameSelect = undefined;
	}
	if ("id" in form) {
		return [form, "/devtools/dynTable/update"];
	}
	return [form];
}

const { data: subDynTableDict, execute: querySubDynTableDict } = useRequest<
	{
		name: string;
		code: string;
	}[]
>("/sys/dict/type/dictTable/subDynTableDict", {
	immediate: false,
});
const formSchema = computed(
	() =>
		[
			{
				label: t("pages.dyntab.table_comment"),
				prop: "tableComment",
				type: "input",
			},
			{
				label: t("pages.dyntab.dyn_code"),
				prop: "dynCode",
				type: "input",
				rules: {
					required: true,
				},
			},
			{
				label: t("pages.dyntab.datasource"),
				prop: "datasource",
				type: "select",
				options: datasourceList?.value?.map((item) => ({
					label: item.connName,
					value: item.id,
				})),
			},
			{
				label: t("pages.dyntab.table_name"),
				prop: "tableNameInput",
				type: "input",
				rules: {
					required: true,
				},
				hide: (formValue: Record<string, any>) => {
					return formValue.datasource;
				},
			},
			{
				label: t("pages.dyntab.select_table"),
				prop: "tableNameSelect",
				type: "select",
				options: tableList?.value?.map((item) => ({
					label: item.tableName,
					value: item.tableName,
				})),
				rules: {
					required: true,
				},
				hide: (formValue: Record<string, any>) => {
					return !formValue.datasource;
				},
			},
			{
				label: t("pages.dyntab.auth_filter"),
				prop: "authFilter",
				type: "radio",
				options: [
					{ label: t("common.true"), value: 1 },
					{ label: t("common.false"), value: 0 },
				],
			},
			{
				label: t("pages.dyntab.table_type"),
				prop: "tableType",
				type: "radio",
				options: [
					{ label: t("pages.dyntab.table_type_single"), value: 0 },
					{ label: t("pages.dyntab.table_type_main"), value: 1 },
					{ label: t("pages.dyntab.table_type_sub"), value: 2 },
				],
			},
			{
				label: t("pages.dyntab.sub_table"),
				prop: "subTable",
				type: "select",
				options: subDynTableDict?.value?.map((item) => ({
					label: item.name,
					value: item.code,
				})),
				hide: (formValue: Record<string, any>) => {
					return formValue.tableType !== 1;
				},
			},
			{
				label: t("pages.dyntab.mom_key"),
				prop: "momKey",
				type: "input",
				hide: (formValue: Record<string, any>) => {
					return formValue.tableType !== 2;
				},
			},
			{
				label: t("pages.dyntab.sub_mom_key"),
				prop: "subMomKey",
				type: "input",
				hide: (formValue: Record<string, any>) => {
					return formValue.tableType !== 2;
				},
			},
		] satisfies FormSchema,
);

interface FetchDataParams {
	id: string;
	datasource: string;
	tableName: string;
	tableNameInput?: string;
	tableNameSelect?: string;
}
async function fetchData(id: string) {
	const { data } = await useRequest<FetchDataParams>(
		`/devtools/dynTable/info/${id}`,
	).get();
	if (data.value) {
		if (data.value.datasource) {
			data.value.tableNameSelect = data.value.tableName;
		} else {
			data.value.tableNameInput = data.value.tableName;
		}
		return {
			...data.value,
			id,
		};
	}
	return null;
}

const dialogRef = ref<InstanceType<typeof FormDialog>>();
onBeforeMount(async () => {
	await queryDatasourceList();
	await querySubDynTableDict();
});
defineExpose({
	open: (
		args?: Record<string, any>,
		opt?: {
			noFetch?: boolean;
			overrideFetchData?: (args?: Record<string, any>) => Promise<any>;
		},
	) => {
		dialogRef.value?.open(args, opt);
	},
});
</script>

<template>
  <FormDialog
    ref="dialogRef"
    :form-schema="formSchema"
    api="/devtools/dynTable/save"
    :watch-form="watchForm"
    :before-submit="beforeSubmit"
    @success="refresh"
    :fetch-data="fetchData"
  />
</template>
