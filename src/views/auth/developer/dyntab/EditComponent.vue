<script setup lang="ts">
import { i18nOptions } from "@/index";
import { useDict, useTableDrag } from "@/index";
import {
	NAutoComplete,
	NButton,
	type NDataTable,
	NInput,
	NSelect,
	NSwitch,
} from "naive-ui";
import { DataTableColumns } from "naive-ui";
import { RowData } from "naive-ui/es/data-table/src/interface";

const { sysDict } = useDict();
const searchValue = ref("");
const dictOptions = computed(() => {
	return Object.keys(sysDict.value)
		.filter((item) =>
			item.toLowerCase().includes((searchValue.value || "").toLowerCase()),
		)
		.map((item) => ({ label: item, value: item }));
});
const createColumns = ({
	updateRow,
	deleteRow,
}: {
	updateRow: (rowData: RowData) => void;
	deleteRow: (rowData: RowData) => void;
}): DataTableColumns<RowData> => {
	return [
		{
			title: "序号",
			key: "sort",
			width: 60,
			fixed: "left",
			render(row) {
				return row.sort + 1;
			},
		},
		{
			title: "拖动",
			key: "drag",
			width: 60,
			fixed: "left",
			render() {
				return h("div", {
					class: [
						"drag-handle",
						"text-black",
						"dark:text-white",
						"cursor-move",
						"w-1.2rem",
						"h-1.2rem",
						"i-icon-park-outline-application-menu",
					],
				});
			},
		},
		{
			title: () =>
				h(
					"div",
					{
						class: "text-lg font-bold text-red-7",
					},
					"基础信息",
				),
			key: "basic",
			children: [
				{
					title: "国际化映射",
					key: "columnLabel",
					width: 220,
					fixed: "left",
					render(row) {
						return h(NSelect, {
							value: row.columnLabel,
							size: "small",
							filterable: true,
							options: i18nOptions.value,
							onUpdateValue(v) {
								row.columnLabel = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "字段名",
					key: "columnName",
					render(row) {
						return h(NInput, {
							value: row.columnName,
							size: "small",
							onUpdateValue(v) {
								row.columnName = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "属性名",
					key: "attrName",
					render(row) {
						return h(NInput, {
							value: row.attrName,
							size: "small",
							onUpdateValue(v) {
								row.attrName = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "字段说明",
					key: "columnComment",
					width: 220,
					fixed: "left",
					render(row) {
						return h(NInput, {
							value: row.columnComment,
							size: "small",
							onUpdateValue(v) {
								row.columnComment = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "是否主键",
					key: "isPk",
					width: 60,
					render(row) {
						return h(NSwitch, {
							value: row.isPk,
							size: "small",
							onUpdateValue(v) {
								row.isPk = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "列表",
					key: "isList",
					width: 60,
					render(row) {
						return h(NSwitch, {
							value: row.isList,
							size: "small",
							onUpdateValue(v) {
								row.isList = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "列宽",
					key: "columnWidth",
					width: 80,
					render(row) {
						return h(NInput, {
							value: row.columnWidth,
							size: "small",
							onUpdateValue(v) {
								row.columnWidth = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "浮动",
					key: "fixed",
					width: 100,
					render(row) {
						return h(NSelect, {
							value: row.fixed,
							size: "small",
							options: [
								{ label: "左", value: "left" },
								{ label: "右", value: "right" },
								{ label: "无", value: "" },
							],
							onUpdateValue(v) {
								row.fixed = v;
								updateRow(row);
							},
						});
					},
				},
			],
		},
		{
			title: () =>
				h(
					"div",
					{
						class: "text-lg font-bold text-red-7",
					},
					"查询配置",
				),
			key: "query",
			children: [
				{
					title: "快速查询",
					key: "isQuery",
					width: 60,
					render(row) {
						return h(NSwitch, {
							value: row.isQuery,
							size: "small",
							onUpdateValue(v) {
								row.isQuery = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "快速查询方式",
					key: "queryLink",
					width: 150,
					render(row) {
						return h(NSelect, {
							value: row.queryLink,
							size: "small",
							options: [
								{ label: "eq(仅留作兼容，后续请选择 select)", value: "eq" },
								{ label: "like(仅留作兼容，后续请选择 input)", value: "like" },
                { label: "输入框", value: "input" },
                { label: "数字输入框", value: "number" },
                { label: "下拉框", value: "select" },
                { label: "多选下拉框", value: "select-multiple" },
                { label: "excel", value: "excel" },
                { label: "excel-textarea", value: "excel-textarea" },
                { label: "日期范围", value: "date-range" },
                { label: "日期时间范围", value: "date-range-time" },
							],
							onUpdateValue(v) {
								row.queryLink = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "高级查询",
					key: "superQuery",
					width: 60,
					render(row) {
						return h(NSwitch, {
							value: row.superQuery,
							size: "small",
							onUpdateValue(v) {
								row.superQuery = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "允许排序",
					key: "isSortable",
					width: 60,
					render(row) {
						return h(NSwitch, {
							value: row.isSortable,
							size: "small",
							onUpdateValue(v) {
								row.isSortable = v;
								updateRow(row);
							},
						});
					},
				},
			],
		},
		{
			title: () =>
				h(
					"div",
					{
						class: "text-lg font-bold text-red-7",
					},
					"表单配置",
				),
			key: "form",
			children: [
				{
					title: "表单",
					key: "isForm",
					width: 60,
					render(row) {
						return h(NSwitch, {
							value: row.isForm,
							size: "small",
							onUpdateValue(v) {
								row.isForm = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "必填",
					key: "isRequired",
					width: 60,
					render(row) {
						return h(NSwitch, {
							value: row.isRequired,
							size: "small",
							onUpdateValue(v) {
								row.isRequired = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "允许编辑",
					key: "isEdit",
					width: 60,
					render(row) {
						return h(NSwitch, {
							value: row.isEdit,
							size: "small",
							onUpdateValue(v) {
								row.isEdit = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "表单类型",
					key: "formType",
					width: 160,
					render(row) {
						return h(NSelect, {
							value: row.formType,
							size: "small",
							options: [
								{ label: "输入框", value: "text" },
								{ label: "数字输入框", value: "number" },
								{ label: "下拉框", value: "select" },
								{ label: "多选下拉", value: "select-multiple" },
								{ label: "单选按钮", value: "radio" },
								{ label: "多选按钮", value: "checkbox" },
								{ label: "日期", value: "date" },
								{ label: "日期时间", value: "date-time" },
								{ label: "日期范围", value: "date-range" },
								{ label: "上传图片", value: "upload-image" },
								{ label: "上传文件", value: "upload-file" },
								{ label: "多行文本框", value: "textarea" },
							],
							onUpdateValue(v) {
								row.formType = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "字典名称",
					key: "dictName",
					width: 180,
					render(row) {
						return h(NAutoComplete, {
							value: row.dictName,
							size: "small",
							clearable: true,
							options: dictOptions.value,
							onUpdateValue(v) {
								row.dictName = v;
								searchValue.value = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "表单校验",
					key: "validator",
					width: 100,
					render(row) {
						return h(NInput, {
							value: row.validator,
							size: "small",
							onUpdateValue(v) {
								row.validator = v;
								updateRow(row);
							},
						});
					},
				},
				{
					title: "初始值",
					key: "initialValue",
					width: 120,
					render(row) {
						return h(NInput, {
							value: row.initialValue,
							size: "small",
							onUpdateValue(v) {
								row.initialValue = v;
								updateRow(row);
							},
						});
					},
				},
			],
		},
		{
			title: "备注",
			key: "remark",
			width: 100,
			render(row) {
				return h(NInput, {
					value: row.remark,
					size: "small",
					onUpdateValue(v) {
						row.remark = v;
						updateRow(row);
					},
				});
			},
		},
		{
			title: "操作",
			key: "actions",
			fixed: "right",
			width: 80,
			render(row) {
				return h(
					NButton,
					{
						text: true,
						type: "error",
						size: "small",
						onClick: () => deleteRow(row),
					},
					{ default: () => "删除" },
				);
			},
		},
	];
};
const modelValue = defineModel<RowData[]>({
	required: true,
});

function handleUpdateRow(row: RowData) {
	const index = modelValue.value.findIndex((item) => item.id === row.id);

	if (index > -1) {
		const newData = [...modelValue.value];
		newData[index] = row;
		modelValue.value = newData;
	}
}

function handleDeleteRow(row: RowData) {
	const index = modelValue.value.findIndex((item) => item.id === row.id);
	if (index > -1) {
		const newData = [...modelValue.value];
		newData.splice(index, 1);
		modelValue.value = newData;
	}
}

const columns = createColumns({
	updateRow: handleUpdateRow,
	deleteRow: handleDeleteRow,
});

const tableRef = ref<InstanceType<typeof NDataTable>>();
useTableDrag({
	tableRef,
	data: modelValue,
});
watch(modelValue, () => {
	modelValue.value.forEach((item, index) => {
		item.sort = index;
	});
});
</script>

<template>
  <n-data-table ref="tableRef" :columns="columns" :data="modelValue" :pagination="false" :scroll-x="2360"
    :single-line="false" striped size="small" single-column :row-key="row => row._id" />
</template>

<style scoped>
.drag-handle {
  cursor: move;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
