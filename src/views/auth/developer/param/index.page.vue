<script setup lang="ts">
import { genActionHideList } from "@/utils";
import { FormDialogV2, FormSchema, useTable, useRequest, useDelDialog } from "@/index";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { state, on, refresh } = useTable(
	"/sys/params/page",
	[
		{
			title: t("pages.param.code"),
			key: "paramCode",
			query: "input",
		},
		{
			title: t("pages.param.value"),
			key: "paramValue",
		},
		{
			title: t("pages.param.remark"),
			key: "remark",
		},
	],
	{
		actionHideList: [
			"importTable",
			"exportTable",
			...genActionHideList({
				save: "sys:param:save",
				edit: "sys:param:edit",
				delete: "sys:param:delete",
				importTable: "sys:param:import",
				exportTable: "sys:param:export",
			}),
		],
	},
);

const handleRefreshCache = async () => {
	const { error } = await useRequest("/sys/params/clearCache");
	if (!error.value) {
		window.$message.success(t("pages.param.refreshCacheSuccess"));
	}
};

const formSchema: FormSchema = [
	{
		label: t("pages.param.code"),
		type: "input",
		prop: "paramCode",
		rules: {
			required: true,
		},
	},
	{
		label: t("pages.param.value"),
		type: "input",
		prop: "paramValue",
		rules: {
			required: true,
		},
	},
	{
		label: t("pages.param.remark"),
		type: "input",
		prop: "remark",
	},
];
const formConfigs = [
	{
		key: "new",
		config: {
			formSchema,
			api: "/sys/params/save",
      onSuccess: () => {
        refresh()
      }
		},
	},
	{
		key: "edit",
		config: {
			formSchema,
			api: "/sys/params/update",
			fetchData: async (row: any) => {
				const { data } = await useRequest(`/sys/params/info/${row.id}`);
				if (data.value) {
					return data.value;
				}
				return {};
			},
      onSuccess: () => {
        refresh()
      }
		},
	},
];

const formDialogRef = ref<InstanceType<typeof FormDialogV2>>();
on((e, row) => {
	if (e === "new" || e === "edit") {
		formDialogRef.value?.open(e, row);
	}
	if (e === "delete") {
		useDelDialog({
			api: "/sys/params/deleteMul",
			params: [row.id],
			afterSuccess: () => {
				refresh();
			},
		});
	}
});
</script>

<template>
  <mar-wrapper v-model="state">
    <template #toolbarExt>
      <n-button size="small" type="success" @click="handleRefreshCache">
        {{ t('pages.param.refreshCache') }}
      </n-button>
    </template>
    <template #free-zone>
      <FormDialogV2 label="" :form-configs="formConfigs" ref="formDialogRef" />
    </template>
  </mar-wrapper>
</template>
