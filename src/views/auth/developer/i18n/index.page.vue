<script setup lang="ts">
import { t } from "@/modules/i18n";
import { genActionHideList } from "@/utils";
import {
	type FormConfig,
	FormDialog,
	FormDialogV2,
	type FormSchema,
	UploadDialog,
	useTable,
	useRequest,
	useDelDialog,
} from "@/index";
import { useFileDialog } from '@vueuse/core'

const { state, on, trigger, refresh } = useTable(
	{
		page: "/sys/language/page",
		exportTable: "/sys/language/exportTable",
		importTable: "/sys/language/importTable",
		importTemplate: "/sys/language/importTemplate",
	},
	[
		{
			title: t("pages.config.i18n.tableTitleCode"),
			key: "i18n",
			query: "input",
		},
		{
			title: t("pages.config.i18n.tableTitleZhCnName"),
			key: "zh_CN",
			query: "input",
		},
		{
			title: t("pages.config.i18n.tableTitleEnName"),
			key: "en_US",
			query: "input",
		},
		{
			title: t("pages.config.i18n.tableTitleZhTwName"),
			key: "zh_TW",
			query: "input",
		},
	],
	{
		actionHideList: [
			...genActionHideList({
				save: "sys:language:save",
				edit: "sys:language:edit",
				delete: "sys:language:delete",
				importTable: "sys:language:import",
				exportTable: "sys:language:export",
			}),
		],
    defaultSort: {
      columnKey: "i18n",
      order: "ascend",
    },
	},
);

const formSchema = [
	{
		label: t("pages.config.i18n.tableTitleCode"),
		prop: "i18n",
		type: "input",
		rules: {
			required: true,
		},
	},
	{
		label: t("pages.config.i18n.tableTitleZhCnName"),
		prop: "zh_CN",
		type: "input",
	},
	{
		label: t("pages.config.i18n.tableTitleEnName"),
		prop: "en_US",
		type: "input",
	},
	{
		label: t("pages.config.i18n.tableTitleZhTwName"),
		prop: "zh_TW",
		type: "input",
	},
] satisfies FormSchema;
const formConfigs = [
	{
		key: "edit",
		config: {
			api: "/sys/language/update",
			formSchema: formSchema,
			title: t("common.edit"),
			onSuccess: refresh,
		},
	},
	{
		key: "new",
		config: {
			api: "/sys/language/save",
			formSchema: formSchema,
			title: t("common.new"),
			onSuccess: refresh,
		},
	},
] satisfies FormConfig[];
const editFormDialogRef = ref<InstanceType<typeof FormDialogV2>>();
on((event, args) => {
	if (event === "new") {
		editFormDialogRef.value?.open("new");
	}
	if (event === "edit") {
		editFormDialogRef.value?.open(
			"edit",
			{},
			{
				overrideFetchData: async () => {
					const { data } = await useRequest<any>(
						`/sys/language/info/${args.id}`,
					);
					if (data.value) {
						return data.value;
					}
				},
			},
		);
	}
	if (event === "delete") {
		useDelDialog({
			api: "/sys/language/deleteMul",
			params: [args.id],
			afterSuccess: refresh,
		});
	}
});

async function handleUpload() {
	const { open, onChange } = useFileDialog({
		accept: '.xlsx',
		multiple: false,
	});

	onChange(async (files) => {
		if (!files) return;
		if (files.length === 0) return;
		if (files[0].size === 0) return;

		// 验证文件类型是否为Excel
		if (!files[0].name.endsWith('.xlsx')) {
			window.$message.error(t('common.formatError'));
			return;
		}

		const file = files[0];
		const formData = new FormData();
		formData.append('file', file);

			const { error } = await useRequest('/sys/language/importTable2', {
				method: 'POST',
				body: formData,
			}, {
        beforeFetch(ctx) {
          if (ctx.options.headers) {
            delete ctx.options.headers['Content-Type'];
          }
        }
      });
		if (error.value) {
			window.$message.error(error.value.message || t('common.operationFailed'));
			return;
		}
		window.$message.success(t('common.operationSuccess'));
		refresh();
	});

  open()
}
</script>

<template>
  <mar-wrapper v-model="state">
    <template #free-zone>
      <FormDialogV2 :form-configs="formConfigs" ref="editFormDialogRef" />
      <UploadDialog template-api="/sys/language/importTemplate" upload-api="/sys/language/importTable"
        :title="t('pages.config.i18n.upload')" ref="uploadDialogRef" />
    </template>
    <template #toolbarExt>
      <!-- <n-button @click="handleUpload" size="small">
        <template #icon>
          <div class="i-icon-park-outline-afferent w-1.2rem h-1.2rem" />
        </template>
        {{ t('pages.config.i18n.upload') }}
      </n-button> -->
    </template>
  </mar-wrapper>
</template>
