<script setup lang="ts">
import { type FormDialogV2, useTable, useRequest, useDelDialog } from "@/index";
import { useTemplateRef } from "vue";
import { createNewAndUpdateConfigs } from "./newAndUpdateConfig";
import { genActionHideList } from '@/utils';

const newAndUpdateDialogRef = useTemplateRef<InstanceType<typeof FormDialogV2>>(
  "newAndUpdateDialogRef",
);
;
const { state, on, refresh, checked } = useTable(
  {
    page: "/hToolTimer/page",
    exportTable: "/hToolTimer/exportTable",
    importTable: "/hToolTimer/importTable",
    importTemplate: "/hToolTimer/importTemplate",
  },
  [
    {
      title: "任务ID",
      key: "id",
    },
    {
      title: "任务编码",
      key: "jobCode",
      query: "input",
    },
    {
      title: "任务描述",
      key: "jobDesc",
      query: "input",
    },
    {
      title: "任务组名",
      key: "jobGroup",
    },
    {
      title: "调用目标",
      key: "invokeTarget",
    },
    {
      title: "定时表达式",
      key: "triggerCron",
    },
    {
      title: "状态",
      key: "jobStatus",
      render: (row) => {
        return row.jobStatus === 0 ? "停止" : "运行";
      },
    },
    {
      title: "上次触发时间",
      key: "triggerLastTime",
    },
    {
      title: "上次执行结果",
      key: "triggerLastStatus",
      render: (row) => {
        return row.triggerLastStatus === 0 ? "失败" : "成功";
      },
    },
    {
      title: "日志开关",
      key: "logStatus",
      render: (row) => {
        return row.logStatus === 0 ? "关闭" : "开启";
      },
    },
  ],
  {
    checkable: true,
    actionHideList: [
      "config",
      ...genActionHideList({
        save: "hToolTimer:save",
        deleteMul: "hToolTimer:deleteMul",
        edit: "hToolTimer:edit",
        groupSearch: "hToolTimer:groupSearch",
        importTable: "hToolTimer:importTable",
        exportTable: "hToolTimer:exportTable",
      }),
    ],
    rowActions: (row) => {
      const acs = [
        {
          label: "执行一次",
          click: () => {
            window.$dialog.info({
              title: "操作",
              content: "是否要执行一次？",
              positiveText: "确定",
              negativeText: "取消",
              onPositiveClick: async () => {
                const { error } = await useRequest(
                  `/hToolTimer/execTask/${row.id}`,
                )
                if (error.value) return;
                window.$message.success("执行一次成功");
                refresh();
              }
            })
          },
        },
        {
          label: '删除',
          click: async () => {
            const { error } = await useRequest(
              `/hToolTimer/delete/${row.id}`,
            )
            if (error.value) return;
            window.$message.success("删除成功");
            refresh();
          }
        }
      ];
      if (row.jobStatus === 0) {
        acs.push({
          label: "启动",
          click: async () => {
            const { error } = await useRequest(
              `/hToolTimer/startJob/${row.id}`,
            )
            if (error.value) return;
            window.$message.success("启动成功");
            refresh();
          },
        });
      } else {
        acs.push({
          label: "停止",
          click: async () => {
            const { error } = await useRequest(
              `/hToolTimer/stopJob/${row.id}`,
            )
            if (error.value) return;
            window.$message.success("停止成功");
            refresh();
          },
        });
      }
      return acs;
    },
  },
);
const newAndUpdateConfigs = createNewAndUpdateConfigs((newValue) => {
  newAndUpdateDialogRef.value?.updateFormModel?.({
    ...newAndUpdateDialogRef.value.formModel,
    ...newValue,
  });
}, refresh)
on(async (e, row) => {
  if (e === "edit") {
    newAndUpdateDialogRef.value?.open(
      "update",
      {},
      {
        overrideFetchData: () => row,
      },
    );
  }
  if (e === "new") {
    newAndUpdateDialogRef.value?.open("new");
  }
  if (e === "delete") {
    useDelDialog({
      api: "/hToolTimer/remove",
      params: row.id,
      afterSuccess: () => {
        window.$message.success("删除成功");
        refresh();
      },
    });
  }
  if (e === "deleteMul") {
    useDelDialog({
      api: "/hToolTimer/deleteMul",
      params: checked.value.map(item => item.id),
      afterSuccess: () => {
        window.$message.success("删除成功");
        refresh();
      },
    });
  }
});

const handleStartAll = () => {
  window.$dialog.info({
    title: "操作",
    content: "是否要启动全部？",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      const { error } = await useRequest(
        "/hToolTimer/startAllJob",
      )
      if (error.value) return;
      window.$message.success("启动全部成功");
      refresh();
    }
  })
}

const handleStopAll = () => {
  window.$dialog.info({
    title: "操作",
    content: "是否要停止全部？",
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      const { error } = await useRequest(
        "/hToolTimer/stopAllJob",
      )
      if (error.value) return;
      window.$message.success("停止全部成功");
      refresh();
    }
  })
}
</script>

<template>
  <mar-wrapper v-model="state">
    <template #toolbarExt>
      <div class="flex items-center gap-2">
        <NButton type="success" size="small" @click="handleStartAll">
          <template #icon>
            <div class="i-icon-park-outline-play" />
          </template>
          启动全部
        </NButton>
        <NButton size="small" type="warning" @click="handleStopAll">
          <template #icon>
            <div class="i-icon-park-outline-pause-one" />
          </template>
          停止全部
        </NButton>
      </div>
    </template>
    <template #free-zone>
      <FormDialogV2 :formConfigs="newAndUpdateConfigs" ref="newAndUpdateDialogRef" />
    </template>
  </mar-wrapper>
</template>