import { FormConfig, FormSchema } from "@/index";
import { NButton } from "naive-ui";
import NaiveCron from "naive-ui-cron";

const createFormSchema = (
	updateModelValue: (newValue: Record<string, any>) => void,
): FormSchema => [
	{
		label: "任务编码",
		prop: "jobCode",
		type: "input",
		rules: {
			required: true,
		},
	},
  {
    label: "任务描述",
    prop: "jobDesc",
    type: "input",
  },
	{
		label: "任务分组",
		prop: "jobGroup",
		type: "select",
		options: [
			{ label: "系统", value: 0 }, // 修改为大写
			{ label: "自定义", value: 1 },
		],
	},
	{
		label: "调用方法",
		prop: "invokeTarget",
		type: "input",
    rules: {
      required: true,
    },
    remark: `Bean调用示例：ryTask.ryParams('ry')\n
Class类调用示例：com.ruoyi.quartz.task.RyTask.ryParams('ry')\n
参数说明：支持字符串，布尔类型，长整型，浮点型，整型`
	},
	{
		label: "cron表达式",
		prop: "triggerCron",
		type: "input-group",
		rules: {
			required: true,
		},
		extra: {
			inputGroup: {
				// icon button
				suffix: h(
					NButton,
					{
						onClick() {
							const cronValue = ref("* * * * * ? *");
							const m = window.$modal.create({
								title: "Cron表达式",
								preset: "card",
								style: {
									width: "900px",
								},
								content: () =>
									h(NaiveCron, {
										language: "zh",
										modelValue: cronValue.value,
										onChange: (value: string) => {
											cronValue.value = value;
										},
									}),
								footer: () =>
									h(
										NButton,
										{
											type: "primary",
											class: "ml-auto",
											onClick: () => {
												updateModelValue({
													triggerCron: cronValue.value,
												});
												m.destroy();
											},
										},
										"确定",
									),
							});
						},
					},
					{
						icon: h("div", {
							class: "i-icon-park-outline:setting",
						}),
					},
				),
			},
		},
	},
];

const createNewAndUpdateConfigs = (
	updateModelValue: (newValue: Record<string, any>) => void,
  refresh: () => void
): FormConfig[] => [
	{
		key: "new",
		config: {
			formSchema: createFormSchema(updateModelValue),
			api: "/hToolTimer/save",
			col: 1,
      onSuccess: refresh
		},
	},
	{
		key: "update",
		config: {
			formSchema: createFormSchema(updateModelValue),
			api: "/hToolTimer/update",
			col: 1,
      onSuccess: refresh
		},
	},
];

export { createNewAndUpdateConfigs };
