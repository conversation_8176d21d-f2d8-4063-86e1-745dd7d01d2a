<script setup lang="tsx">
import { FormDialog, createLogRender, useTable, useRequest } from "@/index";
import { genActionHideList } from "@/utils";
import { NSelect } from 'naive-ui';

const modal = useModal();
const { state, refresh } = useTable(
	{
    page: "/hToolTimerLog/page",
    exportTable: "/hToolTimerLog/exportTable",
  },
	[
		{
			title: "任务编码",
			key: "jobCode",
		},
		{
			title: "任务描述",
			key: "jobDesc",
			query: "input",
		},
		{
			title: "任务组名",
			key: "jobGroup",
      query: "input"
		},
		{
			title: "调用目标字符串",
			key: "invokeTarget",
		},
		{
			title: "触发定时标识",
			key: "triggerCron",
		},
		{
			title: "定时器状态",
			key: "jobStatus",
		},
		{
			title: "上次触发时间",
			key: "triggerLastTime",
		},
		{
			title: "上次执行结果",
			key: "triggerLastStatus",
		},
		{
			title: "日志开关",
			key: "logStatus",
		},
	],
	{
		actionHideList: [
			"delete",
			"save",
			"config",
			"importTable",
			"deleteMul",
			"edit",
      ...genActionHideList({
        exportTable: 'hToolTimerLog:exportTable',
        page: 'hToolTimerLog:page',
      })
		],
	},
);

const handleClearLog = () => {
  let range: number = 1
  modal.create({
    title: "清除日志",
    preset: "dialog",
    type: "warning",
    positiveText: "确定",
    negativeText: "取消",
    style: "width: 500px",
    content: () => (
      <div class="flex items-center">
        <p class="mr-2 w-140px">选择清除范围</p>
        <NSelect onUpdate:value={newRange => {
          range = newRange
        }} options={[{
          label: '清理一个月之前',
          value: 30
        }, {
          label: '清理三个月之前',
          value: 90
        }, {
          label: '清理六个月之前',
          value: 180
        }, {
          label: '清理一年之前',
          value: 365
        }, {
          label: '清理全部',
          value: -1
        }
        ]} />
      </div>
    ),
    onPositiveClick: async () => {
      const { error } = await useRequest(`/hToolTimerLog/clearLog/${range}`);
      if (error.value) {
        return;
      }
      window.$message.success("清除成功");
      refresh()
    }
  });
};

</script>

<template>
  <mar-wrapper v-model="state">
    <template #toolbarExt>
      <NButton type="warning" @click="handleClearLog" size="small">
        <template #icon>
          <div class="i-icon-park-outline-clear" />
        </template>
        清除日志
      </NButton>
    </template>
  </mar-wrapper>
</template>
