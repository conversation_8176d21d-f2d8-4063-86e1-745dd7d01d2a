<script setup lang="ts">
import * as clipboard from "clipboard-polyfill"
import { t } from "@/modules/i18n";
import { useFileDialog } from '@vueuse/core'
import { useRequest } from "@/index";

const { data } = await useRequest("/access/accessInfo");

// 硬件信息相关
const showHardwareModal = ref(false);
const hardwareInfo = ref<any>(null);
const hardwareInfoString = computed(() => {
  return hardwareInfo.value ? JSON.stringify(hardwareInfo.value, null, 4) : '';
});

async function getHardwareInfo() {
  const response = await fetch("/api/access/serverInfo");
  const serverInfoData = await response.json();
  hardwareInfo.value = serverInfoData;
  showHardwareModal.value = true;
}
function copyHardwareInfo() {
  if (!hardwareInfoString.value) return;
  
  clipboard.writeText(hardwareInfoString.value)
    .then(() => {
      window.$message.success(t('common.operationSuccess'));
    })
    .catch(() => {
      window.$message.error(t('common.operationFailed'));
    });
}

const { open, onChange } = useFileDialog({
  accept: '.lic',
  multiple: false,
});
onChange(async (files) => {
  if (!files) return;
  if (files.length === 0) return;
  if (files[0].size === 0) return;
  if (files[0].name !== 'license.lic') {
    window.$message.error(t('common.formatError'));
    return;
  }
  const file = files[0];
  const formData = new FormData();
  formData.append('file', file);
  const response = await fetch('/api/access/uploadLicense', {
    method: 'POST',
    body: formData,
  });
  if (!response.ok) {
    window.$message.error(t('common.operationFailed'));
    return;
  }
  const data = await response.json();
  if (data.code !== 200) {
    window.$message.error(data.message);
    return;
  }
  window.$message.success(t('common.operationSuccess'));
})
</script>

<template>
  <NCard>
    <div class="flex flex-col space-y-6">
      <!-- 标题区域 -->
      <div class="flex items-center">
        <div class="bg-red-50 p-2 rounded-md">
          <NIcon size="48" color="#f56c6c">
            <div class="i-icon-park-outline-network-tree"></div>
          </NIcon>
        </div>
        <div class="ml-4">
          <h2 class="text-xl font-bold">{{ $t('pages.about.title') }}</h2>
        </div>
      </div>

      <!-- 授权信息区域 -->
      <div class="grid grid-cols-1 gap-4">
        <div class="flex items-center">
          <span class="text-gray-700 dark:text-gray-300 font-700 w-24">{{ $t('pages.about.issuedTime') }}：</span>
          <span>{{ data?.issuedTime }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-700 dark:text-gray-300 font-700 w-24">{{ $t('pages.about.expiryTime') }}：</span>
          <span>{{ data?.expiryTime }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-700 dark:text-gray-300 font-700 w-24">{{ $t('pages.about.expiryDay') }}：</span>
          <span>{{ data?.expiryDay }}{{ $t('common.day') }}</span>
        </div>
        
        <div class="my-2 border-t border-gray-100 dark:border-gray-700"></div>



        <!-- 分隔线 -->
        <div class="my-2 border-t border-gray-100 dark:border-gray-700"></div>

        <!-- 版本信息区域 -->
        <div class="flex items-center">
          <span class="text-gray-700 dark:text-gray-300 font-700 w-24">{{ $t('pages.about.projectVersion') }}：</span>
        </div>
        <div v-for="(version, index) in data?.projectVersion" :key="index" class="flex items-center ml-6">
          <span class="text-gray-700 dark:text-gray-300 font-700 w-24">{{ version.appName }}：</span>
          <span>{{ version.appVersion }} ({{ version.appDate }})</span>
        </div>

        <!-- 分隔线 -->
        <div class="my-2 border-t border-gray-100 dark:border-gray-700"></div>

        <!-- 服务器信息 -->
        <div class="flex items-center">
          <span class="text-gray-700 dark:text-gray-300 font-700 w-24">{{ $t('pages.about.serverInfo') }}：</span>
        </div>
        
        <!-- 底部操作区域 -->
        <div class="flex items-center space-x-4 mt-4 ml-6">
          <NButton type="info" size="small" text @click="getHardwareInfo">
            {{ $t('pages.about.getHardwareInfo') }}
          </NButton>
          <NButton type="info" size="small" text @click="(e) => open()">
            {{ $t('pages.about.uploadLicenseFile') }}
          </NButton>
        </div>

        <!-- 状态信息 -->
        <div class="text-gray-700 dark:text-gray-300 text-sm mt-4 ml-6">
          {{ $t('pages.about.status') }}: 
          <span :class="data?.status === 1 ? 'text-green-500' : 'text-red-500'">{{ data?.status === 1 ? $t('common.normal') : $t('common.ban') }}</span>
        </div>
      </div>
    </div>
  </NCard>
  
  <!-- 硬件信息弹窗 -->
  <NModal v-model:show="showHardwareModal" preset="card" :title="$t('pages.about.getHardwareInfo')" style="width: 600px">
    <div class="flex flex-col">
      <div class="bg-gray-50 p-4 rounded mb-4 font-mono text-sm whitespace-pre overflow-auto max-h-80">{{ hardwareInfoString }}</div>
      <div class="flex justify-end">
        <NButton @click="copyHardwareInfo" type="primary">
          <template #icon>
            <div class="i-icon-park-outline-copy"></div>
          </template>
          {{ $t('common.copy') }}
        </NButton>
      </div>
    </div>
  </NModal>
</template>
