<script setup lang="ts">
import {t} from "@/modules/i18n";
import {createJsonRender, create<PERSON>og<PERSON><PERSON>, MarW<PERSON>per, useTable, useDict} from "@/index";

const {userDict} = useDict();
const {state} = useTable(
    "/sys/log/operation/page",
    [
      {
        title: t("pages.log.operation.userName"),
        key: "userId",
        options: Object.keys(userDict.value).map((key) => ({
          label: userDict.value[key].name,
          value: key,
        })),
      },
      {
        title: t("pages.log.operation.operation"),
        key: "operation",
        query: "input",
      },
      {
        title: t("pages.log.operation.requestPath"),
        key: "requestPath",
        query: "input",
      },
      {
        title: t("pages.log.operation.requestMethod"),
        key: "requestMethod",
      },
      {
        title: t("pages.log.operation.requestParam"),
        key: "requestParams",
        width: 200,
        query: "input",
        render: createJsonRender(
            t("pages.log.operation.requestParam"),
            "requestParams",
        ),
      },
      {
        title: t("pages.log.operation.responseValue"),
        key: "responseMsg",
        width: 200,
        query: "input",
        render: createJsonRender(
            t("pages.log.operation.responseValue"),
            "responseMsg",
        ),
      },
      {
        title: t("pages.log.operation.errorMsg"),
        key: "errorMsg",
        width: 200,
        render: createLogRender(t("pages.log.operation.errorMsg"), "errorMsg"),
      },
      {
        title: t("pages.log.operation.consumingTime"),
        key: "consumingTime",
        query: 'input',
        sort: 'on',
      },
      {
        title: t("pages.log.operation.resultCode"),
        key: "resultCode",
        query: 'input',
      },
      {
        title: t("pages.log.operation.userIp"),
        key: "userIp",
      },
      {
        title: t("pages.log.operation.userAgent"),
        key: "userAgent",
      },
      {
        title: t("pages.log.operation.createDate"),
        key: "requestDate",
        sort: "on",
        query: 'date'
      },
    ],
    {
      actionHideList: ["save","delete","deleteMul", "edit", 'config','exportTable','importTable', 'groupSearch'],
      defaultSort: {
        columnKey: "requestDate",
        order: "descend",
      },
    },
);
</script>

<template>
  <mar-wrapper v-model="state">
  </mar-wrapper>
</template>
