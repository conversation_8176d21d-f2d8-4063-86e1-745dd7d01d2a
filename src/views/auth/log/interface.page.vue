<script setup lang="ts">
import {t} from "@/modules/i18n";
import {<PERSON><PERSON><PERSON><PERSON>, createJsonRender, useTable} from "@/index";

const {state} = useTable(
    "/sys/log/interface/page",
    [
      {
        title: t("pages.log.interface.appName"),
        key: "appName",
        query: "input",
        superQuery: "input",
      },
      {
        title: t("pages.log.interface.operation"),
        key: "operation",
        query: "input",
        superQuery: "input",
      },
      {
        title: t("pages.log.interface.logType"),
        key: "logType",
        query: "select",
        superQuery: "select",
        options: [
          {label: "发送", value: 1},
          {label: "接收", value: 2},
        ],
      },
      {
        title: t("pages.log.interface.requestDate"),
        key: "requestDate",
        query: "date-range",
        sort: "on",
      },
      {
        title: t("pages.log.interface.consumingTime"),
        key: "consumingTime",
        query: 'input',
        sort: 'on',
      },
      {
        title: t("pages.log.interface.sourceIp"),
        key: "sourceIp",
        query: "input",
      },
      {
        title: t("pages.log.interface.targetIp"),
        key: "targetIp",
        query: "input",
      },
      {
        title: t("pages.log.interface.userAgent"),
        key: "userAgent",
      },
      {
        title: t("pages.log.interface.requestUrl"),
        key: "requestUrl",
        query: "input",
      },
      {
        title: t("pages.log.interface.requestMsg"),
        key: "requestMsg",
        width: 200,
        render: createJsonRender(
            t("pages.log.interface.requestMsg"),
            "requestMsg",
        ),
        query: 'input',
      },
      {
        title: t("pages.log.interface.responseMsg"),
        key: "responseMsg",
        width: 200,
        render: createJsonRender(
            t("pages.log.interface.responseMsg"),
            "responseMsg",
        ),
        query: 'input',
      },
      {
        title: t("pages.log.interface.resultCode"),
        key: "resultCode",
        query: 'input',
      },
    ],
    {
      actionHideList: ["save","delete","deleteMul", "edit", 'config',"importTable","exportTable","groupSearch"],
      defaultSort: {
        columnKey: "requestDate",
        order: "descend",
      },
    },
);
</script>

<template>
  <MarWrapper v-model="state"/>
</template>
