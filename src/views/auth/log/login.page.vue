<script setup lang="ts">
import { t } from "@/modules/i18n";
import { genActionHideList } from '@/utils';
import { MarWrapper, useTable, useDownload } from "@/index";

const { state, on } = useTable(
	{
		page: "/sys/log/login/page",
		exportTable: "/sys/log/login/exportTable",
	},
	[
		{
			title: t("pages.log.login.userName"),
			key: "userName",
			query: "input",
		},
		{
			title: t("pages.log.login.operation"),
			key: "operation",
			query: "select",
			options: [
				{ label: t("pages.log.login.login"), value: 0 },
				{ label: t("pages.log.login.logout"), value: 1 },
			],
		},
		{
			title: t("pages.log.login.loginStatus"),
			key: "loginStatus",
			query: "select",
			options: [
				{ label: t("pages.log.login.success"), value: 1 },
				{ label: t("pages.log.login.failure"), value: 0 },
				{ label: t("pages.log.login.locked"), value: 2 },
			],
			sort: "on",
		},
		{
			title: t("pages.log.login.ip"),
			key: "ip",
      query: 'input',
		},
		{
			title: t("pages.log.login.userAgent"),
			key: "userAgent",
      query: 'input',
		},
		{
			title: t("pages.log.login.createDate"),
			key: "createDate",
			query: 'date-range',
			sort: "on",
		},
	],
	{
		actionHideList: [
			"delete",
			"edit",
			"importTable",
      'config',
			...genActionHideList({
				exportTable: "sys:log:login:exportTable",
				delete: "sys:log:login:deleteMul",
				edit: "sys:log:login:update",
			}),
		],
	},
);

on(async (event) => {
	if (event === "exportTable") {
		await useDownload("/sys/log/login/exportTable", {
			filename: "login.xlsx",
		});
	}
});
</script>

<template>
  <mar-wrapper v-model="state">
  </mar-wrapper>
</template>
