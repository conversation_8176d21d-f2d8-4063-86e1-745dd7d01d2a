<script setup lang="tsx">
import { t } from "@/modules/i18n";
import { DynTable, useDynTable, useRequest } from "@/index";

const options = useDynTable({
	actionBar: ({ checked, refresh }) => [
		{
			label: t("system.button.enable"),
			icon: "i-heroicons-outline-refresh",
			type: "error",
			onClick: async () => {
				if (checked.value.length === 0) {
					window.$message.error(t("common.prompt.needSelectedOne"));
					return;
				}
				const map = checked.value.map((item) => item.id);
				const { data } = await useRequest("/base/project/enable").post(map);
				if (data.value) {
					window.$message.success(t("common.operationSuccess"));
				}
				refresh();
			},
		},
	],
	api: {
		page: "/base/project/page",
		info: "/base/project/info",
		save: "/base/project/save",
		update: "/base/project/update",
		delete: "/base/project/deleteMul",
	},
	otherOptions: {
		buttonHideList: ["importTable"],
		defaultSort: {
			columnKey: "createDate",
			order: "descend",
		},
	},
});
</script>

<template>
  <DynTable v-bind="options"/>
</template>
