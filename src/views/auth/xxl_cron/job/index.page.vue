<script setup lang="ts">
import { type FormDialogV2, useTable, useDelDialog, useRequest } from "@/index";
import { useTemplateRef } from "vue";
import { createNewAndUpdateConfigs } from "./newAndUpdateConfig";
import { runOnceConfigs } from "./runOnceConfig";
import { genActionHideList } from '@/utils';

const runOnceDialogRef =
	useTemplateRef<InstanceType<typeof FormDialogV2>>("runOnceDialogRef");
const newAndUpdateDialogRef = useTemplateRef<InstanceType<typeof FormDialogV2>>(
	"newAndUpdateDialogRef",
);

const { state, on, trigger, refresh } = useTable(
	"/xxl-job-admin/jobinfo/page",
	[
		// 任务 ID
		{
			title: "任务 ID",
			key: "id",
		},
		{
			title: "任务描述",
			key: "jobDesc",
			query: "input",
		},
		{
			title: "调度类型",
			key: "glueType",
			render: (row) => {
				return `${row.glueType}: ${row.executorHandler}`;
			},
		},
		{
			title: "运行模式",
			key: "scheduleType",
			render: (row) => {
				return `${row.scheduleType}: ${row.scheduleConf}`;
			},
		},
		{
			title: "负责人",
			key: "author",
			query: "input",
		},
		{
			title: "状态",
			key: "triggerStatus",
			render: (row) => {
				return row.triggerStatus === 0 ? "停止" : "运行";
			},
		},
	],
	{
		actionHideList: [
			"importTable",
			"exportTable",
			"config",
			...genActionHideList({
				save: "xxl-job-admin:jobinfo:save",
        edit: "xxl-job-admin:jobinfo:update",
        delete: "xxl-job-admin:jobinfo:remove",
			}),
		],
		rowActions: (row) => {
			const acs = [
				{
					label: "执行一次",
					click: () => {
						runOnceDialogRef.value?.open(
							"runOnce",
							{},
							{
								overrideFetchData: () => ({
									id: row.id,
								}),
							},
						);
					},
				},
				{
					label: "注册节点",
					click: async () => {
						const { data, error } = await useRequest<any>(
							"/xxl-job-admin/jobgroup/loadById",
						).post(row.jobGroup);
						if (error.value) return;
						window.$dialog.info({
							title: "注册节点",
							content: data.value?.registryList?.join(",\n"),
						});
					},
				},
				{
					label: "下次执行时间",
					click: () => {
						trigger("next_time", row);
					},
				},
			];
			if (row.triggerStatus === 0) {
				acs.push({
					label: "启动",
					click: async () => {
						const { error } = await useRequest(
							"/xxl-job-admin/jobinfo/start",
						).post(row.id);
						if (error.value) return;
						window.$message.success("启动成功");
						refresh();
					},
				});
			} else {
				acs.push({
					label: "停止",
					click: async () => {
						const { error } = await useRequest(
							"/xxl-job-admin/jobinfo/stop",
						).post(row.id);
						if (error.value) return;
						window.$message.success("停止成功");
						refresh();
					},
				});
			}
			return acs;
		},
    defaultSort: {
      columnKey: "id",
      order: "ascend",
    },
	},
);
const newAndUpdateConfigs = await createNewAndUpdateConfigs((newValue) => {
	newAndUpdateDialogRef.value?.updateFormModel?.({
		...newAndUpdateDialogRef.value.formModel,
		...newValue,
	});
}, refresh);
on(async (e, row) => {
  if (e === 'next_time') {
    const { data } = await useRequest<any[]>('/xxl-job-admin/jobinfo/nextTriggerTime').post({
      scheduleType: row.scheduleType,
      scheduleConf: row.scheduleConf,
    })
    if (data.value) {
      window.$modal.create({
        preset: 'card',
        title: '下次执行时间',
        style: 'width: 300px;',
        content: () => h('div', {
          class: 'flex flex-col gap-2',
        }, data.value?.map((item: string) => h('div', {}, item))),
      })
    }
  }
	if (e === "edit") {
		newAndUpdateDialogRef.value?.open(
			"update",
			{},
			{
				overrideFetchData: () => row,
			},
		);
	}
	if (e === "new") {
		newAndUpdateDialogRef.value?.open("new");
	}
	if (e === "delete") {
		useDelDialog({
			api: "/xxl-job-admin/jobinfo/remove",
			params: row.id,
			afterSuccess: () => {
				window.$message.success("删除成功");
				refresh();
			},
		});
	}
});
</script>

<template>
  <mar-wrapper v-model="state">
    <template #free-zone>
      <FormDialogV2 :formConfigs="runOnceConfigs" ref="runOnceDialogRef" />
      <FormDialogV2 :formConfigs="newAndUpdateConfigs" ref="newAndUpdateDialogRef" />
    </template>
  </mar-wrapper>
</template>
