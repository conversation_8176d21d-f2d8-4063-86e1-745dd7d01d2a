import { FormConfig, FormSchema, useRequest } from "@/index";
import { NButton } from "naive-ui";
import NaiveCron from "naive-ui-cron";

const createFormSchema = async (
	updateModelValue: (newValue: Record<string, any>) => void,
): Promise<FormSchema> => {
const { data: jobGroupList } = await useRequest(
	"/xxl-job-admin/jobgroup/getAll",
);
  return [
	{
		label: "执行器",
		prop: "jobGroup", // 修改为 jobGroup
		type: "select",
		rules: {
			required: true,
		},
		options: jobGroupList.value?.map((item) => ({
			value: item.id,
			label: item.title,
		})),
	},
	{
		label: "任务描述",
		prop: "jobDesc",
		type: "input",
		rules: {
			required: true,
		},
	},
	{
		label: "负责人",
		prop: "author",
		type: "input",
		rules: {
			required: true,
		},
	},
	{
		label: "报警邮件",
		prop: "alarmEmail",
		type: "input",
	},
	{
		label: "调度类型",
		prop: "scheduleType",
		type: "select",
		options: [
			{ label: "CRON", value: "CRON" }, // 修改为大写
			{ label: "ONCE", value: "ONCE" },
		],
	},
	{
		label: "cron表达式",
		prop: "scheduleConf",
		type: "input-group",
		rules: {
			required: true,
		},
		extra: {
			inputGroup: {
				// icon button
				suffix: h(
					NButton,
					{
						onClick() {
							const cronValue = ref("* * * * * ? *");
              const nextTriggerTimes = ref<string[]>([]);
							const m = window.$modal.create({
								title: "Cron表达式",
								preset: "card",
								style: {
									width: "900px",
								},
								content: () =>
									h("div", [
										h(NaiveCron, {
											language: "zh",
											modelValue: cronValue.value,
											showNextTimes: false,
											onChange: (value: string) => {
												cronValue.value = value;
											},
										}),
										h(NButton, {
											class: "mt-4",
											onClick: async () => {
                        const { data } = await useRequest('/xxl-job-admin/jobinfo/nextTriggerTime').post({
                          scheduleConf: cronValue.value,
                          scheduleType: 'CRON',
                        })
                        if (data.value) {
                          nextTriggerTimes.value = data.value
                        }
											}
										}, "获取近期执行时间"),
                    nextTriggerTimes.value?.length ? h('div', {
                      class: 'mt-4'
                    }, [
                      h('div', '近期执行时间:'),
                      ...nextTriggerTimes.value.map(time => h('div', {
                        class: 'mt-2'
                      }, time))
                    ]) : null
									]),
								footer: () =>
									h(
										NButton,
										{
											type: "primary",
											class: "ml-auto",
											onClick: () => {
												updateModelValue({
													scheduleConf: cronValue.value,
												});
												m.destroy();
											},
										},
										"确定",
									),
							});
						},
					},
					{
						icon: h("div", {
							class: "i-icon-park-outline:setting",
						}),
					},
				),
			},
		},
	},
	{
		label: "运行模式",
		prop: "glueType", // 修改为 glueType
		type: "select",
		options: [
			{ label: "BEAN", value: "BEAN" }, // 修改为 BEAN
			{ label: "GLUE", value: "GLUE" },
		],
	},
	{
		label: "JobHandler",
		prop: "executorHandler", // 修改为 executorHandler
		type: "input",
		rules: {
			required: true,
		},
	},
	{
		label: "任务参数",
		prop: "executorParam",
		type: "input",
	},
	{
		label: "路由策略",
		prop: "executorRouteStrategy",
		type: "select",
		options: [
			{ label: "随机路由", value: "RANDOM" },
			{ label: "轮询路由", value: "ROUND" },
		],
	},
	{
		label: "子任务ID",
		prop: "childJobId",
		type: "input",
	},
	{
		label: "调度过期策略",
		prop: "misfireStrategy",
		type: "select",
		options: [
			{ label: "忽略", value: "DO_NOTHING" },
			{ label: "立即执行一次", value: "FIRE_ONCE_NOW" },
		],
	},
	{
		label: "阻塞处理策略",
		prop: "executorBlockStrategy",
		type: "select",
		options: [
			{ label: "单机串行", value: "SERIAL_EXECUTION" },
			{ label: "丢弃后续调度", value: "DISCARD_LATER" },
		],
	},
	{
		label: "任务超时时间",
		prop: "executorTimeout",
		type: "input-number",
	},
	{
		label: "失败重试次数",
		prop: "executorFailRetryCount",
		type: "input-number",
	},
  ]
}

const createNewAndUpdateConfigs = async (
	updateModelValue: (newValue: Record<string, any>) => void,
  afterSubmit: () => void,
): Promise<FormConfig[]> => [
	{
		key: "new",
		config: {
			formSchema: await createFormSchema(updateModelValue),
			api: "/xxl-job-admin/jobinfo/add",
			col: 2,
      onSuccess: afterSubmit,
		},
	},
	{
		key: "update",
		config: {
			formSchema: await createFormSchema(updateModelValue),
			api: "/xxl-job-admin/jobinfo/update",
			col: 2,
      onSuccess: afterSubmit,
		},
	},
];

export { createNewAndUpdateConfigs };
