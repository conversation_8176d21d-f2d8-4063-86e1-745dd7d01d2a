import { FormConfig, FormSchema } from "@/index";

const createFormSchema = (): FormSchema => [
	{
		label: "AppName",
		prop: "appName",
		type: "input",
		rules: {
			required: true,
		},
	},
	{
		label: "名称",
		prop: "title",
		type: "input",
		rules: {
			required: true,
		},
	},
	{
		label: "注册方式",
		prop: "addressType",
		type: "select",
		rules: {
			required: true,
		},
		options: [
			{ label: "自动注册", value: 0 },
			{ label: "手动注册", value: 1 },
		],
	},
	{
		label: "地址列表",
		prop: "addressList",
		type: "input",
	},
];

const createNewAndUpdateConfigs = (afterSubmit: () => void): FormConfig[] => [
	{
		key: "new",
		config: {
			formSchema: createFormSchema(),
			api: "/xxl-job-admin/jobgroup/add",
			col: 1,
			onSuccess: afterSubmit,
		},
	},
	{
		key: "update",
		config: {
			formSchema: createFormSchema(),
			api: "/xxl-job-admin/jobgroup/update",
			col: 1,
			onSuccess: afterSubmit,
		},
	},
];

export { createNewAndUpdateConfigs };
