<script setup lang="ts">
import { genActionHideList } from "@/utils";
import { type FormDialogV2, useTable, useDelDialog } from "@/index";
import { useTemplateRef } from "vue";
import { createNewAndUpdateConfigs } from "./newAndUpdateConfig";

const newAndUpdateDialogRef = useTemplateRef<InstanceType<typeof FormDialogV2>>(
	"newAndUpdateDialogRef",
);

const { state, on, refresh } = useTable(
	"/xxl-job-admin/jobgroup/page",
	[
		{
			title: "序号",
			key: "id",
		},
		{
			title: "AppName",
			key: "appName",
			query: "input",
		},
		{
			title: "名称",
			key: "title",
			query: "input",
		},
		{
			title: "注册方式",
			key: "addressType",
			render: (row) => {
				return row.addressType === 0 ? "自动注册" : "手动注册";
			},
		},
		{
			title: "服务地址",
			key: "addressList",
		},
	],
	{
		actionHideList: [
			"config",
			"importTable",
			"exportTable",
			"deleteMul",
			...genActionHideList({
				save: "xxl-job-admin:jobgroup:save",
        edit: "xxl-job-admin:jobgroup:update",
        delete: "xxl-job-admin:jobgroup:delete",
			}),
		],
    defaultSort: {
      columnKey: "id",
      order: "ascend",
    },
	},

);

const newAndUpdateConfigs = createNewAndUpdateConfigs(refresh);
on(async (e, row) => {
	if (e === "edit") {
		newAndUpdateDialogRef.value?.open(
			"update",
			{},
			{
				overrideFetchData: () => row,
			},
		);
	}
	if (e === "new") {
		newAndUpdateDialogRef.value?.open("new");
	}
	if (e === "delete") {
		useDelDialog({
			api: "/xxl-job-admin/jobgroup/remove",
			params: row.id,
			afterSuccess: () => {
				window.$message.success("删除成功");
				refresh();
			},
		});
	}
});
</script>

<template>
  <mar-wrapper v-model="state">
    <template #free-zone>
      <FormDialogV2 :formConfigs="newAndUpdateConfigs" ref="newAndUpdateDialogRef" />
    </template>
  </mar-wrapper>
</template>
