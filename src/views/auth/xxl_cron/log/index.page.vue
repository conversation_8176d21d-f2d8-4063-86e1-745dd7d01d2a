<script setup lang="tsx">
import { FormDialog, createLogRender, useTable, useRequest } from "@/index";

const { data: jobGroupList } = await useRequest(
	"/xxl-job-admin/jobgroup/getAll",
);
const jobIdList = ref<
	{
		id: string;
		jobDesc: string;
	}[]
>([]);

const jobGroupGetter = computed(() => [
	{ value: "0", label: "全部" },
	...jobGroupList.value.map((item) => ({ value: item.id, label: item.title })),
]);
const jobIdListGetter = computed(() => [
	{ value: "0", label: "全部" },
	...jobIdList.value.map((item) => ({ value: item.id, label: item.jobDesc })),
]);

const modal = useModal();
const { state } = useTable(
	"/xxl-job-admin/joblog/page",
	[
		{
			title: "任务 ID",
			key: "jobId",
		},
		{
			title: "JobHandler",
			key: "executorHandler",
		},
		{
			title: "调度时间",
			key: "triggerTime",
		},
		{
			title: "调度结果",
			key: "triggerCode",
		},
		{
			title: "调度备注",
			key: "triggerMsg",
			render: createLogRender("调度备注", "triggerMsg"),
		},
		{
			title: "执行时间",
			key: "handleTime",
			query: "date-range",
		},
		{
			title: "执行结果",
			key: "handleCode",
		},
		{
			title: "执行备注",
			key: "handleMsg",
      render: createLogRender("执行备注", "handleMsg"),
		},
	],
	{
		actionHideList: [
			"delete",
			"save",
			"config",
			"importTable",
			"exportTable",
			"deleteMul",
			"edit",
		],
		rowActions: [
			{
				label: "日志片段",
				click: async (row) => {
					const { data } = await useRequest(
						"/xxl-job-admin/joblog/logDetailCat",
					).post({
						executorAddress: row.executorAddress,
						triggerTime: row.triggerTime,
						logId: row.id,
						fromLineNum: 1,
					});
					if (!data.value) return;
					modal.create({
						title: "日志片段",
						preset: "dialog",
						content: () => (
							<pre class="whitespace-pre-wrap break-all">
								{data.value?.logContent}
							</pre>
						),
					});
				},
			},
		],
		toolbarExt: [
			() => (
				<div>
					<FormDialog
						label="清除日志"
						formSchema={[
							{
								label: "执行器",
								prop: "jobGroup",
								type: "select",
								options: jobGroupGetter.value,
							},
							{
								label: "任务",
								prop: "jobId",
								type: "select",
								options: jobIdListGetter.value,
							},
							{
								label: "测试",
								prop: "test",
								type: "input",
								default: "123",
							},
							{
								label: "清除方式",
								prop: "type",
								type: "select",
								options: [
									{ label: "清理一个月之前日志数据", value: 1 },
									{ label: "清理三个月之前日志数据", value: 2 },
									{ label: "清理六个月之前日志数据", value: 3 },
									{ label: "清理一年之前日志数据", value: 4 },
									{ label: "清理一千条以前日志数据", value: 5 },
									{ label: "清理一万条以前日志数据", value: 6 },
									{ label: "清理三万条以前日志数据", value: 7 },
									{ label: "清理十万条以前日志数据", value: 8 },
									{ label: "清理所有日志数据", value: 9 },
								],
							},
						]}
						watchForm={async (changedProps, form) => {
							if (changedProps.jobGroup) {
								jobIdList.value.length = 0;
								const { data } = await useRequest(
									`/xxl-job-admin/joblog/getJobsByGroup?jobGroup=${changedProps.jobGroup}`,
								);
								jobIdList.value = data.value?.map((item) => ({
									id: item.id,
									jobDesc: item.jobDesc,
								}));
							}
						}}
						api="/xxl-job-admin/joblog/clear"
					/>
				</div>
			),
		],
    defaultSort: {
      columnKey: "triggerTime",
      order: "descend",
    },
	},
);
</script>

<template>
  <mar-wrapper v-model="state">
  </mar-wrapper>
</template>
