<script setup lang="ts">
import { t } from "@/modules/i18n";
import { FormDialog, FormSchema, useRequest } from "@/index";
import { TreeSelectOption } from "naive-ui";

const props = defineProps<{
	api: string;
	deptList: TreeSelectOption[];
}>();

const schema = computed<FormSchema>(
	() =>
		[
			{
				prop: "deptName",
				label: t("pages.perm.department.name"),
				type: "input",
				rules: {
					required: true,
				},
			},
			{
				prop: "parentId",
				label: t("pages.perm.department.parent"),
				type: "tree-select",
				options: props.deptList,
				rules: {
					required: true,
				},
			},
			{
				prop: "orderNum",
				label: t("pages.perm.department.orderNum"),
				type: "input-number",
				rules: {
					required: true,
				},
			},
		] satisfies FormSchema,
);

const formDialogRef = ref<InstanceType<typeof FormDialog> | null>(null);
const fetchData = async (args: Record<string, any>) => {
	const { data } = await useRequest(`/sys/dept/info/${args.id}`);
	if (data.value) {
		return data.value;
	}
	return {};
};

defineExpose({
	open: (
		args?: Record<string, any>,
		opt?: {
			noFetch?: boolean;
			overrideFetchData?: (args?: Record<string, any>) => Promise<any>;
		},
	) => {
		formDialogRef.value?.open(args, opt);
	},
});
</script>

<template>
  <FormDialog
    ref="formDialogRef"
    :title="$t('common.edit')"
    :form-schema="schema"
    :fetch-data="fetchData"
    :api="api"
  />
</template>
