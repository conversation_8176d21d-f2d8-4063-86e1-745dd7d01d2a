<script setup lang="ts">
import { t } from "@/modules/i18n";
import { useRequest } from "@/index";
import { onClickOutside } from "@vueuse/core";
import { TreeOption } from "naive-ui";
import DepartmentDialog from "./DepartmentDialog.vue";
import {
	DepartmentTreePageItem,
	DepartmentTreePageResponse,
} from "./types";

const { data, error, execute } =
	await useRequest<DepartmentTreePageResponse>("/sys/dept/treePage");

// 添加选中节点状态
const selectedKey = ref<string | null>(null);

function convertToTreeOption(item: DepartmentTreePageItem): TreeOption {
	if (item.children?.length) {
		return {
			key: item.id,
			value: item.id,
			label: item.deptName,
			children: item.children?.map((child) => convertToTreeOption(child)) ?? [],
		};
	}
	return {
		key: item.id,
		value: item.id,
		label: item.deptName,
	};
}

const treeData = computed(() => {
	return data.value?.map((item) => convertToTreeOption(item)) ?? [];
});

const showDropdown = ref(false);
const x = ref(0);
const y = ref(0);
const dropdownRef = ref<HTMLElement>();
const currentNode = ref<{
	key: string;
	label: string;
}>();

const opt = [
	{ label: t("common.create"), key: "create" },
	{ label: t("common.edit"), key: "edit" },
	{ label: t("common.delete"), key: "delete" },
];

function handleContextMenu(
	e: MouseEvent,
	node: {
		key: string;
		label: string;
	},
) {
	e.preventDefault();
	showDropdown.value = true;
	x.value = e.clientX;
	y.value = e.clientY;
	currentNode.value = node;
  console.log(node);
  selectedKey.value = node.key;
}

const departmentDialogRef = ref<InstanceType<typeof DepartmentDialog> | null>(
	null,
);
const dialogApi = ref("/sys/dept/update");

async function handleSelect(key: string | number) {
	showDropdown.value = false;
	if (key === "edit" && selectedKey) {
		departmentDialogRef.value?.open({ id: selectedKey.value });
		dialogApi.value = "/sys/dept/update";
	}
	if (key === "create") {
		departmentDialogRef.value?.open(
			{},
			{
				overrideFetchData: async () => ({
					parentId: selectedKey,
				}),
			},
		);
		dialogApi.value = "/sys/dept/save";
	}
	if (key === "delete") {
		window.$dialog.warning({
			title: t("common.delete"),
			content: t("common.deleteConfirm"),
			positiveText: t("common.confirm"),
			negativeText: t("common.cancel"),
			onPositiveClick: async () => {
				const { error } = await useRequest(
					`/sys/dept/delete/${selectedKey.value}`,
				).post();
				if (error.value) return;
				window.$message.success(t("common.operationSuccess"));
				execute();
			},
		});
	}
}

onClickOutside(dropdownRef, (event) => {
	if (
		event.target &&
		"closest" in event.target &&
		!(event.target as Element).closest(".n-dropdown-option")
	) {
		showDropdown.value = false;
	}
});

function renderLabel(info: { option: TreeOption }) {
	return h(
		"div",
		{
			class: ["w-full h-30px flex items-center cursor-pointer", { 'bg-primary-1': selectedKey.value === info.option.key }],
			onContextmenu: (e: MouseEvent) =>
				handleContextMenu(
					e,
					info.option as unknown as {
						key: string;
						label: string;
					},
				),
      onClick: () => selectedKey.value = info.option.key as string
		},
		info.option.label as string,
	);
}
</script>

<template>
  <CommonError v-if="error" />
  <n-card v-else-if="treeData" :title="$t('pages.perm.department.title')">
    <template #header-extra>
      <n-space>
        <n-button type="primary" :disabled="!selectedKey" @click="() => handleSelect('create')">
          {{ $t('common.create') }}
        </n-button>
        <n-button type="primary" :disabled="!selectedKey" @click="() => handleSelect('edit')">
          {{ $t('common.edit') }}
        </n-button>
        <n-button type="error" :disabled="!selectedKey" @click="() => handleSelect('delete')">
          {{ $t('common.delete') }}
        </n-button>
      </n-space>
    </template>
    <div class="text-sm text-gray-500 mb-4">{{ $t('tip.rightClick') }}</div>
    <n-tree
      :data="treeData"
      default-expand-all
      block-line
      :render-label="renderLabel"
    />

    <DepartmentDialog
      ref="departmentDialogRef"
      :deptList="treeData"
      :api="dialogApi"
      @success="execute"
    />

    <n-dropdown
      ref="dropdownRef"
      :show="showDropdown"
      :options="opt"
      :x="x"
      :y="y"
      placement="bottom-start"
      trigger="manual"
      @select="handleSelect"
    />
  </n-card>
</template>

