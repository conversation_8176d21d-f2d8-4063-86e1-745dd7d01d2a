<script setup lang="tsx">
import { t } from "@/modules/i18n";
import { genActionHideList } from "@/utils";
import {
	type FormDialog,
	type FormDialogV2,
	type FormSchema,
	type TableDialog,
	type UploadDialog,
  useDict,
  useTable,
  useRequest,
  useDelDialog,
} from "@/index";
import { NTransfer } from 'naive-ui';

const { sysDict, departmentDict } = useDict()
const { data: allUserList } = await useRequest<Record<string, { code: string, deptId: string, id: string, name: string }>>("/sys/dict/type/dictTable/user").get();
const { state, on, trigger, refresh, checked } = useTable(
	{
		page: "/sys/warehouse/page",
    importTable: "/sys/warehouse/importTable",
    exportTable: "/sys/warehouse/exportTable",
    importTemplate: "/sys/warehouse/importTemplate",
	},
	[
		{
			title: t("system.header.dataDeptId"),
			key: "dataDeptId",
      options: Object.values(departmentDict.value).map((item) => ({
        label: item.name,
        value: item.id,
      })),
      query: "select",
		},
		{
			title: t("system.header.isEnable"),
			key: "isEnable",
      options: [
        { label: t("system.option.open"), value: 1 },
        { label: t("system.option.close"), value: 0 },
      ],
		},
		{
			title: t("system.header.subRoute"),
			key: "firstRouter",
		},
		{
			title: t("system.header.warehouseCode"),
			key: "warehouseCode",
			query: "input",
		},
		{
			title: t("system.header.warehouseName"),
			key: "warehouseName",
			query: "input",
		},
		{
			title: t("system.header.warehouseType"),
			key: "warehouseType",
      options: Object.values(sysDict.value['warehouse_type']).map((item: any) => ({
        label: t(item.dictLabel),
        value: item.dictValue,
      })),
		},
	],
	{
		checkable: true,
		actionHideList: [
			...genActionHideList({
				save: "sys:warehouse:save",
				edit: "sys:warehouse:edit",
				delete: "sys:warehouse:delete",
        deleteMul: "sys:warehouse:deleteMul",
				importTable: "sys:warehouse:importTable",
				// exportTable: "sys:warehouse:export",
			}),
		],
    rowActions(row) {
      return [
        {
          label: t('common.assign_user'),
          async click() {
            const {data: userList} = await useRequest<any[]>(`/sys/warehouse/userList/${row.warehouseCode}`)
            const m = window.$modal.create({
              title: t('common.assign_user'),
              preset: 'dialog',
              type: 'warning',
              // <n-transfer v-model:value="value" :options="options" />
              content: () => h(NTransfer, {
                value: userList.value,
                onUpdateValue: (val: any) => userList.value = val,
                options: allUserList.value ? Object.values(allUserList.value).filter(item => item.deptId).map(item => ({
                  label: `${item.name} - ${departmentDict.value[item.deptId].name}`,
                  value: item.id
                })) : []
              }),
              positiveText: t('common.saveLabel'),
              negativeText: t('common.cancel'),
              onPositiveClick() {
                const { error } = useRequest(`/sys/warehouse/assignedUser/${row.warehouseCode}`).post(JSON.stringify(userList.value))
                if (error.value)
                  return
                window.$message.success(t(`common.operationSuccess`))
                m.destroy()
              },
              onNegativeClick() {
                m.destroy()
              }
            })
          }
        }
      ]
    }
	},
);

const formSchema = [
	{
		prop: "dataDeptId",
		label: t("system.header.dataDeptId"),
		type: "select",
		options: Object.values(departmentDict.value).map((item) => ({
			label: item.name,
			value: item.id,
		})),
	},
	{
		prop: "isEnable",
		label: t("system.header.isEnable"),
		type: "select",
		options: [
			{ label: t("system.option.open"), value: 1 },
			{ label: t("system.option.close"), value: 0 },
		],
	},
	{
		prop: "firstRouter",
		label: t("system.header.subRoute"),
		type: "input",
	},
	{
		prop: "warehouseCode",
		label: t("system.header.warehouseCode"),
		type: "input",
	},
	{
		prop: "warehouseName",
		label: t("system.header.warehouseName"),
		type: "input",
	},
	{
		prop: "warehouseType",
		label: t("system.header.warehouseType"),
		type: "select",
		options: Object.values(sysDict.value['warehouse_type']).map((item: any) => ({
			label: t(item.dictLabel),
			value: item.dictValue,
		})),
	},
] satisfies FormSchema;

const formDialogRef = ref<InstanceType<typeof FormDialog>>();
const editFormDialogRef = ref<InstanceType<typeof FormDialog>>();
const tableDialogRef = ref<InstanceType<typeof TableDialog>>();

on((event: string, args: any) => {
	if (event === "new") {
		formDialogRef.value?.open({});
	}
	if (event === "edit") {
		editFormDialogRef.value?.open(args.id);
	}
	if (event === "delete") {
		useDelDialog({
			api: "/sys/warehouse/deleteMul",
			params: [args.id],
			afterSuccess: refresh,
		});
	}
	if (event === "dictData") {
		tableDialogRef.value?.open();
	}
	if (event === "deleteMul") {
		useDelDialog({
			api: "/sys/warehouse/deleteMul",
			params: checked.value.map((item) => item.id),
			afterSuccess: refresh,
		});
	}
});


async function fetchData(...args: any[]) {
	const { data } = await useRequest(`/sys/warehouse/info/${args[0]}`).get();
	return data.value;
}
const detailDialogRef = ref<InstanceType<typeof FormDialogV2>>();
</script>

<template>
  <mar-wrapper v-model="state">
    <template #free-zone>
      <FormDialog api="/sys/warehouse/update" :form-schema="formSchema" :title="t('common.edit')"
        ref="editFormDialogRef" :fetch-data="fetchData" @success="refresh" />
      <FormDialog api="/sys/warehouse/save" :form-schema="formSchema" :title="t('common.create')" ref="formDialogRef"
        @success="refresh" />
    </template>
  </mar-wrapper>
</template>
