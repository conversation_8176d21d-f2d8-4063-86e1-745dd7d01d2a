<script setup lang="ts">
import { t } from "@/modules/i18n";
import { onClickOutside } from "@vueuse/core";
import { computed } from "vue";

const props = defineProps<{
	roleName: string;
	remark?: string;
	id: string;
	treeRoots: string[];
	selected?: boolean;
}>();
const emit = defineEmits<{
	(e: "edit", id: string): void;
	(e: "create", parentId: string): void;
	(e: "delete", id: string): void;
	(e: "select", id: string): void;
}>();

const opt = computed(() => {
	if (props.treeRoots.includes(props.id)) {
		// 如果是根节点，只返回创建选项
		return [
			{ label: t("common.create"), key: "create" }
		];
	}
	// 如果不是根节点，返回所有选项
	return [
		{ label: t("common.create"), key: "create" },
		{ label: t("common.edit"), key: "edit" },
		{ label: t("common.delete"), key: "delete" },
	];
});

const showDropdown = ref(false);
const x = ref(0);
const y = ref(0);
const dropdownRef = ref<HTMLElement>();

function handleContextMenu(e: MouseEvent) {
	e.preventDefault();
	showDropdown.value = true;
	x.value = e.clientX;
	y.value = e.clientY;
}

async function handleSelect(key: string | number) {
	showDropdown.value = false;
	if (key === "edit") {
		emit("edit", props.id);
	}
	if (key === "create") {
		emit("create", props.id);
	}
	if (key === "delete") {
		emit("delete", props.id);
	}
}

onClickOutside(dropdownRef, (event) => {
	if (
		event.target &&
		"closest" in event.target &&
		!(event.target as Element).closest(".n-dropdown-option")
	) {
		showDropdown.value = false;
	}
});
</script>

<template>
  <div 
    class="flex items-center cursor-pointer" 
    @contextmenu="handleContextMenu"
    @click="emit('select', props.id)"
    :class="{ 'bg-primary-1': props.selected }"
  >
    <div class="mr-2">{{ props.roleName }}</div>
    <div class="text-gray text-12px truncate">{{ props.remark }}</div>
    <n-dropdown ref="dropdownRef" :show="showDropdown" :options="opt" :x="x" :y="y" placement="bottom-start"
      trigger="manual" @select="handleSelect" />
  </div>
</template>
