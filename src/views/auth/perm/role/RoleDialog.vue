<script setup lang="ts">
import { t } from "@/modules/i18n";
import { FormDialog, FormSchema, useRequest } from "@/index";
import { NTreeSelect, TreeSelectOption } from "naive-ui";
import { RoleTreePageItem } from "./types";

const menuTreeRef = ref<InstanceType<typeof NTreeSelect> | null>(null);
const props = defineProps<{
  api: string;
  roleList: TreeSelectOption[];
  nodeList: RoleTreePageItem[];
}>();

interface MenuListItem {
  label: string;
  value: string;
  children?: MenuListItem[];
}
interface ServerDeptListItem {
  id: string;
  parentId: string;
  weight: number;
  deptName: string;
  children?: ServerDeptListItem[];
}
interface ServerMenuListItem {
  id: string;
  pid: string;
  menuName: string;
  menuType: number;
  children?: ServerMenuListItem[];
}
const menuList = ref<MenuListItem[]>([]);
const deptList = ref<MenuListItem[]>([]);
function convertServerToMenuList(data: ServerMenuListItem[]): MenuListItem[] {
  return data.map((item) => ({
    label: t(item.menuName),
    value: item.id,
    children: item.children ? convertServerToMenuList(item.children) : undefined,
  }));
}
function convertServerToDeptList(data: ServerDeptListItem[]): MenuListItem[] {
  return data.map((item) => ({
    label: item.deptName,
    value: item.id,
    children: item.children ? convertServerToDeptList(item.children) : undefined,
  }));
}
const hyperScopeEnabled = ref(true);
const schema = computed<FormSchema>(
  () =>
    [
      {
        prop: "roleName",
        label: t("pages.perm.role.name"),
        type: "input",
        rules: {
          required: true,
        },
      },
      {
        prop: "parentId",
        label: t("pages.perm.role.parent"),
        type: "tree-select",
        options: props.roleList,
        rules: {
          required: true,
        },
        disabled: true,
      },
      {
        prop: "dataScope",
        label: t("pages.perm.role.dataScope"),
        type: "select",
        options: [
          {
            label: t("pages.perm.role.all"),
            value: 1,
            disabled: !hyperScopeEnabled.value,
          },
          { label: t("pages.perm.role.custom"), value: 2 },
          { label: t("pages.perm.role.dept"), value: 3 },
          { label: t("pages.perm.role.self"), value: 4 },
        ],
        rules: {
          required: true,
        },
      },
      {
        prop: "remark",
        label: t("pages.perm.role.remark"),
        type: "input",
      },
      {
        prop: "menuIdList",
        label: t("pages.perm.role.menuList"),
        type: "tree-cascade",
        options: menuList.value,
        bindRef: (ref) => {
          menuTreeRef.value = ref;
        },
      },
      {
        prop: "deptIdList",
        label: t("pages.perm.role.deptIdList"),
        type: "tree-cascade",
        options: deptList.value,
        hide: (formValue: Record<string, any>) => formValue.dataScope !== 2,
      },
    ] satisfies FormSchema,
);

const formDialogRef = ref<InstanceType<typeof FormDialog> | null>(null);
const fetchOptions = async (parentId?: string) => {
  if (!parentId)
    return []

  const [{ data: menuData }, { data: deptData }] = await Promise.all([
    useRequest<ServerMenuListItem[]>(
      `/sys/role/roleMenuTree/${parentId}`,
    ).get(),
    useRequest<ServerDeptListItem[]>(
      `/sys/role/roleDeptTree/${parentId}`,
    ).get(),
  ]);
  if (menuData.value) {
    menuList.value = convertServerToMenuList(menuData.value);
  }
  if (deptData.value) {
    deptList.value = convertServerToDeptList(deptData.value);
  }
}
const fetchData = async (args: Record<string, any>) => {
  hyperScopeEnabled.value = true;
  const { data } = await useRequest<any>(`/sys/role/info/${args.id}`);
  if (data.value) {
    const parent = props.nodeList.find(
      (item) => item.id === data.value.parentId,
    );
    if (parent) {
      hyperScopeEnabled.value = parent.dataScope === 1;
    }
    await fetchOptions(data.value.parentId)
    return data.value;
  }
  return {};
};

defineExpose({
  open: (
    args?: Record<string, any>,
    opt?: {
      noFetch?: boolean;
      overrideFetchData?: (args?: Record<string, any>) => Promise<any>;
    },
    parentId?: string,
  ) => {
    if (parentId) {
      fetchOptions(parentId).then(() => {
        formDialogRef.value?.open(args, opt);
      });
    } else {
      formDialogRef.value?.open(args, opt);
    }
  },
});
</script>

<template>
  <FormDialog ref="formDialogRef" :title="$t('common.edit')" :form-schema="schema" :fetch-data="fetchData" :api="api" />
</template>
