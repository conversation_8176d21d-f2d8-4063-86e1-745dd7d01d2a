<script setup lang="ts">
import { t } from "@/modules/i18n";
import { useRequest } from "@/index";
import { onClickOutside } from "@vueuse/core";
import { TreeOption } from "naive-ui";
import RoleDialog from "./RoleDialog.vue";
import RoleTreeLabelRender from "./RoleTreeLabelRender.vue";
import { RoleTreePageItem, RoleTreePageResponse } from "./types";

const { data, error, execute } =
	await useRequest<RoleTreePageResponse>("/sys/role/treePage");
const treeRoots = computed(() => {
	if (!Array.isArray(data.value)) {
		return [];
	}
	if (data.value.length === 0) return [];
	return data.value.map((item) => item.id);
});

const selectedKey = ref<string | null>(null);
const isRoot = computed(() => selectedKey.value ? treeRoots.value.includes(selectedKey.value) : false);

const nodeList = ref<RoleTreePageItem[]>([]);
function convertToTreeOption(item: RoleTreePageItem): TreeOption {
	nodeList.value.push({
		...item,
		children: undefined,
	});
	if (item.children?.length) {
		return {
			key: item.id,
			value: item.id,
			label: item.roleName,
			remark: item.remark,
			children: item.children?.map((child) => convertToTreeOption(child)) ?? [],
		};
	}
	return {
		key: item.id,
		value: item.id,
		label: item.roleName,
		remark: item.remark,
	};
}

const treeData = computed(() => {
	return data.value?.map((item) => convertToTreeOption(item)) ?? [];
});

const showDropdown = ref(false);
const dropdownRef = ref<HTMLElement>();

const roleDialogRef = ref<InstanceType<typeof RoleDialog> | null>(null);
const dialogApi = ref("/sys/role/update");

function handleEdit(id: string) {
	roleDialogRef.value?.open({ id });
	dialogApi.value = "/sys/role/update";
}

function handleCreate(parentId: string) {
	roleDialogRef.value?.open(
		{},
		{
			overrideFetchData: async () => ({
				parentId,
			}),
		},
		parentId,
	);
	dialogApi.value = "/sys/role/save";
}

function handleDelete(id: string) {
	window.$dialog.warning({
		title: t("common.delete"),
		content: t("common.deleteConfirm"),
		positiveText: t("common.confirm"),
		negativeText: t("common.cancel"),
		onPositiveClick: async () => {
			const { error } = await useRequest(`/sys/role/delete/${id}`).post();
			if (error.value) return;
			window.$message.success(t("common.operationSuccess"));
			execute();
		},
	});
}

onClickOutside(dropdownRef, (event) => {
	if (
		event.target &&
		"closest" in event.target &&
		!(event.target as Element).closest(".n-dropdown-option")
	) {
		showDropdown.value = false;
	}
});

function renderLabel(info: { option: TreeOption }) {
	return h(RoleTreeLabelRender, {
		roleName: info.option.label as string,
		remark: (info.option as any).remark,
		id: info.option.key as string,
		treeRoots: treeRoots.value,
		selected: selectedKey.value === info.option.key,
		onEdit: (id: string) => handleEdit(id),
		onCreate: (parentId: string) => handleCreate(parentId),
		onDelete: (id: string) => handleDelete(id),
		onSelect: (id: string) => selectedKey.value = id,
	});
}
</script>

<template>
  <CommonError v-if="error" />
  <n-card v-else-if="treeData" :title="$t('pages.perm.role.title')">
    <template #header-extra>
      <n-space>
        <n-button type="primary" :disabled="!selectedKey" @click="() => selectedKey && handleCreate(selectedKey)">
          {{ $t('common.create') }}
        </n-button>
        <n-button type="primary" :disabled="!selectedKey || isRoot" @click="() => selectedKey && handleEdit(selectedKey)">
          {{ $t('common.edit') }}
        </n-button>
        <n-button type="error" :disabled="!selectedKey || isRoot" @click="() => selectedKey && handleDelete(selectedKey)">
          {{ $t('common.delete') }}
        </n-button>
      </n-space>
    </template>
    <div class="text-sm text-gray-500 mb-4">{{ $t('tip.rightClick') }}</div>
    <n-tree 
      :data="treeData" 
      default-expand-all 
      block-line 
      :render-label="renderLabel"
    />

    <RoleDialog ref="roleDialogRef" :roleList="treeData" :nodeList="nodeList" :api="dialogApi" @success="execute" />
  </n-card>
</template>
