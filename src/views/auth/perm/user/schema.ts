import { t } from "@/modules/i18n";
import { FormSchema, useDict, useRequest } from "@/index";


const defineFormSchema = async () => {
const { departmentDict } = useDict();
  const { data } =
    await useRequest<
      {
        code: string;
        name: string;
      }[]
    >("/sys/role/list");
    const roleList = data.value?.map((item) => ({
      label: item.name,
      value: item.code,
    }));

  const { data: warehouseData } = await useRequest<any[]>("/sys/warehouse/warehouseDict").get();
  const warehouseList = warehouseData.value?.map((item) => ({
    label: item.name,
    value: item.code,
  }));

  return [
    {
      prop: "username",
      label: t("pages.permission.user.username"),
      type: "input",
      rules: {
        required: true,
      },
    },
    {
      prop: "deptId",
      label: t("pages.permission.user.dept"),
      type: "select",
      options: Object.entries(departmentDict.value).map(([key, value]) => ({
        label: value.name,
        value: key,
      })),
      rules: {
        required: true,
      }
    },
    {
      prop: "realName",
      label: t("pages.permission.user.realName"),
      type: "input",
      rules: {
        required: true,
      },
    },
    {
      prop: "gender",
      label: t("pages.permission.user.gender"),
      type: "radio",
      options: [
        { label: t("pages.permission.user.male"), value: 0 },
        { label: t("pages.permission.user.female"), value: 1 },
        { label: t("pages.permission.user.secret"), value: 2 },
      ],
    },
    {
      prop: "email",
      label: t("pages.permission.user.mail"),
      type: "input",
    },
    {
      prop: "mobile",
      label: t("pages.permission.user.phone"),
      type: "input",
    },
    {
      prop: "roleIdList",
      label: t("pages.permission.user.role"),
      type: "select-multiple",
      options: roleList,
      hide: () => roleList?.length === 0,
    },
    {
      prop: 'warehouse',
      label: t("system.header.warehouse"),
      type: "select",
      options: warehouseList,
      hide: () => warehouseList?.length === 0,
    },
    {
      prop: "isEnable",
      label: t("pages.permission.user.status"),
      type: "radio",
      options: [
        { label: t("common.ban"), value: 0 },
        { label: t("common.normal"), value: 1 },
      ],
      default: 1,
    },
  ] satisfies FormSchema;
};

export default defineFormSchema;
