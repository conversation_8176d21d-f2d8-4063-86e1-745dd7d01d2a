<script setup lang="ts">
import { t } from "@/modules/i18n";
import { genActionHideList } from "@/utils";
import { FormConfig, FormDialogV2, useDict, useTable, useRequest, useDelDialog, useConfirm, UploadDialog } from "@/index";
import defineFormSchema from "./schema";

const { departmentDict } = useDict();
const { state, refresh, on, checked } = useTable(
	"/sys/user/page",
	[
		{
			title: t("pages.permission.user.username"),
			key: "username",
			query: "input",
		},
		{
			title: t("pages.permission.user.dept"),
			key: "deptId",
			query: "select",
			options: Object.entries(departmentDict.value).map(([key, value]) => ({
				label: value.name,
				value: key,
			})),
		},
		{
			title: t("pages.permission.user.mail"),
			key: "email",
		},
		{
			title: t("pages.permission.user.phone"),
			key: "mobile",
		},
		{
			title: t("pages.permission.user.gender"),
			key: "gender",
			query: "select",
			options: [
				{ label: t("pages.permission.user.male"), value: 0 },
				{ label: t("pages.permission.user.female"), value: 1 },
			],
		},
		{
			width: 80,
			title: t("pages.permission.user.status"),
			key: "isEnable",
			options: [
				{ label: t("common.normal"), value: 1 },
				{ label: t("common.ban"), value: 0 },
			],
		},
	],
	{
		rowActions: (row: any) => [
			{
				label: row.isEnable === 1 ? t("common.ban") : t("common.open"),
				class: row.isEnable === 1 ? "text-red-700" : "text-green-500",
				click: (row: any) => {
					useRequest("/sys/user/modifyUserStatus")
						.post({
							id: row.id,
							isEnable: row.isEnable === 1 ? 0 : 1,
						})
						.then(() => {
							refresh();
						});
				},
			},
			{
				label: t("pages.permission.user.resetPassword"),
				click: (row: any) => {
					useConfirm({
						content: t("pages.permission.user.resetPasswordConfirm"),
						api: "/sys/user/resetPassword",
						params: [row.id],
						afterSuccess: () => {
							window.$message.success(
								t("pages.permission.user.resetPasswordSuccess"),
							);
						},
					});
				},
			},
		],
		actionHideList: [
			"exportTable",
      'importTable',
      'config',
			...genActionHideList({
				save: "sys:user:save",
				edit: "sys:user:edit",
				delete: "sys:user:delete",
				deleteMul: "sys:user:deleteMul",
				config: "sys:user:config",
        importTable: "sys:user:importTable",
			}),
		],
		checkable: true,
	},
);

const formSchema = await defineFormSchema();
const formConfigs = [
	{
		key: "new",
		config: {
			api: "/sys/user/save",
			formSchema,
      onSuccess: () => {
        refresh()
      }
		},
	},
	{
		key: "edit",
		config: {
			api: "/sys/user/update",
			formSchema,
      onSuccess: () => {
        refresh()
      }
		},
	},
] satisfies FormConfig[];

const formDialogRef = ref<InstanceType<typeof FormDialogV2>>();
on((e, row) => {
	if (e === "new") {
		formDialogRef.value?.open("new");
	}
	if (e === "edit") {
		formDialogRef.value?.open(
			"edit",
			{},
			{
				overrideFetchData: async () => {
					const { data } = await useRequest(`/sys/user/info/${row.id}`);
					if (data.value) {
						return data.value;
					}
					return {};
				},
			},
		);
	}
	if (e === "delete") {
		useDelDialog({
			api: "/sys/user/deleteMul",
			params: [row.id],
			afterSuccess: () => {
				refresh();
			},
		});
	}
	if (e === "deleteMul") {
		useDelDialog({
			api: "/sys/user/deleteMul",
			params: checked.value.map((item) => item.id),
			afterSuccess: () => {
				refresh();
			},
		});
	}
});

const uploadDialogRef = ref<InstanceType<typeof UploadDialog>>();
function handleImport() {
  uploadDialogRef.value?.open();
}
</script>

<template>
  <mar-wrapper v-model="state">
    <template #free-zone>
      <form-dialog-v2 :form-configs="formConfigs" ref="formDialogRef" />
      <UploadDialog ref="uploadDialogRef" :upload-api="'/sys/user/importTable'" :template-api="'/sys/user/importTemplate'"
        :after-upload="refresh" />
    </template>
    <template #toolbarExt>
      <n-button type="info" @click="handleImport">
        <div class="i-icon-park-outline-import-and-export w-4 h-4 mr-2"></div>
        {{ t("system.button.import_user") }}
      </n-button>
    </template>
  </mar-wrapper>
</template>
