<script setup lang="ts">
import { t } from "@/modules/i18n";
import { useRequest } from "@/index";

// 切换SQL日志开关
async function toggleSqlLog(enabled: boolean) {
  try {
    const status = enabled ? 1 : 0;
    const { error } = await useRequest(`/private/debug/sqlPrint/${status}`);
    if (!error.value) {
      window.$message.success(t('common.operationSuccess'));
    } else {
      window.$message.error(t('common.operationFailed'));
    }
  } catch (error) {
    console.error("切换SQL日志开关失败", error);
    window.$message.error(t('common.operationFailed'));
  }
}

// Hutool定时器相关
async function handleOpenAllTimer() {
  const { error } = await useRequest('/hToolTimer/startAllJob')
  if (!error.value) {
    window.$message.success(t('common.operationSuccess'));
  } else {
    window.$message.error(t('common.operationFailed'));
  }
}

async function handleCloseAllTimer() {
  const { error } = await useRequest('/hToolTimer/stopAllJob')
  if (!error.value) {
    window.$message.success(t('common.operationSuccess'));
  } else {
    window.$message.error(t('common.operationFailed'));
  }
}
</script>

<template>
  <NCard>
    <!-- SQL日志开关控制栏 -->
    <div class="flex flex-col space-y-4">
      <div class="flex items-center">
        <span class="text-gray-700 dark:text-gray-300 font-700 w-36">SQL日志：</span>
        <div class="flex space-x-2">
          <NButton type="primary" size="small" @click="toggleSqlLog(true)">
            开启
          </NButton>
          <NButton type="primary" size="small" @click="toggleSqlLog(false)">
            关闭
          </NButton>
        </div>
      </div>

      <div class="flex items-center">
        <span class="text-gray-700 dark:text-gray-300 font-700 w-36">Hutool定时器：</span>
        <div class="flex space-x-2">
          <NButton type="primary" size="small" @click="handleOpenAllTimer">
            开启所有定时器
          </NButton>
          <NButton type="primary" size="small" @click="handleCloseAllTimer">
            关闭所有定时器
          </NButton>
        </div>
      </div>
    </div>
  </NCard>
</template>