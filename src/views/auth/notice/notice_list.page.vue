<script setup lang="tsx">
import { t } from "@/modules/i18n";
import { <PERSON><PERSON><PERSON><PERSON>, createJsonRender, useTable, MarDialog, useDict, useRequest, useDelDialog } from "@/index";

const { data: roleList } = await useRequest<any[]>('/sys/role/list')
const { userDict, departmentDict } = useDict()

const ReceiverRender = (receiver: {
  all: boolean
  users: number[]
  roles: number[]
  depts: number[]
}) => {
  if (receiver.all) {
    return t("system.options.all")
  }

  return (
    <div>
      <section>
        <span class="text-lg font-bold">{t("system.options.user")}</span>
        {receiver.users.map(user => (
          <div key={user}>{userDict.value[user]?.name}</div>
        ))}
      </section>
      <section>
        <span class="text-lg font-bold">{t("system.options.role")}</span>
        {receiver.roles.map(role => (
          <div key={role}>{roleList.value?.find((item: any) => item.code === role)?.name}</div>
        ))}
      </section>
      <section>
        <span class="text-lg font-bold">{t("system.options.dept")}</span>
        {receiver.depts.map(dept => (
          <div key={dept}>{departmentDict.value[dept]?.name}</div>
        ))}
      </section>
    </div>
  )
}

const { state, refresh, on } = useTable(
  {
    page: '/sys/notice/page',
    info: '/sys/notice/info',
    importTable: '/sys/notice/importTable',
    exportTable: '/sys/notice/exportTable',
    importTemplate: '/sys/notice/importTemplate',
    save: '/sys/notice/save',
    update: '/sys/notice/update',
  },
  [
    {
      title: t("system.menu.title"),
      key: "title",
      query: "input",
      superQuery: "input",
    },
    {
      title: t("system.menu.content"),
      key: "content",
      query: "input",
      superQuery: "input",
    },
    {
      title: t("system.menu.firstRouter"),
      key: "firstRouter",
      query: 'input'
    },
    {
      title: t("system.menu.noticeParams"),
      key: "noticeParams",
      render: createJsonRender(t("system.menu.noticeParams"), "noticeParams"),
    },
    {
      title: t("system.menu.receiver"),
      key: "receiver",
      render: (row: Record<string, any>) => {
        return h(MarDialog, {
          label: t("system.menu.receiver"),
          title: t("system.menu.receiver"),
          buttonProps: {
            type: 'text',
            class: 'underline text-blue-500 p-0',
          }
        }, {
          default: () => h('div', {}, ReceiverRender(row.receiver))
        })
      }
    },
    {
      title: t("system.menu.userNum"),
      key: "userNum",
    },
    {
      title: t("system.menu.readNum"),
      key: "readNum",
    },
    {
      title: t("system.menu.noticeStatus"),
      key: "noticeStatus",
      query: 'select',
      options: [
        { label: t("system.options.read"), value: 1 },
        { label: t("system.options.unread"), value: 2 },
      ],
    },
    {
      title: t("system.menu.requestUrl"),
      key: "requestUrl",
      query: "input",
    },
    {
      title: t("system.menu.remark"),
      key: "remark",
    },
  ],
  {
    actionHideList: [ "edit", "save", "config", "deleteMul"],
    rowActions(row) {
      return [
        {
          label: t("system.button.send"),
          perm: 'sys:notice:send',
          click: async () => {
            const { error } = await useRequest(`/sys/notice/send/${row.id}`).post()
            if (error.value) return
            window.$message.success(t("common.operateSuccess"))
            refresh()
          }
        },
        {
          label: t("system.button.cancelNotice"),
          perm: 'sys:notice:cancelNotice',
          click: async () => {
            const { error } = await useRequest(`/sys/notice/cancelNotice/${row.id}`).post()
            if (error.value) return
            window.$message.success(t("common.operateSuccess"))
            refresh()
          }
        }
      ]
    },
  },
);

on(async (e, args) => {
  if (e === 'delete') {
    useDelDialog({
      api: '/sys/notice/deleteMul',
      params: [args.id],
      afterSuccess: () => {
        window.$message.success(t("common.operateSuccess"))
        refresh()
      }
    })
  }
})
</script>

<template>
  <MarWrapper v-model="state" />
</template>
