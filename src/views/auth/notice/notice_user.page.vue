<script setup lang="ts">
import { MarWrapper, useTable, useDict, useRequest } from "@/index";
import { t } from '@/modules/i18n'

const { userDict } = useDict()
const userOptions = computed(() => Object.values(userDict.value).map(item => ({
  label: item.name,
  value: item.id,
})))

const { state, refresh, on } = useTable({
  page: '/sys/noticeUser/page',
  exportTable: '/sys/noticeUser/exportTable',
  info: '/sys/noticeUser/info',
}, [
  {
    title: t("system.menu.title"),
    key: "title",
    query: "input",
    superQuery: "input",
  },
  {
    title: t("system.menu.content"),
    key: "content",
  },
  {
    title: t("system.menu.noticeId"),
    key: "noticeId",
  },
  {
    title: t("system.menu.noticeType"),
    key: "noticeType",
  },
  {
    title: t("system.menu.sender"),
    key: "sender",
    options: userOptions.value,
  },
  {
    title: t("system.menu.readStatus"),
    key: "readStatus",
    options: [
      {
        label: t("system.options.unread"),
        value: 0,
      },
      {
        label: t("system.options.read"),
        value: 1,
      },
    ]
  },
  {
    title: t("system.menu.readDate"),
    key: "readDate",
  },
  {
    title: t("system.menu.sendDate"),
    key: "sendDate",
  },
  {
    title: t("system.menu.receiver"),
    key: "receiver",
    options: userOptions.value,
  },
], {
  defaultSort: {
    columnKey: 'id',
    order: 'descend',
  }
})
</script>

<template>
  <MarWrapper v-model="state" />
</template>