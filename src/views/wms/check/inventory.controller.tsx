import {
  Over<PERSON>AP<PERSON>,
  Controller,
  OverrideColumn,
  HideButton,
  DefaultSort,
  OverrideSubTableAPI,
  DynTablePage,
  JsonRender,
  ActionBar,
  TableInstance, CompareEnum, DefaultSubTableSort, HideFirstColumnHerf,
  useDict,
  HideSubTableButton
} from '@/index'
import dayjs from "dayjs";
import { NSelect, NSwitch } from 'naive-ui';

/**
* 这是新的 "装饰器风格" 接口，更接近 Java 的注解
* 你想叫它装饰器还是叫注解都可以，我更喜欢叫 "装饰器"
*
* 装饰器模式的文件需要以 xxx.controller.tsx 的方式命名
*/

@OverrideAPI({ // 覆盖主表接口地址
  page: "/check/inventory/page",
  info: "/check/inventory/info",
  save: "/check/inventory/save",
  update: "/check/inventory/update",
  delete: "/check/inventory/deleteMul",
})
@OverrideSubTableAPI({
  page: "/check/inventoryDetail/page",
  info: "/check/inventoryDetail/info",
  save: "/check/inventoryDetail/save",
  update: "/check/inventoryDetail/update",
  delete: "/check/inventoryDetail/deleteMul",
})
@HideButton(["importTable"]) // 隐藏按钮
@HideSubTableButton(['save'])
@DefaultSort({
  columnKey: "createDate", // 默认排序字段
  order: "descend",
})
@DefaultSubTableSort({
  columnKey: "id",//子表默认排序字段
  order: "descend",
})
@HideFirstColumnHerf
@Controller('/wms/check/inventory')
class InventoryQueryPage extends DynTablePage {

  zoneOptions = ref<any[]>([])

  onBeforeMount(): void {
    this.request.get<any[]>('/sys/dict/type/dictTable/zone').then((res) => {
      this.zoneOptions.value = res.map((item: any) => ({
        label: item.name,
        value: item.code,
      })) ?? []
    })
  }

  // 获取时间范围的方法
  getTimeRange() {
    const format = 'YYYY-MM-DD'
    const currentDate = dayjs().add(1, 'day').format(format)
    const oneMonthAgo = dayjs().subtract(1, 'day').format(format)
    return [
      oneMonthAgo,
      currentDate
    ]
  }
  // 获取时间范围的方法
  getTimeRange2() {
    const format = 'YYYY-MM-DD'
    const currentDate = dayjs().add(1, 'day').format(format)
    const oneMonthAgo = '1994-03-28'
    return [
      oneMonthAgo,
      currentDate
    ]
  }

  /**
   * 追加工具栏上的操作按钮，类似的还有 _appendRowButton 追加行操作按钮，_appendBatchOperate 追加批量操作按钮
   *
   * 参数是 TableInstance 类型，可以访问到表格实例的属性和方法
   *
   * 追加方法和上面的重写方法不同，不需要装饰器，但是函数名是固定的，不能随便取
   */
  _appendToolbarButton({ refresh, checked }: TableInstance): ActionBar {
    return [
      // 选择异动库存
      {
        label: this.i18n.t('system.button.chooseChangedInventory'),
        icon: 'i-icon-park-outline-change',
        type: 'info',
        // perm: "check:inventory:generateFromInventoryChange",
        onClick: async () => {
          const {sysDict} = useDict();
          const extForm = reactive({
            blindCheckMode: false,
            autoCloseEnabled: false,
          })

          const {close} = this.dialog.dynamicTableDialog({

            dynCode: 'inventory_change',
            bottomSlot: () => <div class="flex gap-4 mt-4 border-solid border border-gray-100 dark:border-gray-700 p-4">
              <div class="flex items-center gap-2">
                <div>暗盘模式</div>
                <NSwitch size='small' v-model:value={extForm.blindCheckMode}></NSwitch>
              </div>
              <div class="flex items-center gap-2">
                <div>盘点无差异自动关闭</div>
                <NSwitch size='small' v-model:value={extForm.autoCloseEnabled}></NSwitch>
              </div>
            </div>,
            options: {
              pageSizeOptions: [50, 100, 200, 500, 1000],
              defaultPageSize: 1000,
              extQuery: [
                {
                  title: "异动发生日期范围",
                  key:  "createDate",
                  query: 'date-range' ,
                  defaultQueryValue: this.getTimeRange() as any
                },
                {
                  title: "动碰维度",
                  key: "groupType",
                  query: 'select',
                  defaultQueryValue: "containerCode",
                  options: [
                    {
                      label: this.i18n.t('system.header.containerCode'),
                      value: "containerCode"
                    },
                    // {
                    //   label: this.i18n.t('system.header.materialCode'),
                    //   value: 'materialCode'
                    // },
                  ]

                }
              ],
              api: {
                page: '/inventory/change/page'
              },
              defaultQueryValue: {
                hasInventory: 1
              },
              otherOptions: {
                beforeRequest: (form) => {
                  return this.utils.compactTableQuery(form, {
                    hasInventory: {
                      op: CompareEnum.EQUAL,
                      opval: 1
                    }
                  })
                }
              }
            },
            handleSubmit: (dialogChecked: any[],tableRef?:any) => {
              console.log('handleSubmit', checked)
              this.request.post('/check/inventory/generateFromInventoryChange', {
                body: {
                  inventoryChangeList: dialogChecked,
                  config: extForm,
                  param: tableRef?.queryWrapper()
                }
              })
                  .then(() => {
                    refresh()
                    close()
                  })
            }
          })
        }
      },
      // 选择即时库存
      {
        label: this.i18n.t('system.button.chooseInstantInventory'),
        icon: 'i-icon-park-outline-database-lock',
        type: 'info',
        onClick: async () => {
          const {sysDict} = useDict();
          const extForm = reactive({
            blindCheckMode: false,
            autoCloseEnabled: false,
          })

          const {close} = this.dialog.dynamicTableDialog({
            dynCode: 'v_inventory_for_check',
            bottomSlot: () => <div class="flex gap-4 mt-4 border-solid border border-gray-100 dark:border-gray-700 p-4">
              <div class="flex items-center gap-2">
                <div>暗盘模式</div>
                <NSwitch size='small' v-model:value={extForm.blindCheckMode}></NSwitch>
              </div>
              <div class="flex items-center gap-2">
                <div>盘点无差异自动关闭</div>
                <NSwitch size='small' v-model:value={extForm.autoCloseEnabled}></NSwitch>
              </div>
            </div>,
            options: {
              pageSizeOptions: [50, 100, 200, 500, 1000],
              defaultPageSize: 1000,
              api: {
                page: '/inventoryForCheck/page'
              },
              defaultQueryValue: {
                // countDate: this.getTimeRange()
              },
              extQuery: [
                {
                  title: '抽盘比例',
                  query: "input",
                  key: "sampleRate",
                  defaultQueryValue: 100,
                  suffix: "%"
                },

              ]
            },
            handleSubmit: (dialogChecked: any[],tableRef?:any) => {
              console.log('handleSubmit', checked,tableRef?.queryWrapper())
              this.request.post('/check/inventory/generateFromInventory', {
                body: {
                  inventoryList: dialogChecked,
                  config: extForm,
                  param: tableRef?.queryWrapper()
                }
              })
                  .then(() => {
                    refresh()
                    close()
                  })
            }
          })
        }
      },
      {
        label: this.i18n.t("system.button.createCheckOutbound"),
        icon: "i-heroicons-outline-document-plus",
        type: "primary",
        onClick: () => {
          if (checked.value.length !== 1) {
            window.$message.error(this.i18n.t("common.prompt.mustSelectedOne"));
            return;
          }
          // 判断是否需要显示弹窗,检查是否所有选中项的 zoneType 都为 平库 MANUAL=0
          const allManualZoneType = checked.value.every(item => item.zoneType === 0);
          if (allManualZoneType) {
            // 直接调用提交逻辑
            this.request.post('/check/inventory/execute', {
              body: {
                exitList: [],  // 因为不需要选择，所以传空数组
                checkList: checked.value
              }
            }).then(() => {
              window.$message.success(this.i18n.t("common.operationSuccess"));
              refresh();
            });
            return;
          }

          const { close } = this.dialog.dynamicTableDialog({
            dynCode: 'base_pick',
            options: {
              otherOptions: {
                beforeRequest: (form) => {
                  return this.utils.compactTableQuery(form, {
                    pickType: {
                      op: CompareEnum.EQUAL,
                      opval: 1
                    },
                    status: {
                      op: CompareEnum.EQUAL,
                      opval: 1
                    },
                    useStatus: {
                      op: CompareEnum.EQUAL,
                      opval: 0
                    }
                  })
                }
              }
            },

            handleSubmit: (dialogChecked: any[]) => {
              if (dialogChecked.length == 0) {
                window.$message.error(this.i18n.t("common.prompt.needSelectedOne"));
                return;
              }
              console.log('handleSubmit', checked)
              this.request.post('/check/inventory/execute', {
                body: {
                  exitList: dialogChecked,
                  checkList: checked.value
                }
              })
                  .then(() => {
                    window.$message.success(this.i18n.t("common.operationSuccess"));
                    refresh()
                    close()
                  })
            }
          })
        }
      },
      {
        label: "差异复盘",
        icon: "i-heroicons-outline-document-plus",
        type: "primary",
        onClick: () => {
          if (checked.value.length === 0) {
            window.$message.error(this.i18n.t("common.prompt.needSelectedOne"));
            return;
          }
          // 判断是否需要显示弹窗,检查是否所有选中项的 zoneType 都为 平库 MANUAL=0
          const allManualZoneType = checked.value.every(item => item.zoneType === 0);
          if (allManualZoneType) {
            // 直接调用提交逻辑
            this.request.post('/check/inventory/executeDifferenceAgain', {
              body: {
                exitList: [],  // 因为不需要选择，所以传空数组
                checkList: checked.value
              }
            }).then(() => {
              window.$message.success(this.i18n.t("common.operationSuccess"));
              refresh();
            });
            return;
          }
          const { close } = this.dialog.dynamicTableDialog({
            dynCode: 'base_pick',
            options: {
              otherOptions: {
                beforeRequest: (form) => {
                  return this.utils.compactTableQuery(form, {
                    pickType: {
                      op: CompareEnum.EQUAL,
                      opval: 1
                    },
                    status: {
                      op: CompareEnum.EQUAL,
                      opval: 1
                    },
                    useStatus: {
                      op: CompareEnum.EQUAL,
                      opval: 0
                    }
                  })
                }
              }
            },
            handleSubmit: (dialogChecked: any[]) => {
              if (dialogChecked.length == 0) {
                window.$message.error(this.i18n.t("common.prompt.needSelectedOne"));
                return;
              }
              console.log('handleSubmit', checked)
              this.request.post('/check/inventory/executeDifferenceAgain', {
                body: {
                  exitList: dialogChecked,
                  checkList: checked.value
                }
              })
                  .then(() => {
                    window.$message.success(this.i18n.t("common.operationSuccess"));
                    refresh()
                    close()
                  })
            }
          })
        }
      },
      {
        label: "整单复盘",
        icon: "i-heroicons-outline-document-plus",
        type: "primary",
        onClick: () => {
          if (checked.value.length === 0) {
            window.$message.error(this.i18n.t("common.prompt.needSelectedOne"));
            return;
          }
          // 判断是否需要显示弹窗,检查是否所有选中项的 zoneType 都为 平库 MANUAL=0
          const allManualZoneType = checked.value.every(item => item.zoneType === 0);
          if (allManualZoneType) {
            // 直接调用提交逻辑
            this.request.post('/check/inventory/executeAllAgain', {
              body: {
                exitList: [],  // 因为不需要选择，所以传空数组
                checkList: checked.value
              }
            }).then(() => {
              window.$message.success(this.i18n.t("common.operationSuccess"));
              refresh();
            });
            return;
          }
          const { close } = this.dialog.dynamicTableDialog({
            dynCode: 'base_pick',
            options: {
              otherOptions: {
                beforeRequest: (form) => {
                  return this.utils.compactTableQuery(form, {
                    pickType: {
                      op: CompareEnum.EQUAL,
                      opval: 1
                    },
                    status: {
                      op: CompareEnum.EQUAL,
                      opval: 1
                    },
                    useStatus: {
                      op: CompareEnum.EQUAL,
                      opval: 0
                    }
                  })
                }
              }
            },
            handleSubmit: (dialogChecked: any[]) => {
              if (dialogChecked.length == 0) {
                window.$message.error(this.i18n.t("common.prompt.needSelectedOne"));
                return;
              }
              console.log('handleSubmit', checked)
              this.request.post('/check/inventory/executeDifferenceAgain', {
                body: {
                  exitList: dialogChecked,
                  checkList: checked.value
                }
              })
                  .then(() => {
                    window.$message.success(this.i18n.t("common.operationSuccess"));
                    refresh()
                    close()
                  })
            }
          })
        }
      },
      {
        label: this.i18n.t("system.button.generateDiffs"),
        icon: "i-heroicons-outline-document-plus",
        type: "error",
        onClick: () => {
          if (checked.value.length === 0) {
            window.$message.error(this.i18n.t("common.prompt.needSelectedOne"));
            return;
          }
          const map = checked.value.map((item) => item.id);
          this.request.post('/check/inventory/close', {
            body: map
          })
          .then(() => {
            window.$message.success(this.i18n.t("common.operationSuccess"));
            refresh()
          })
        },
      },
    ]
  }
}

export {
  InventoryQueryPage
}
