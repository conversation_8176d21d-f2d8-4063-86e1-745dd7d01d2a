import {
  <PERSON><PERSON><PERSON><PERSON>,
  Controller,
  BindF<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  OverrideFormOptions,
  OverrideSubFormOptions,
} from '@/decorator';
import { BatchOperate, DynFormPage, TableInstance } from '@/components';
import { DynTablePage } from '@/components';
import { h, defineComponent, type Component } from 'vue'

@OverrideAPI({
  page: "/base/pack/page",
  info: "/base/pack/info",
  save: "/base/pack/save",
  update: "/base/pack/update",
  delete: "/base/pack/deleteMul",
})
@BindForm('/wms/base/pack/form')
@Controller('/wms/base/pack')
class BasePackPage extends DynTablePage {
  _appendBatchOperate(instance: TableInstance): BatchOperate {
    return [
      {
        label: 'test',
        key: 'test',
        props: {
          type: 'button',
          onClick: () => {
            this.getChecked()
          }
        }
      }
    ]
  }
}

@Controller('/wms/base/pack/form')
class BasePackFormPage extends DynFormPage {

  @OverrideSubFormOptions('status')
  statusOptions() {
    console.log('statusOptions')
    return [
      {
        label: 'test',
        value: 'test',
      }
    ]
  }

  _appendForm(): Component {
    return h('div', {}, '123')
  }

}

export {
  BasePackPage,
  BasePackFormPage,
}