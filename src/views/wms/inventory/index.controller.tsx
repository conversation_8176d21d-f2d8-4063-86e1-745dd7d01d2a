import {
    <PERSON><PERSON><PERSON><PERSON>,
    Controller,
    OverrideColumn,
    HideButton,
    DefaultSort,
    DynTablePage,
    JsonRender,
    ActionBar,
    TableInstance,
    TableDictSelect,
    APIDictSelect
} from '@/index';
import {
    NInput,
    NInputNumber,
    NForm,
    NFormItem,
    FormInst,
    NTag,
    NSpace,
    NDivider,
    NEmpty,
    NButton,
    NCard,
    NDataTable,
    DataTableCreateSummary
} from 'naive-ui';
import {SummaryRowData} from 'naive-ui/es/data-table/src/interface';

/**
 * 这是新的 "装饰器风格" 接口，更接近 Java 的注解
 * 你想叫它装饰器还是叫注解都可以，我更喜欢叫 "装饰器"
 *
 * 装饰器模式的文件需要以 xxx.controller.tsx 的方式命名
 */

@OverrideAPI({ // 覆盖主表接口地址
    page: "/inventoryAvailable/page",
    info: "/inventoryAvailable/info",
    update: "/inventory/update",
})
@HideButton(["importTable", "save", "delete", "deleteMul", "edit"]) // 隐藏按钮
@DefaultSort({
    columnKey: "lot", // 默认排序字段
    order: "ascend",
})
@Controller('/wms/inventory/query') // controller 注册路由，现在不需要按照文件注册路由了，直接用controller注册
class InventoryQueryPage extends DynTablePage {

    /**
     * 首先你需要定义一个 类，类名随意，但是要继承 DynTablePage 类
     *
     * 类里面有一些全局属性，可以通过 this.xxx 访问
     * 比如 this.i18n.t 是国际化方法，this.router 是路由方法
     */

    @OverrideColumn('lotJson') // 重写主表的warehouse列渲染方式
    renderLotJson(row) { // 函数名可以随便取
        return <JsonRender json={row.lotJson}/>
    }

    @OverrideColumn('lotJson.color')
    renderColor(row) {
        console.log(row)
        return <div>{row.lotJson.color}</div>
    }

    checkedCache = ref<any[]>([])

    private includeThisRow(row: any) {
        return this.checkedCache.value.some(item => item.id === row.id)
    }

    _createSummary(pageData: any[]): SummaryRowData | SummaryRowData[] {
        return {
            containerCode: {
                value: '总计:'
            },
            actQty: {
                value: pageData.reduce((acc, curr) => acc + (curr.actQty || 0), 0)
            },
            avaQty: {
                value: pageData.reduce((acc, curr) => acc + (curr.avaQty || 0), 0)
            },
            disQty: {
                value: pageData.reduce((acc, curr) => acc + (curr.disQty || 0), 0)
            }
        }
    }

    _rowProps(rowData: any, rowIndex: number): Record<string, any> {
        if (rowData.isHold === 1) {
            return {
                class: 'bg-rose-50! dark:bg-rose-950!'
            }
        }
        return {}
    }

    _appendRowButton(row: any): any[] {
        return [
            {
                label: this.i18n.t(computed(() => {
                    return this.includeThisRow(row) ? 'system.button.removeFromMergePallet' : 'system.button.addToMergePallet'
                }).value),
                click: () => {
                    if (this.includeThisRow(row)) {
                        this.checkedCache.value = this.checkedCache.value.filter(item => item.id !== row.id)
                        return
                    }
                    this.checkedCache.value.push(row)
                }
            }
        ]
    }

    /**
     * 追加工具栏上的操作按钮，类似的还有 _appendRowButton 追加行操作按钮，_appendBatchOperate 追加批量操作按钮
     *
     * 参数是 TableInstance 类型，可以访问到表格实例的属性和方法
     *
     * 追加方法和上面的重写方法不同，不需要装饰器，但是函数名是固定的，不能随便取
     */
    _appendToolbarButton({refresh, checked}: TableInstance): ActionBar {
        return [
            {
                // label: this.i18n.t('system.button.handleQuery'),
                label: "冻结",
                icon: 'i-icon-park-outline-change',
                type: 'info',
                onClick: () => {
                    if (checked.value.length > 1 || checked.value.length === 0) {
                        // message 自带国际化
                        this.message.error('system.tips.pleaseSelectOne')
                        return
                    }
                    const formRef = ref<FormInst | null>(null)

                    const form = ref({
                        remark: "",
                        id: checked.value[0].id
                    })
                    // 使用 this.dialog.create 创建弹窗
                    const {close} = this.dialog.create({

                        // // 弹窗内容，这里使用的是 jsx 语法，类似于 Vue 的模板
                        // content: () => (
                        //     <NForm ref={formRef} model={form.value}>
                        //       <NFormItem>
                        //         <NInput v-model={form.value.remark}/>
                        //       </NFormItem>
                        //     </NForm>
                        // ),

                        content: () =>
                            h(NInput, {
                                value: form.value.remark,
                                placeholder: '请输入冻结原因',
                                onUpdateValue(v) {
                                    form.value.remark = v;
                                    console.log(form.value)
                                },
                            }),

                        handleSubmit: () => {
                            this.request.post('/inventory/handleFreezeById', {
                                body: form.value
                            }).then(() => {
                                this.message.success('system.tips.operationSuccess')
                                refresh()
                                close()
                            })
                        }
                    })
                }
            },
            {
                // label: this.i18n.t('system.button.handleQuery'),
                label: "解冻",
                icon: 'i-icon-park-outline-change',
                type: 'info',
                onClick: () => {
                    if (checked.value.length > 1 || checked.value.length === 0) {
                        // message 自带国际化
                        this.message.error('system.tips.pleaseSelectOne')
                        return
                    }
                    const id = checked.value[0].id;
                    this.request.post('/inventory/handleUnfreezeById', {
                        query: {
                            id
                        }
                    })
                        .then(() => {
                            refresh()
                            close()
                        })
                }
            },
            {
                label: this.i18n.t('system.button.mergePallets'),
                icon: 'i-icon-park-outline-merge-cells',
                type: 'info',
                countSource: this.checkedCache,
                onClick: () => {
                    if (this.checkedCache.value.length === 0) {
                        this.message.error('system.tips.pleaseAtLeastSelectOne')
                        return
                    }

                    const pickCode = ref('')
                    const {close} = this.dialog.create({
                        style: {
                            width: '1100px'
                        },
                        content: () => {
                            const columns = [
                                {
                                    title: this.i18n.t('system.header.materialCode'),
                                    key: 'materialCode',
                                    width: 100,
                                    render: (row) => row.materialCode
                                },
                                {
                                    title: this.i18n.t('system.header.materialName'),
                                    key: 'materialName',
                                    width: 100,
                                    render: (row) => row.materialName
                                },
                                {
                                    title: this.i18n.t('system.header.containerCode'),
                                    key: 'containerCode',
                                    width: 100
                                },
                                {
                                    title: this.i18n.t('system.header.lot'),
                                    key: 'lot',
                                    width: 100
                                },
                                {
                                    title: this.i18n.t('system.header.actQty'),
                                    key: 'actQty',
                                    width: 80
                                },
                                {
                                    title: this.i18n.t('system.header.unitCode'),
                                    key: 'unitCode',
                                    width: 80
                                },
                                {
                                    title: this.i18n.t('system.header.locationCode'),
                                    key: 'locationCode',
                                    width: 100
                                },
                                {
                                    title: this.i18n.t('system.header.warehouseCode'),
                                    key: 'warehouseCode',
                                    width: 100
                                },
                                {
                                    title: this.i18n.t('system.header.zoneCode'),
                                    key: 'zoneCode',
                                    width: 80
                                },
                                {
                                    title: this.i18n.t('system.header.operation'),
                                    key: 'actions',
                                    width: 80,
                                    render: (row) => {
                                        return (
                                            <NButton
                                                size="small"
                                                type="error"
                                                text
                                                onClick={() => {
                                                    this.checkedCache.value = this.checkedCache.value.filter(i => i.id !== row.id)
                                                }}
                                            >
                                                {this.i18n.t('common.delete')}
                                            </NButton>
                                        )
                                    }
                                }
                            ]


                            return (
                                <NCard class="w-1000px">
                                    <div class="flex justify-between items-center mb-3">
                                        <div
                                            class="text-lg font-medium">{this.i18n.t('system.button.mergePallets')}</div>
                                        <NButton
                                            size="small"
                                            type="error"
                                            text
                                            onClick={() => this.checkedCache.value = []}
                                            disabled={this.checkedCache.value.length === 0}
                                        >
                                            {this.i18n.t('system.button.clearAll')}
                                        </NButton>
                                    </div>

                                    <div class="mb-4">
                                        <div class="mb-1">{this.i18n.t('system.tips.pleaseSelectPick')}</div>
                                        <APIDictSelect
                                            url="/base/pick/getPick"
                                            modelValue={pickCode.value}
                                            onUpdate:modelValue={(val) => {
                                                pickCode.value = val
                                            }}
                                            NaiveSelectProps={{
                                                size: 'small',
                                                style: 'width: 100%'
                                            }}
                                            afterRequest={(data) => {
                                                return data.map(item => ({
                                                    label: item.name,
                                                    value: item.code
                                                })) || []
                                            }}
                                        />
                                        {/* <TableDictSelect
                                            tableName="pick"
                                            modelValue={pickCode.value}
                                            onUpdate:modelValue={(val) => {
                                                pickCode.value = val
                                            }}
                                            NaiveSelectProps={{
                                                size: 'small',
                                                style: 'width: 100%'
                                            }}
                                        /> */}
                                    </div>

                                    <NDivider/>
                                    {this.checkedCache.value.length === 0 ? (
                                        <NEmpty description={this.i18n.t('system.tips.noData')}/>
                                    ) : (
                                        <NDataTable
                                            columns={columns}
                                            data={this.checkedCache.value}
                                            bordered={false}
                                            striped
                                            size="small"
                                            maxHeight={400}
                                            scrollX={600}
                                        />
                                    )}
                                </NCard>
                            )
                        },
                        handleSubmit: () => {
                            if (this.checkedCache.value.length === 0) {
                                this.message.error('system.tips.pleaseAtLeastSelectOne')
                                return
                            }

                            if (!pickCode.value) {
                                this.message.error('system.tips.pleaseSelectPick')
                                return
                            }

                            this.request.post('/task/mould', {
                                body: {
                                    pickCode: pickCode.value,
                                    oprList: this.checkedCache.value
                                }
                            }).then(() => {
                                this.message.success('system.tips.operationSuccess')
                                refresh()
                                this.checkedCache.value = []
                                close()
                            })
                        }
                    })
                }
            },
            {
                label: this.i18n.t('system.button.splitPallet'),
                icon: 'i-icon-park-outline-split-cells',
                type: 'info',

                // 点击事件
                onClick: () => {
                    if (checked.value.length > 1 || checked.value.length === 0) {
                        // message 自带国际化
                        this.message.error('system.tips.pleaseSelectOne')
                        return
                    }
                    const form = ref({
                        pickCode: '',
                        oprList: [{
                            ...checked.value[0],
                            qty: checked.value[0].qty === null ? 0 : checked.value[0].qty,
                        }]
                    })

                    const formRef = ref<FormInst | null>(null)

                    const rules = {
                        'oprList.0.qty': [
                            {
                                required: true,
                                message: this.i18n.t('system.tips.required'),
                                trigger: ['input', 'blur']
                            }
                        ],
                        pickCode: [
                            {
                                required: true,
                                message: this.i18n.t('system.tips.required'),
                                trigger: ['input', 'blur']
                            }
                        ]
                    }

                    // 使用 this.dialog.create 创建弹窗
                    const {close} = this.dialog.create({

                        // 弹窗内容，这里使用的是 jsx 语法，类似于 Vue 的模板
                        content: () => (
                            <NForm ref={formRef} model={form.value} rules={rules}>
                                <NFormItem path="oprList[0].qty" label={this.i18n.t('system.tips.pleaseInputNumber')}>
                                    <NInputNumber min={0} v-model:value={form.value.oprList[0].qty} size="small"/>
                                </NFormItem>
                                <NFormItem path="pickCode" label={this.i18n.t('system.tips.pleaseSelectPick')}>
                                    <APIDictSelect
                                        url="/base/pick/getPick"
                                        modelValue={form.value.pickCode}
                                        onUpdate:modelValue={(val) => {
                                            form.value.pickCode = val
                                        }}
                                        NaiveSelectProps={{
                                            size: 'small',
                                            style: 'width: 100%'
                                        }}
                                        afterRequest={(data) => {
                                            return data.map(item => ({
                                                label: item.name,
                                                value: item.code
                                            })) || []
                                        }}
                                    />
                                </NFormItem>
                            </NForm>
                        ),

                        handleSubmit: () => {
                            formRef.value?.validate()
                                .then(() => {
                                    this.request.post('/task/devanning', {
                                        body: form.value
                                    }).then(() => {
                                        this.message.success('system.tips.operationSuccess')
                                        refresh()
                                        close()
                                    })
                                })
                                .catch(() => {
                                    this.message.error('system.tips.validationFailed')
                                })
                        }
                    })

                }
            }

        ]
    }

}

export {
    InventoryQueryPage
}
