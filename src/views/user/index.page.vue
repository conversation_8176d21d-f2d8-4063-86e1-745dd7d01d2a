<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useAuth } from '@/hooks'
import { useRequest } from '@/hooks/useRequest'

const { t } = useI18n()
const { userInfo, logout } = useAuth()

const formRef = ref()
const formValue = ref({
  password: '',
  newPassword: '',
  confirmPassword: '',
})

const rules = {
  password: {
    required: true,
    message: t('pages.user.passwordRuleTip'),
    trigger: 'blur',
  },
  newPassword: {
    required: true,
    message: t('pages.user.newPasswordRuleTip'),
    trigger: ['input', 'blur'],
  },
  confirmPassword: {
    required: true,
    message: t('pages.user.confirmPasswordRuleTip'),
    trigger: ['input', 'blur'],
    validator: (rule: any, value: string) => {
      if (value !== formValue.value.newPassword)
        return new Error(t('pages.user.passwordNotMatch'))
      return true
    },
  },
}

async function handleValidateClick() {
  try {
    await formRef.value?.validate()
    const { error } = await useRequest('/sys/user/password')
      .post({
        password: formValue.value.password,
        newPassword: formValue.value.newPassword,
        confirmPassword: formValue.value.confirmPassword,
      })

    if (!error.value) {
      window.$message.success(t('pages.user.passwordSuccess'))
      logout()
    }
  }
  catch {
    window.$message.error(t('pages.user.formValidateError'))
  }
}
</script>

<template>
  <n-space vertical>
    <n-card title="">
      <n-space size="large">
        <n-descriptions label-placement="left" :column="1" :title="$t('pages.user.title')">
          <n-descriptions-item label="id">
            {{ userInfo?.id }}
          </n-descriptions-item>
          <n-descriptions-item :label="$t('pages.user.username')">
            {{ userInfo?.username }}
          </n-descriptions-item>
          <n-descriptions-item :label="$t('pages.user.realName')">
            {{ userInfo?.realName }}
          </n-descriptions-item>
        </n-descriptions>
      </n-space>
    </n-card>
    <n-card :title="$t('pages.user.updateInfo')">
      <n-space justify="center">
        <n-form ref="formRef" class="w-500px" :label-width="80" :model="formValue" :rules="rules">
          <n-form-item :label="$t('pages.user.password')" path="password">
            <n-input v-model:value="formValue.password" type="password" :placeholder="$t('pages.user.passwordRuleTip')" />
          </n-form-item>
          <n-form-item :label="$t('pages.user.newPassword')" path="newPassword">
            <n-input v-model:value="formValue.newPassword" type="password" :placeholder="$t('pages.user.newPasswordRuleTip')" />
          </n-form-item>
          <n-form-item :label="$t('pages.user.confirmPassword')" path="confirmPassword">
            <n-input v-model:value="formValue.confirmPassword" type="password" :placeholder="$t('pages.user.confirmPasswordRuleTip')" />
          </n-form-item>
          <n-form-item>
            <n-button type="primary" attr-type="button" block @click="handleValidateClick">
              {{ $t('pages.user.submit') }}
            </n-button>
          </n-form-item>
        </n-form>
      </n-space>
    </n-card>
  </n-space>
</template>
