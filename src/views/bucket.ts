import type { Component } from 'vue'

export type Bucket = Record<string, Component>

// Use more specific type instead of any
const vueModules = import.meta.glob<Component>('./**/*.page.vue', {
  eager: true,
  import: 'default'
})

// Extract path processing logic
const processPagePath = (path: string): string => 
  path
    .replace('/index.page.vue', '')
    .replace('.page.vue', '')
    .slice(1) // Remove leading '.'

const vuePages = Object.entries(vueModules).reduce<Bucket>((acc, [path, component]) => {
  acc[processPagePath(path)] = component
  return acc
}, {})

export const pageBucket: Bucket = vuePages