<script setup lang="ts">
import type { FormInst } from 'naive-ui'
import { useAuth, useSystem } from '@/hooks'
import { local } from '@/utils'
import { systemConfig } from '@/config'
import { onKeyStroke } from '@vueuse/core'

const authStore = useAuth()

const { t } = useI18n()
const rules = computed(() => {
  return {
    username: {
      required: true,
      trigger: 'blur',
      message: t('login.accountRuleTip'),
    },
    password: {
      required: true,
      trigger: 'blur',
      message: t('login.passwordRuleTip'),
    },
  }
})
const formValue = ref({
  username: '',
  password: '',
  firstRouter: '',
})
const isRemember = ref(false)
const isLoading = ref(false)

const formRef = ref<FormInst | null>(null)
async function handleLogin() {
  if (!system.value)
    return

  formValue.value.firstRouter = system.value
  formRef.value?.validate(async (errors) => {
    if (errors)
      return

    isLoading.value = true
    const { username, password } = formValue.value

    const encryptForm = {
      username,
      password,
    }
    if (isRemember.value)
      local.set('loginAccount', encryptForm)
    else
      local.remove('loginAccount')

    await authStore.login(formValue.value)
    isLoading.value = false
  })
}
onMounted(() => {
  checkUserAccount()
})
function checkUserAccount() {
  const loginAccount = local.get('loginAccount')
  if (!loginAccount)
    return

  formValue.value.username = loginAccount.username
  formValue.value.password = loginAccount.password
  isRemember.value = true
}

const { currentSystem: system } = useSystem()
const systemList = systemConfig?.list
  ?.filter(item => item.homePath)
  .map((item) => {
    return {
      label: item.key,
      value: item.key,
    }
  })

onKeyStroke('Enter', () => {
  handleLogin()
}, {
  target: document,
})
</script>

<template>
  <div class="flex flex-col items-center justify-center mt-40px px-40px relative">
    <div class="flex items-center justify-center">
      <img src="/imgs/logo-text.png" class="h-40px mb-10px">
    </div>
    <n-form ref="formRef" :rules="rules" :model="formValue" :show-label="false" size="large" class="w-full">
      <n-form-item path="username">
        <n-input v-model:value="formValue.username" clearable :placeholder="$t('login.accountPlaceholder')"
          class="h-45px" />
      </n-form-item>
      <n-form-item path="password">
        <n-input v-model:value="formValue.password" type="password" :placeholder="$t('login.passwordPlaceholder')"
          clearable show-password-on="click" class="h-45px">
          <template #password-invisible-icon>
            <div class="i-icon-park-outline-preview-close-one h-1.2rem w-1.2rem" />
          </template>
          <template #password-visible-icon>
            <div class="i-icon-park-outline-preview-open h-1.2rem w-1.2rem" />
          </template>
        </n-input>
      </n-form-item>
      <n-space vertical :size="24">
        <div class="flex-y-center justify-between">
          <n-checkbox v-model:checked="isRemember">
            {{ $t('login.rememberMe') }}
          </n-checkbox>
          <n-select v-model:value="system" :options="systemList" class="w-160px" />
        </div>
        <n-button block type="primary" size="large" :loading="isLoading" :disabled="isLoading" class="h-45px text-16px"
          @click="handleLogin">
          {{ $t('login.signIn') }}
        </n-button>
      </n-space>
    </n-form>
  </div>
</template>
