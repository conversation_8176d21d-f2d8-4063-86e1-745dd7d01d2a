<script setup lang="ts">
import LoginCard from './components/LoginCard.vue'
</script>

<template>
  <n-el
    class="wh-full flex-center relative bg-gradient-to-rb from-[#f0f5ff] to-[#FFDDD7] dark:from-[#101014] dark:to-[#1a1a1a]">
    <div class="absolute inset-0 overflow-hidden">
      <div
        class="dark:invisible absolute -right-10vw -top-10vh w-50vw h-50vh rounded-full opacity-10 bg-gradient-to-rb from-[#D61A4C] to-[#FFA2B8] dark:from-[#ff4d4f] dark:to-[#ff7875]" />
      <div
        class="dark:invisible absolute -left-10vw -bottom-10vh w-50vw h-50vh rounded-full opacity-10 bg-gradient-to-rb from-[#D61A4C] to-[#FFA2B8] dark:from-[#ff4d4f] dark:to-[#ff7875]" />
    </div>
    <div class="fixed top-40px right-40px text-lg">
      <DarkModeSwitch />
      <LangsSwitch />
    </div>
    <n-el
      class="h-full w-full max-w-1000px max-h-500px rounded-lg overflow-hidden transition-all duration-300 backdrop-blur-sm shadow-lg bg-white/80 dark:bg-dark/80">
      <div class="w-full login-card flex items-center justify-center h-full relative">
        <div class="relative w-50% h-full">
          <div class="absolute inset-0 bg-gradient-to-r from-transparent light:to-white/90 z-10" />
          <img src="/imgs/bg.png" class="w-full h-full object-cover opacity-90 dark:opacity-70">
        </div>
        <LoginCard class="w-50%" />
      </div>
    </n-el>
  </n-el>
</template>
