import type { Component } from 'vue'
import type { Core } from '@/core/types'

// Define interface for controller classes
export type IController = {
  view: Component
}

// Use more specific type
const controllerBucket = new Map<string, new (core: Core) => IController>()

/**
 * Decorator for registering controller components.
 * @param path - The route path for the controller
 * @returns Class decorator function
 */
function Controller(path: string) {
  return function (target: new (core: Core) => IController) {
    controllerBucket.set(path, target)
  }
}

/**
 * Decorator for registering query controller components.
 * @param path - The route path for the controller
 * @returns Class decorator function
 */
function QueryController(path: string) {
  return function (target: new (core: Core) => IController) {
    Reflect.defineMetadata('Reflect.QueryController', true, target)
    controllerBucket.set(path, target)
  }
}

export {
  Controller,
  controllerBucket,
  QueryController
}