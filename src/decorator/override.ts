import 'reflect-metadata'

function OverrideAPI(options: {
  page?: string
  importTable?: string
  exportTable?: string
  importTemplate?: string
  delete?: string
  save?: string
  info?: string
  update?: string
}) {
  return function (target: any) {
    Reflect.defineMetadata('Reflect.OverrideAPI', options, target)
  }
}

function OverrideSubTableAPI(options: {
  page?: string
  importTable?: string
  exportTable?: string
  importTemplate?: string
  delete?: string
  save?: string
  info?: string
  update?: string
}) {
  return function (target: any) {
    Reflect.defineMetadata('Reflect.OverrideSubTableAPI', options, target)
  }
}

function OverrideColumn(columnKey: string) {
  return function (target: any, propertyKey: string) {
    const classConstructor = target.constructor;
    let overridesColumnMap: Record<string, string> =
      Reflect.getMetadata('Reflect.OverrideColumn', classConstructor);
    if (!overridesColumnMap) {
      overridesColumnMap = {};
    }
    overridesColumnMap[columnKey] = propertyKey;
    Reflect.defineMetadata('Reflect.OverrideColumn', overridesColumnMap, classConstructor);
  }
}

function OverrideSubTableColumn(columnKey: string) {
  return function (target: any, propertyKey: string) {
    const classConstructor = target.constructor;
    let overridesColumnMap: Record<string, string> =
      Reflect.getMetadata('Reflect.OverrideSubTableColumn', classConstructor);
    if (!overridesColumnMap) {
      overridesColumnMap = {};
    }
    overridesColumnMap[columnKey] = propertyKey;
    Reflect.defineMetadata('Reflect.OverrideSubTableColumn', overridesColumnMap, classConstructor);
  }
}

function OverrideFormOptions(columnKey: string) {
  return function (target: any, propertyKey: string) {
    const classConstructor = target.constructor;
    let overridesFormOptionsMap: Record<string, string> =
      Reflect.getMetadata('Reflect.OverrideFormOptions', classConstructor);
    
    if (!overridesFormOptionsMap) {
      overridesFormOptionsMap = {};
    }
    overridesFormOptionsMap[columnKey] = propertyKey;
    Reflect.defineMetadata('Reflect.OverrideFormOptions', overridesFormOptionsMap, classConstructor);
  }
}

function OverrideSubFormOptions(columnKey: string) {
  return function (target: any, propertyKey: string) {
    const classConstructor = target.constructor;
    let overridesSubFormOptionsMap: Record<string, string> =
      Reflect.getMetadata('Reflect.OverrideSubFormOptions', classConstructor);
    
    if (!overridesSubFormOptionsMap) {
      overridesSubFormOptionsMap = {};
    }
    overridesSubFormOptionsMap[columnKey] = propertyKey;
    Reflect.defineMetadata('Reflect.OverrideSubFormOptions', overridesSubFormOptionsMap, classConstructor);
  }
}

export {
  OverrideAPI,
  OverrideSubTableAPI,
  OverrideColumn,
  OverrideSubTableColumn,
  OverrideFormOptions,
  OverrideSubFormOptions
}