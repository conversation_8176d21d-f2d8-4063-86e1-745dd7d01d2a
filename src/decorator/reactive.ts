import { ref, watch, WatchOptions } from 'vue'

/**
 * 将一个变量变成响应式的
 * 所谓响应式，就是当这个变量发生变化时，会触发一些操作
 * 比如重新渲染页面，或者触发你设定的 watch，或者更新 computed
 */
function Reactive<T extends object>(target: T, key: string) {
  const value = ref()

  Reflect.defineProperty(target, key, {
    get() {
      return value.value
    },
    set(v) {
      value.value = v
    }
  })
}

function Watch(watchField: string | string[], watchConfig: WatchOptions = {}) {
  return function (target: any, key: string, descriptor: PropertyDescriptor) {
    let watchFn: Array<() => any> | (() => any) = [];

    if (typeof watchField === "string") {
      watchFn = target[watchField];
    } else if (Array.isArray(watchField)) {
      watchFn = watchField.map((field) => () => target[field]);
    }
    watch(watchFn, descriptor.value, watchConfig);
  };
}

export {
  Reactive,
  Watch
}