import 'reflect-metadata'

type ButtonHideList = Array<
  'save'
  | 'edit'
  | 'delete'
  | 'deleteMul'
  | 'importTable'
  | 'exportTable'
  | 'advancedQuery'
  | 'config'
>

function HideButton(buttons: ButtonHideList) {
  const set = new Set(buttons)
  return function (target: any) {
    Reflect.defineMetadata('Reflect.HideButton', set, target)
  }
}

function HideSubTableButton(buttons: ButtonHideList) {
  const set = new Set(buttons)
  return function (target: any) {
    Reflect.defineMetadata('Reflect.HideSubTableButton', set, target)
  }
}

function DefaultSort(options: { columnKey: string, order: 'ascend' | 'descend' }) {
  return function (target: any) {
    Reflect.defineMetadata('Reflect.DefaultSort', options, target)
  }
}

function DefaultSubTableSort(options: { columnKey: string, order: 'ascend' | 'descend' }) {
  return function (target: any) {
    Reflect.defineMetadata('Reflect.DefaultSubTableSort', options, target)
  }
}

function BindForm(formPath: string) {
  return function (target: any) {
    Reflect.defineMetadata('Reflect.BindForm', formPath, target)
  }
}

function HideRowOperate(target: any) {
  Reflect.defineMetadata('Reflect.HideRowOperate', true, target)
}

function HideFirstColumnHerf(target: any) {
  Reflect.defineMetadata('Reflect.HideFirstColumnHerf', true, target)
}

export {
  HideButton,
  DefaultSort,
  BindForm,
  HideRowOperate,
  HideSubTableButton,
  HideFirstColumnHerf,
  DefaultSubTableSort
}