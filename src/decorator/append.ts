import 'reflect-metadata'

function AppendRowButton() {
  return function (target: any, propertyKey: string) {
    const classConstructor = target.constructor;
    let appendRowButton: Function =
      Reflect.getMetadata('Reflect.AppendRowButton', classConstructor);
    if (appendRowButton) {
      console.warn('AppendRowButton already exists, this will override the previous one')
    }
    Reflect.defineMetadata('Reflect.AppendRowButton', propertyKey, classConstructor);
  }
}

function AppendToolbarButton() {
  return function (target: any, propertyKey: string) {
    const classConstructor = target.constructor;
    let appendToolbarButton: Function =
      Reflect.getMetadata('Reflect.AppendToolbarButton', classConstructor);
    if (appendToolbarButton) {
      console.warn('AppendToolbarButton already exists, this will override the previous one')
    }
    Reflect.defineMetadata('Reflect.AppendToolbarButton', propertyKey, classConstructor);
  }
}

export {
  Append<PERSON>owButton,
  AppendToolbarButton
}