import 'reflect-metadata'

function ExecuteBeforeRequest(fn: (form: any) => any) {
  return function (target: any) {
    Reflect.defineMetadata('Reflect.ExecuteBeforeRequest', fn, target)
  }
}

function ExecuteAfterRequest(fn: (data: any) => any) {
  return function (target: any) {
    Reflect.defineMetadata('Reflect.ExecuteAfterRequest', fn, target)
  }
}

function ExecuteOnMount() {
  return function (target: any) {
    Reflect.defineMetadata('Reflect.ExecuteOnMount', true, target)
  }
}

function ExecuteOnBeforeMount() {
}

function ExecuteOnUnmount() {
}

function ExecuteOnUpdated() {
}

function ExecuteOnBeforeUnmount() {
}

export {
  ExecuteOnMount,
  ExecuteOnBeforeMount,
  ExecuteOnUnmount,
  ExecuteOnUpdated,
  ExecuteOnBeforeUnmount,
  ExecuteBeforeRequest,
  ExecuteAfterRequest,
}