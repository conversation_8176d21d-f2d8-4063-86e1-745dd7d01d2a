{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["ESNext", "DOM"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["src/*"], "naive-ui/*": ["./node_modules/@inform/naive-ui-fork/*"], "naive-ui": ["./node_modules/@inform/naive-ui-fork"]}, "resolveJsonModule": true, "types": ["node", "@inform/naive-ui-fork/volar", "vue", "vite/client"], "allowJs": true, "strict": false, "strictNullChecks": true, "noImplicitAny": false, "noUnusedLocals": false, "declaration": true, "declarationDir": "dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true, "experimentalDecorators": true, "useDefineForClassFields": false, "emitDecoratorMetadata": true}, "exclude": ["node_modules", "dist", "postcss.config.mjs", "unocss.config.ts", ".history", "plugin", "update-version.js", "naive-ui", "docs", "reference"]}