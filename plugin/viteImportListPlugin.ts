import { writeFileSync } from 'node:fs'
import { resolve } from 'node:path'
import process from 'node:process'
import type { Plugin } from 'vite'
import fg from 'fast-glob'

interface Options {
  outputPath?: string
}

export function pluginImportList(options: Options = {}): Plugin {
  return {
    name: 'plugin-import-list',
    apply: 'build',
    closeBundle() {
      const outputPath = options.outputPath || resolve(process.cwd(), 'dist')
      const srcPath = resolve(process.cwd(), 'src')

      autoImportList(srcPath, outputPath)
      componentImportList(srcPath, outputPath)
    },
  }
}

function autoImportList(srcPath: string, outputPath: string) {
  // 获取 utils 文件列表
  const utilsFiles = fg.sync('utils/**/!(index).ts', {
    cwd: srcPath,
  })

  // 获取 hooks 文件列表
  const hooksFiles = fg.sync('hooks/**/!(index).{ts,tsx}', {
    cwd: srcPath,
  })

  const constantsFiles = fg.sync('constants/**/!(index).ts', {
    cwd: srcPath,
  })

  const filterList = ['window', 'obj']
  // 生成文件列表内容
  const content = [
    ...utilsFiles,
    ...hooksFiles,
    ...constantsFiles,
  ].map(file => file
    .replace(srcPath, '')
    .replace(/\.ts$/, '')
    .replace(/\.tsx$/, '')
    .replace('utils/', '')
    .replace('hooks/', '')
    .replace('constants/', ''),
  ).filter(item => !filterList.includes(item))

  writeFileSync(
    resolve(outputPath, 'autoImportList.js'),
    `export default ${JSON.stringify(content)}`,
  )
}

function componentImportList(srcPath: string, outputPath: string) {
  // 获取 components 文件列表
  const componentsFiles = fg.sync('components/**/!(index).{vue,tsx}', {
    cwd: srcPath,
  })

  // 生成文件列表内容
  const content = componentsFiles.map(file => {
    return file.split('/').pop()?.replace(/\.vue$/, '').replace(/\.tsx$/, '')
  })

  writeFileSync(
    resolve(outputPath, 'componentImportList.js'),
    `export default ${JSON.stringify(content)}`,
  )
}
