import { writeFileSync } from 'node:fs'
import { resolve } from 'node:path'
import process from 'node:process'
import type { RsbuildPlugin } from '@rsbuild/core'
import { globSync } from 'glob'

interface Options {
  outputPath?: string
}

export function pluginImportList(options: Options = {}): RsbuildPlugin {
  return {
    name: 'plugin-import-list',
    setup(api) {
      api.onAfterBuild(() => {
        const outputPath = options.outputPath || resolve(process.cwd(), 'dist')
        const srcPath = resolve(process.cwd(), 'src')

        // 获取 utils 文件列表
        const utilsFiles = globSync('utils/**/!(index).ts', {
          cwd: srcPath,
        })

        // 获取 hooks 文件列表
        const hooksFiles = globSync('hooks/**/!(index).ts', {
          cwd: srcPath,
        })

        const constantsFiles = globSync('constants/**/!(index).ts', {
          cwd: srcPath,
        })

        // 生成文件列表内容
        const content = [
          ...utilsFiles,
          ...hooksFiles,
          ...constantsFiles,
        ].map(file => file
          .replace(srcPath, '')
          .replace(/\.ts$/, '')
          .replace('utils/', '')
          .replace('hooks/', '')
          .replace('constants/', ''),
        )

        writeFileSync(
          resolve(outputPath, 'import-list.js'),
          `export default ${JSON.stringify(content)}`,
        )
      })
    },
  }
}
