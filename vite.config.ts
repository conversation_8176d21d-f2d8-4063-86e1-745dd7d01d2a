import { resolve } from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import dts from 'vite-plugin-dts'
import { pluginImportList } from './plugin/viteImportListPlugin'

export default defineConfig(({ mode }) => ({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      formats: ['es'],
      fileName: 'index'
    },
    target: 'es2021',
    rollupOptions: {
      external: [
        'vue',
        'vue-router',
        '@vueuse/core',
        'naive-ui',
        'vue-i18n',
        'tdesign-vue-next',
      ],
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      'naive-ui': '@inform/naive-ui-fork'
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://121.36.244.146:7003',
        changeOrigin: true,
        rewrite: path => path
          .replace(/^\/api/, '')
          .replace(/^\/auth/, '/wms'),
        ws: true,
      },
    },
  },
  plugins: [
    vue(),
    vueJsx({
      babelPlugins: [
        ['@babel/plugin-proposal-decorators', { legacy: true }],
        '@babel/plugin-transform-class-properties'
      ]
    }),
    dts({
      include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
      rollupTypes: true,
    }),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        '@vueuse/core',
        'vue-i18n',
        {
          'naive-ui': [
            'useDialog',
            'useMessage',
            'useNotification',
            'useLoadingBar',
            'useModal',
          ],
        },
      ],
      include: [
        /\.[tj]sx?$/,
        /\.vue$/,
        /\.vue\?vue/,
        /\.md$/,
      ],
      dts: 'src/typings/auto-imports.d.ts',
    }),
    Components({
      dts: 'src/typings/components.d.ts',
      resolvers: [
        NaiveUiResolver(),
      ],
    }),
    pluginImportList({
      outputPath: resolve(__dirname, 'dist'),
    }),
  ],
}))