{"name": "@inform/module", "type": "module", "version": "0.1.0-beta.430", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "keywords": ["<PERSON><PERSON>", "Vue3"], "exports": {"./index.css": "./dist/index.css", ".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./autoImportList": "./dist/autoImportList.js", "./componentImportList": "./dist/componentImportList.js"}, "files": ["dist"], "scripts": {"dev": "vite dev --config vite.dev.config.ts", "build": "vite build --config vite.config.ts", "typecheck": "vue-tsc --noEmit", "test": "vitest", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "dependencies": {"@iconify-json/mdi": "^1.2.3", "@vueuse/core": "^12.5.0", "clipboard-polyfill": "^4.1.1", "colord": "2.9.3", "echarts": "5.5.1", "json-tree-view-vue3": "^1.0.2", "prism-code-editor": "^3.4.0", "radash": "12.1.0", "valibot": "^1.0.0", "validator": "^13.12.0", "vue": "3.5.13", "vue-draggable-plus": "^0.6.0", "vue-i18n": "9.14.0", "vue-plugin-hiprint": "0.0.58-fix", "vue-reactive-decorator": "^2.0.1", "vue-router": "4.4.3"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@iconify-json/icon-park-outline": "1.2.0", "@iconify/vue": "4.1.2", "@microsoft/api-extractor": "^7.49.1", "@types/node": "22.5.4", "@unocss/postcss": "^0.65.2", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "dayjs": "^1.11.13", "fast-glob": "^3.3.3", "glob": "^11.0.1", "lint-staged": "15.2.9", "@inform/naive-ui-fork": "^2.42.0", "naive-ui-cron": "0.1.0-beta.4", "reflect-metadata": "^0.2.2", "shiki": "1.22.0", "simple-git-hooks": "2.11.1", "tsc-alias": "^1.8.10", "typescript": "5.5.4", "unocss": "0.62.3", "unplugin-auto-import": "0.18.2", "unplugin-code-sample": "^0.1.10", "unplugin-icons": "0.19.3", "unplugin-vue-components": "0.27.4", "utility-types": "3.11.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-dts": "^4.5.0", "vite-plugin-remove-console": "^2.2.0", "vitepress": "^1.6.3", "vitest": "2.1.2", "vue-tsc": "2.1.6"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "volta": {"node": "20.12.2"}}