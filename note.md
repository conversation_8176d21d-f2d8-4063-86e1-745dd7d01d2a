全新的，面向对象的语法
```tsx
@Controller('/outbound/table')
class OutboundTablePage extends DynamicTablePage {

}

@Controller('/outbound/form')
@OverrideURL({
  page: '',
  importTable: '',
  exportTable: '',
  importTemplate: '',
  delete: '',
  save: '',
  info: '',
  update: ''
})
class OutboundFormPage extends DynamicFormPage {

  @OverrideColumn('lotJson') // 覆写lotJson列的渲染
  renderJsonColumn(row: Record<string, unknown>) {
    return <div>{row.lotJson}</div>
  }

  @Append('rowOperation') // 追加行操作
  renderRowOperation(row: Record<string, unknown>) {
    return <div>
      <IButton onClick={() => this.router.push('/some/api', {
        query: { id: row.id }
      })}>查看</IButton>
    </div>
  }
  
  @Append('toolbar') // 追加工具栏
  renderToolbar() {
    return <div>
      <IButton>新增</IButton>
    </div>
  }

  @Reactive
  outboundList: Record<string, unknown>[] = []
  
  @Watch('outboundList')
  handleOutboundListChange(newVal: Record<string, unknown>[]) {
    console.log(newVal)
  }

  @ExecuteOnMount
  fetchData() {
    this.request.get('/some/api').then(res => {
      this.outboundList = res.data
    })
  }

  // 默认的渲染函数，没有装饰器
  render() {
    return <IModal>
      // ...
      </IModal>
  }
}

export {
  OutboundTablePage,
  OutboundFormPage
}
```

类中可以访问到的上下文
- app    (全局的一些配置、状态、方法)
- router (路由)
- page   (当前页面的一些信息)
- request(用于请求)
- components (可以全局调用的一些组件，比如弹窗和消息)

## 路由
```ts
interface Router {

  query: Record<string, string>
  params: Record<string, string>

  push(path: string, options?: {
    query?: Record<string, string>
    component?: Component
  })

  replace(path: string, options?: {
    query?: Record<string, string>
    component?: Component
  })

  back(options?: {
    closeTab?: boolean
  })

}
```

## 页面
```ts
interface Page {
  // 来自后端返回的动态表配置
  dynamicConfig: DynamicTableConfig
}
```

## 请求
```ts
interface Request {
  get(url: string, options?: {
    query?: Record<string, string>
  })

  post(url: string, options?: {
    query?: Record<string, string>
    body?: unknown
  })

  // 下面是一些常用的快捷方法
  postMultiPart(url: string, options?: {
    query?: Record<string, string>
    body?: Record<string, string | Blob>
  })
  
  download(url: string, options?: {
    query?: Record<string, string>
  })
}
```

## 组件
```ts
interface Components {
  modal: Modal
  dialog: Dialog
  message: Message
  notification: Notification
  loading: {
    start: () => void
    end: () => void
  }
}
```