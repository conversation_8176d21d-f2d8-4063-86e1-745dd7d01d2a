import { resolve } from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { NaiveUiResolver, TDesignResolver } from 'unplugin-vue-components/resolvers'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'

export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      'naive-ui': '@inform/naive-ui-fork'
    },
  },
  server: {
    host: '0.0.0.0',
    port: 9981,
    proxy: {
      '/api': {
        // target: 'http://************:8075',
        // target: 'http://************:8075',
        // target: 'http://************:8075',
        target: 'http://*************:8075',
        changeOrigin: true,
        rewrite: path => path
          .replace(/^\/api/, '')
          .replace(/^\/auth/, '/sn'),
        ws: true,
        // target: 'http://**************:7003',
        // changeOrigin: true,
        // rewrite: path => path
        //   .replace(/^\/api/, '')
        //   .replace(/^\/auth/, '/wms'),
        // ws: true,
      },
    },
  },
  plugins: [
    vue(),
    vueJsx({
      babelPlugins: [
        ['@babel/plugin-proposal-decorators', { legacy: true }],
        '@babel/plugin-transform-class-properties'
      ]
    }),
    AutoImport({
      resolvers: [
        TDesignResolver()
      ],
      imports: [
        'vue',
        'vue-router',
        '@vueuse/core',
        'vue-i18n',
        {
          'naive-ui': [
            'useDialog',
            'useMessage',
            'useNotification',
            'useLoadingBar',
            'useModal',
          ],
        },
      ],
      include: [
        /\.[tj]sx?$/,
        /\.vue$/,
        /\.vue\?vue/,
        /\.md$/,
      ],
      dts: 'src/typings/auto-imports.d.ts',
    }),
    Components({
      dts: 'src/typings/components.d.ts',
      resolvers: [
        NaiveUiResolver(),
        TDesignResolver(),
      ],
    }),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "@/styles/variables.scss";',
      },
    },
  },
  optimizeDeps: {
    include: ['vue', 'vue-router', '@vueuse/core', 'naive-ui'],
  },
  esbuild: {
    pure: ['console.log', 'debugger'],
  },
})
