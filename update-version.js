#!/usr/bin/env node

import fs from 'fs';
import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取 package.json 文件
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// 获取当前版本号
const currentVersion = packageJson.version;
console.log(`当前版本: ${currentVersion}`);

// 检查是否为 beta 版本
if (!currentVersion.includes('-beta.')) {
  console.error('当前版本不是 beta 版本，无法升级');
  process.exit(1);
}

// 解析版本号
const [mainVersion, betaVersion] = currentVersion.split('-beta.');
const newBetaNumber = parseInt(betaVersion, 10) + 1;
const newVersion = `${mainVersion}-beta.${newBetaNumber}`;

// 更新 package.json 中的版本号
packageJson.version = newVersion;
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');

console.log(`版本已更新: ${currentVersion} -> ${newVersion}`);

try {
  // 执行 pnpm build
  console.log('正在构建项目...');
  execSync('pnpm build', { stdio: 'inherit' });
  console.log('构建成功!');

  // 执行 npm publish
  console.log('正在发布到 npm...');
  execSync('npm publish --tag beta', { stdio: 'inherit' });
  console.log(`发布成功! 版本 ${newVersion} 已发布到 npm`);
} catch (error) {
  console.error('操作失败:', error.message);
  process.exit(1);
} 