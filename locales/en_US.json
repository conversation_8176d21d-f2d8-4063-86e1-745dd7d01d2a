{"error": {"network": "Network error", "unknown": "Unknown error", "unauthorized": "Unauthorized", "componentNotFound": "Component not found", "downloadFailed": "Download failed", "fileNum": "The number of files exceeds the limit.", "fileSize": "The file size exceeds the limit.", "fileType": "Incorrect file type", "uploadFailed": "Upload failed"}, "datePicker": {"today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "thisQuarter": "This Quarter"}, "common": {"cancel": "Cancel", "confirm": "Confirm", "close": "Closure", "reload": "Refresh", "choose": "<PERSON><PERSON>", "navigate": "Navigate", "inputPlaceholder": "please enter", "selectPlaceholder": "please choose", "codeSample": "Sample code", "female": "Woman", "male": "male", "action": "operation", "ban": "Disabled", "banOrOpen": "Disable/Enable", "button": "<PERSON><PERSON>", "compare": {"equal": "equal to", "greater": "greater than", "greaterEqual": "Greater than or equal to", "in": "Among...", "less": "less than", "lessEqual": "Less than or equal to", "like": "include", "notEqual": "unequal to", "notIn": "Not in... among."}, "create": "create", "delete": "Delete", "deleteConfirm": "Are you sure you want to delete?", "deleteMul": "delete", "downloadTemplate": "Download template", "dragUploadTip": "Drag and drop files or click to upload", "edit": "Edit", "exportTable": "Export", "false": "No.", "formatError": "Format error", "importTable": "Import", "link": {"and": "and", "or": "or"}, "menu": "<PERSON><PERSON>", "open": "Start", "operationSuccess": "Operation successful", "required": "Required", "reset": "Reset", "save": "Add", "saveLabel": "Save", "search": "Search", "submit": "submit", "tip": "Reminder", "true": "Yes", "update": "Edit", "upload": "import", "uploadFile": "Upload file", "validateError": "Verification failed", "normal": "Normal", "secret": "Confidentiality"}, "app": {"loginOut": "Login out", "loginOutContent": "Confirm to log out of current account?", "loginOutTitle": "Sign out", "userCenter": "Personal center", "light": "Light", "dark": "Dark", "system": "System", "backTop": "Back to top", "toggleSider": "Toggle sidebar", "BreadcrumbIcon": "Breadcrumbs icon", "blackAndWhite": "Black and white mode", "bottomCopyright": "Bottom copyright", "breadcrumb": "Bread crumbs", "colorWeak": "Color Weakness Mode", "interfaceDisplay": "Interface display", "logoDisplay": "LOGO display", "messages": "Messages", "multitab": "Display multiple tabs", "notifications": "Notify", "notificationsTips": "Notification", "pageTransition": "Page transition", "reset": "Reset", "resetSettingContent": "Confirm to reset all settings?", "resetSettingMeaasge": "Reset successful", "resetSettingTitle": "Reset settings", "searchPlaceholder": "Search page/path", "setting": "Setting", "systemSetting": "System settings", "themeColor": "Theme color", "themeSetting": "Theme settings", "todos": "Todos", "toggleFullScreen": "Toggle full screen", "togglContentFullScreen": "Toggle content full screen", "topProgress": "Top progress", "transitionFadeBottom": "Bottom fade", "transitionFadeScale": "Scale fade", "transitionFadeSlide": "Side fade", "transitionNull": "No transition", "transitionSoft": "Soft", "transitionZoomFade": "Expand fade out", "transitionZoomOut": "Zoom out", "watermake": "Watermark", "closeOther": "Close other", "closeAll": "Close all", "closeLeft": "Close left", "closeRight": "Close right", "backHome": "Back to the homepage", "getRouteError": "Failed to obtain route, please try again later.", "layoutSetting": "Layout settings", "leftMenu": "Left menu", "topMenu": "Top menu", "mixMenu": "Mix menu"}, "login": {"signInTitle": "<PERSON><PERSON>", "accountRuleTip": "Please enter account", "passwordRuleTip": "Please enter password", "or": "Or", "rememberMe": "Remember me", "forgotPassword": "Forget the password?", "signIn": "Sign in", "signUp": "Sign up", "noAccountText": "Don't have an account?", "accountPlaceholder": "Enter the account number", "checkPasswordPlaceholder": "Please enter password again", "checkPasswordRuleTip": "Please confirm password again", "haveAccountText": "Do you have an account?", "passwordPlaceholder": "Enter password", "readAndAgree": "I have read and agree", "registerTitle": "Register", "userAgreement": "User Agreement", "resetPassword": "Reset password", "resetPasswordPlaceholder": "Enter account/mobile phone number", "resetPasswordRuleTip": "Please enter your account/mobile phone number", "resetPasswordTitle": "Reset"}, "route": {"appRoot": "Home", "cardTable": "Card list", "draggableTable": "Draggable list", "editTable": "Editable list", "commonTable": "Common list", "dashboard": "Dashboard", "demo": "Function example", "fetch": "Request example", "table": "List", "monitor": "Monitoring", "multi": "Multi-level menu", "multi2": "Multi-level menu subpage", "multi2Detail": "Details page of multi-level menu", "multi3": "multi-level menu", "multi4": "Multi-level menu 3-1", "workbench": "Workbench", "QRCode": "QR code", "about": "About", "clipboard": "Clipboard", "demo403": "403", "demo404": "404", "demo500": "500", "dictionarySetting": "Dictionary settings", "documents": "Document", "documentsVite": "Vite", "documentsVue": "<PERSON><PERSON>", "documentsVueuse": "VueUse (external link)", "documentsMarina": "Marina docs", "echarts": "Echarts", "editor": "Editor", "editorMd": "MarkDown editor", "editorRich": "Rich text editor", "error": "Exception page", "icons": "Icon", "justSuper": "Supervisible", "map": "Map", "menuSetting": "<PERSON><PERSON>", "permission": "Permissions", "permissionDemo": "Permissions example", "setting": "System settings", "userCenter": "Personal Center", "accountSetting": "User settings", "cascader": "Administrative region selection", "dict": "Dictionary example"}, "http": {"400": "Syntax error in the request", "401": "User unauthorized", "403": "Server refused access", "404": "Requested resource does not exist", "405": "Request method not allowed", "408": "Network request timed out", "500": "Internal server error", "501": "Server not implemented the requested functionality", "502": "Bad gateway", "503": "Service unavailable", "504": "Gateway timeout", "505": "HTTP version not supported for this request", "defaultTip": "Request error"}, "components": {"iconSelector": {"inputPlaceholder": "Select target icon", "searchPlaceholder": "Search icon", "clearIcon": "Clear icon", "selectorTitle": "Icon selection"}, "copyText": {"message": "<PERSON><PERSON>d successfully", "tooltip": "Copy"}, "toolbar": {"download": "Download", "import": "Bulk import", "new": "Newly built", "advancedQuery": "Advanced search", "all": "All", "export": "Export", "exportConfirm": "Are you sure you want to export?", "selected": "Selected"}, "advancedQueryDialog": {"addQueryGroup": "Add group", "addQueryItem": "Add new conditions", "handleReset": "Clear", "handleSearch": "confirm", "pleaseInputValue": "Please enter the search value", "pleaseSelectField": "Please select a field.", "pleaseSelectOperator": "Please select an operator.", "pleaseSelectValue": "Please select the query value.", "title": "Advanced search"}}, "pages": {"dashboard": {"activeUser": "Active user", "totalUser": "Total user", "visitNum": "Visit number", "registerNum": "Register number", "totalVisitNum": "Total visit number", "collectionNum": "Collection number", "orderNum": "Order number", "todoNum": "Todo number", "more": "More", "dynamic": "Dynamic", "announcement": "Announcement", "message": "Message", "notice": "Notice", "activity": "Activity", "taskProgress": "Task progress", "endTime": "End time", "progress": "progress", "startTime": "Start time", "status": "Status", "totalCollectionNum": "Cumulative Collection量", "totalDownloadNum": "Cumulative download volume", "totalRegisterNum": "Cumulative registration volume", "trafficTrend": "Traffic trend", "transactionName": "Transaction Name", "transactionRecord": "Transaction record", "visitSource": "Referral来源", "visitTrend": "Visitors trend"}, "user": {"title": "Personal center", "confirmPassword": "Confirm password", "confirmPasswordRuleTip": "Please enter the new password again.", "formValidateError": "Form validation failed.", "newPassword": "New password", "newPasswordRuleTip": "Please enter a new password.", "password": "Original password", "passwordNotMatch": "The passwords entered twice do not match.", "passwordRuleTip": "Please enter the original password", "passwordSuccess": "Password changed successfully.", "realName": "Real name", "roleIdList": "Character", "submit": "Submit changes", "updateInfo": "Information modification", "username": "Username"}, "config": {"dictionary": {"createDate": "Creation time", "dictName": "Dictionary name", "dictTag": "Dictionary tag", "dictType": "Dictionary type", "dictValue": "Dictionary value", "remark": "Note", "sort": "Sort"}, "menu": {"button": "<PERSON><PERSON>", "buttonType": "Button type", "commonButton": "General button", "commonButtonList": "General button list", "commonButtonPrefix": "Common Button Prefix", "customButton": "Custom button", "dir": "Table of Contents", "disable": "Disable", "dynCode": "Dynamic table identifier", "enable": "enable", "firstRouter": "Affiliated System", "icon": "Icon", "isCache": "Is caching", "isEnable": "Enable or not", "menuName": "<PERSON>u Name", "menuType": "Menu type", "newWindow": "New window", "openStyle": "Opening method", "orderNum": "Sort", "pageType": "page", "pid": "Parent menu", "sameWindow": "fellow student", "title": "<PERSON><PERSON>", "url": "Routing address"}}, "developer": {"menu": {"title": "Menu management"}, "title": "Developer settings"}, "dyntab": {"auth_filter": "Data filtering", "create_date": "Creation time", "datasource": "Data source", "dyn_code": "Dynamic Table Identifier", "select_table": "Selection form", "table_comment": "Function name", "table_name": "Table name", "table_type": "Table type", "table_type_main": "Main table", "table_type_single": "Single table", "table_type_sub": "Child form"}, "log": {"interface": {"appName": "Application Name", "consumingTime": "Time-consuming", "logType": "Log type", "operation": "Operation", "requestDate": "Request time", "requestMsg": "Request Parameters", "requestUrl": "Request Address", "responseMsg": "response value", "resultCode": "Status", "sourceIp": "Source IP", "targetIp": "Target IP", "title": "Interface log", "userAgent": "User-Agent"}, "login": {"createDate": "Creation time", "failure": "failure", "ip": "IP", "locked": "lock", "login": "Sign in", "loginStatus": "Login status", "logout": "Log out", "operation": "Operation", "success": "Success", "title": "Login log", "userAgent": "browser", "userName": "Username"}, "operation": {"consumingTime": "time-consuming", "createDate": "Creation time", "errorMsg": "Error message", "operation": "Operation", "requestMethod": "Request Method", "requestParam": "Request parameters", "requestPath": "Request path", "responseValue": "Response value", "resultCode": "Status", "title": "Operation log", "userAgent": "User-Agent", "userIp": "IP", "userName": "Username"}}}, "i18_0000000001": "", "i18n_0000000002": "Username cannot be empty", "i18_0000000003": "Password cannot be empty", "i18_0000000004": "User does not exist", "i18_0000000005": "User is disabled", "i18_0000000006": "Password error", "i18_0000000007": "No warehouse permission", "hooks": {"useUpload": {"fileNum": "The number of files exceeds the limit", "fileSize": "The file size exceeds the limit.", "fileType": "Incorrect file type", "uploadFailed": "Upload failed"}}, "i18n_0000000001": "System initializing, please log in later.", "i18n_0000000003": "Password cannot be empty", "i18n_0000000004": "User does not exist.", "i18n_0000000005": "User disabled", "i18n_0000000006": "Incorrect password", "i18n_0000000007": "No warehouse permission.", "tip": {"delete": "Are you sure you want to delete?", "rightClick": "Right-click to perform operation.", "internalServerError": "Server error"}}