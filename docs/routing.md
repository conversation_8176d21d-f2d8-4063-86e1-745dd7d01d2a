# 路由配置指南

## 路由系统介绍

本框架采用了**约定式路由**，这意味着您不需要手动配置路由，系统会根据文件结构和命名规则自动生成路由。

## 路由生成规则

### 文件路径与路由地址

框架会根据以下规则将文件路径转换为路由地址：

| 文件路径 | 生成的路由地址 |
| --- | --- |
| `src/views/wms/inventory/list/index.page.vue` | `/wms/inventory/list` |
| `src/views/wms/dashboard/workbench/index.page.vue` | `/wms/dashboard/workbench` |
| `src/views/auth/user/profile/index.page.vue` | `/auth/user/profile` |

### 系统模块

框架支持多个系统模块，这些模块在 `src/config/system.ts` 中配置：

```ts
export const systemList = [
  {
    key: "auth",  // 认证系统
    homePath: "/auth/dashboard/workbench",  // 首页路径
  },
  {
    key: "wcs",   // 仓控系统
    homePath: "/wcs/dashboard/workbench",
  },
  {
    key: "wms",   // 仓储管理系统
    homePath: "/wms/dashboard/workbench",
  },
  {
    key: "pda",   // 手持设备系统
  },
];
```

每个模块对应 `src/views` 下的一个目录，只有在系统模块列表中声明的模块才会被加载。

## 页面自动注册

系统会自动扫描并注册所有以 `.page.vue` 结尾的文件作为页面路由。通过这种方式，您只需要按照规定的目录结构创建文件，无需额外的路由配置。

## 路由参数

### 动态参数

如果您需要在路由中使用动态参数，可以通过以下方式创建文件：

1. 创建带参数的文件夹，例如 `[id]`
2. 在该文件夹下创建 `index.page.vue` 文件

例如：

```
src/
└── views/
    └── wms/
        └── inventory/
            └── detail/
                └── [id]/
                    └── index.page.vue
```

生成的路由将是：`/wms/inventory/detail/:id`

### 访问路由参数

在页面中，您可以使用 Vue Router 提供的 API 访问路由参数：

```vue
<script setup>
import { useRoute } from 'vue-router'

const route = useRoute()
const id = route.params.id

// 使用参数获取详情数据
const getDetail = useRequest(`/wms/inventory/${id}`)

// ...
</script>
```

## 路由跳转

### 在代码中跳转

```vue
<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 跳转到指定路径
function goToList() {
  router.push('/wms/inventory/list')
}

// 带参数跳转
function goToDetail(id) {
  router.push(`/wms/inventory/detail/${id}`)
}

// 带查询参数跳转
function goToListWithQuery() {
  router.push({
    path: '/wms/inventory/list',
    query: { keyword: '关键词', status: 'active' }
  })
}
</script>
```

### 在模板中跳转

使用 Vue Router 提供的 `<router-link>` 组件：

```vue
<template>
  <router-link to="/wms/inventory/list">进入列表页</router-link>
  
  <!-- 带参数跳转 -->
  <router-link :to="`/wms/inventory/detail/${id}`">查看详情</router-link>
  
  <!-- 带查询参数跳转 -->
  <router-link :to="{ path: '/wms/inventory/list', query: { keyword: '关键词' } }">
    搜索列表
  </router-link>
</template>
```

## 系统首页配置

每个系统模块可以设置自己的首页路径，用户登录后会自动跳转到对应的首页。首页配置在 `src/config/system.ts` 文件中：

```ts
export const systemList = [
  {
    key: "wms",
    homePath: "/wms/dashboard/workbench",  // 首页路径
  },
  // 其他系统...
];
```

确保您创建的首页路径与配置的 `homePath` 一致。 