# 组件使用指南

## 组件库介绍

本框架主要使用 Naive UI 作为 UI 组件库，提供了丰富的组件供您使用，包括：表格、表单、按钮、弹窗、菜单等。这些组件不仅美观，而且功能强大，可以满足大多数业务场景的需求。

## 常用组件使用方法

### 1. 按钮 (NButton)

按钮组件是最基本也是使用最频繁的组件之一：

```vue
<template>
  <n-button>普通按钮</n-button>
  <n-button type="primary">主要按钮</n-button>
  <n-button type="info">信息按钮</n-button>
  <n-button type="success">成功按钮</n-button>
  <n-button type="warning">警告按钮</n-button>
  <n-button type="error">错误按钮</n-button>
  
  <!-- 带图标的按钮 -->
  <n-button>
    <template #icon>
      <div class="i-icon-park-outline-plus"></div>
    </template>
    添加
  </n-button>
</template>
```

### 2. 输入框 (NInput)

输入框用于接收用户的文本输入：

```vue
<script setup>
import { ref } from 'vue'

const inputValue = ref('')
</script>

<template>
  <n-input v-model:value="inputValue" placeholder="请输入内容" />
  
  <!-- 带前缀和后缀的输入框 -->
  <n-input v-model:value="inputValue" placeholder="请输入搜索内容">
    <template #prefix>
      <div class="i-icon-park-outline-search"></div>
    </template>
  </n-input>
  
  <!-- 禁用状态 -->
  <n-input disabled value="禁用状态" />
</template>
```

### 3. 表格 (NDataTable)

表格组件是展示数据的主要方式：

```vue
<script setup>
import { ref, onMounted } from 'vue'

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 表格列定义
const columns = [
  {
    title: '姓名',
    key: 'name'
  },
  {
    title: '年龄',
    key: 'age'
  },
  {
    title: '地址',
    key: 'address'
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h('div', [
        h('n-button', {
          size: 'small',
          onClick: () => handleEdit(row)
        }, { default: () => '编辑' }),
        h('n-button', {
          size: 'small',
          type: 'error',
          style: 'margin-left: 8px',
          onClick: () => handleDelete(row)
        }, { default: () => '删除' })
      ])
    }
  }
]

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  onChange: (page) => {
    pagination.value.page = page
    loadData()
  }
})

// 加载数据
async function loadData() {
  loading.value = true
  try {
    const getUserList = useRequest('/user/list')
    const res = await getUserList.get({
      params: {
        page: pagination.value.page,
        size: pagination.value.pageSize
      }
    })
    
    tableData.value = res.data.value.items || []
    pagination.value.itemCount = res.data.value.total || 0
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadData()
})

// 处理编辑
function handleEdit(row) {
  console.log('编辑', row)
}

// 处理删除
function handleDelete(row) {
  console.log('删除', row)
}
</script>

<template>
  <n-data-table
    :columns="columns"
    :data="tableData"
    :loading="loading"
    :pagination="pagination"
  />
</template>
```

### 4. 表单 (NForm)

表单用于数据收集和提交：

```vue
<script setup>
import { ref } from 'vue'

// 表单数据
const formRef = ref(null)
const formModel = ref({
  name: '',
  age: null,
  email: '',
  introduction: ''
})

// 表单校验规则
const rules = {
  name: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入姓名'
  },
  age: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入年龄'
  },
  email: {
    required: true,
    trigger: ['blur', 'input'],
    validator(rule, value) {
      if (!value) {
        return new Error('请输入邮箱')
      } else if (!/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(value)) {
        return new Error('请输入正确的邮箱格式')
      }
      return true
    }
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate()
    
    // 验证通过，提交数据
    const createUser = useRequest('/user/create')
    const res = await createUser.post(formModel.value)
    
    if (res.code.value === 0) {
      window.$message.success('提交成功')
      // 重置表单
      formRef.value.restoreValidation()
      Object.assign(formModel.value, {
        name: '',
        age: null,
        email: '',
        introduction: ''
      })
    } else {
      window.$message.error(res.msg.value || '提交失败')
    }
  } catch (err) {
    window.$message.error('表单验证失败')
  }
}
</script>

<template>
  <n-form
    ref="formRef"
    :model="formModel"
    :rules="rules"
    label-placement="left"
    label-width="80"
  >
    <n-form-item label="姓名" path="name">
      <n-input v-model:value="formModel.name" placeholder="请输入姓名" />
    </n-form-item>
    
    <n-form-item label="年龄" path="age">
      <n-input-number v-model:value="formModel.age" placeholder="请输入年龄" />
    </n-form-item>
    
    <n-form-item label="邮箱" path="email">
      <n-input v-model:value="formModel.email" placeholder="请输入邮箱" />
    </n-form-item>
    
    <n-form-item label="简介" path="introduction">
      <n-input
        v-model:value="formModel.introduction"
        type="textarea"
        placeholder="请输入简介"
      />
    </n-form-item>
    
    <div class="flex justify-center mt-4">
      <n-button type="primary" @click="handleSubmit">提交</n-button>
    </div>
  </n-form>
</template>
```

### 5. 弹窗 (NModal)

弹窗组件用于显示重要信息或进行操作确认：

```vue
<script setup>
import { ref } from 'vue'

const showModal = ref(false)

function openModal() {
  showModal.value = true
}

function closeModal() {
  showModal.value = false
}

function handleConfirm() {
  // 执行确认操作
  console.log('确认操作')
  closeModal()
}
</script>

<template>
  <n-button @click="openModal">打开弹窗</n-button>
  
  <n-modal
    v-model:show="showModal"
    title="确认操作"
    preset="dialog"
    positive-text="确认"
    negative-text="取消"
    @positive-click="handleConfirm"
    @negative-click="closeModal"
  >
    <p>确定要执行此操作吗？</p>
  </n-modal>
</template>
```

### 6. 选择器 (NSelect)

选择器用于从多个选项中选择一个或多个值：

```vue
<script setup>
import { ref } from 'vue'

const selectedValue = ref(null)
const options = [
  { label: '选项1', value: 1 },
  { label: '选项2', value: 2 },
  { label: '选项3', value: 3 }
]
</script>

<template>
  <n-select
    v-model:value="selectedValue"
    :options="options"
    placeholder="请选择"
  />
</template>
```

### 7. 日期选择器 (NDatePicker)

用于选择日期或日期范围：

```vue
<script setup>
import { ref } from 'vue'

const date = ref(null)
const dateRange = ref(null)
</script>

<template>
  <!-- 单个日期选择 -->
  <n-date-picker v-model:value="date" />
  
  <!-- 日期范围选择 -->
  <n-date-picker v-model:value="dateRange" type="daterange" />
</template>
```

## 图标使用方法

本框架使用图标集成方案，可以直接通过 CSS 类名来使用图标：

```vue
<template>
  <!-- 使用图标 -->
  <div class="i-icon-park-outline-home"></div>
  <div class="i-icon-park-outline-user"></div>
  <div class="i-icon-park-outline-setting"></div>
</template>
```

## 常用样式类

框架集成了 UnoCSS，提供了许多实用的 CSS 工具类：

```vue
<template>
  <!-- 弹性布局 -->
  <div class="flex items-center justify-between">
    <span>左侧内容</span>
    <span>右侧内容</span>
  </div>
  
  <!-- 边距 -->
  <div class="m-4 p-2">带边距的内容</div>
  
  <!-- 尺寸 -->
  <div class="w-full h-10">宽度 100%，高度 10 单位</div>
  
  <!-- 文本 -->
  <p class="text-lg font-bold text-blue-500">蓝色粗体大文本</p>
</template>
```

## 最佳实践

1. **组件按需导入**：只导入您需要使用的组件，减小打包体积。

2. **使用组合式 API**：尽量使用 `script setup` 语法和组合式 API，使代码更简洁。

3. **表单验证**：使用 NForm 提供的表单验证功能，确保数据的正确性。

4. **响应式设计**：利用 UnoCSS 提供的工具类实现响应式布局。

5. **按照业务划分组件**：将页面拆分为多个小组件，使代码更易维护。 