# 基础概念

## 什么是前端框架？

前端框架是一套预先设计好的代码结构和工具集合，用于简化网页应用的开发过程。使用框架可以让您专注于业务逻辑的开发，而不必从零开始构建所有功能。

## 本框架的核心概念

### 1. 组件化开发

组件是构成页面的基本单位，就像搭积木一样，我们可以通过组合不同的组件来构建完整的页面。

在本框架中，每个组件通常由以下部分组成：
- **模板**：定义组件的 HTML 结构
- **脚本**：定义组件的交互逻辑
- **样式**：定义组件的外观

示例组件：
```vue
<template>
  <div class="my-component">
    <h1>{{ title }}</h1>
    <button @click="handleClick">点击我</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 定义响应式数据
const title = ref('我是组件标题')

// 定义方法
function handleClick() {
  alert('按钮被点击了！')
}
</script>
```

### 2. 路由系统

路由系统负责管理页面之间的跳转。您可以将路由理解为不同页面的"地址"，通过访问不同的地址可以切换到不同的页面。

### 3. 状态管理

状态管理是指管理应用中的数据状态。在复杂应用中，多个组件可能需要共享和操作同一数据，这时就需要使用状态管理来统一管理数据。

### 4. 响应式设计

响应式是指数据变化时，界面会自动更新。这是现代前端框架的一个核心特性，使得开发人员不需要手动操作 DOM 就能实现界面更新。

示例：
```vue
<script setup>
import { ref } from 'vue'

const count = ref(0)

function increment() {
  count.value++  // 修改数据后，显示该数据的界面会自动更新
}
</script>

<template>
  <div>
    <p>当前计数：{{ count }}</p>
    <button @click="increment">增加</button>
  </div>
</template>
```

### 5. 布局系统

本框架提供了几种常用的布局模式：

- **左侧菜单布局**：菜单在页面左侧，内容在右侧
- **顶部菜单布局**：菜单在页面顶部，内容在下方
- **混合菜单布局**：顶部和左侧都有菜单

您可以根据需求选择合适的布局模式。

## 技术栈简介

本框架基于以下技术构建：

- **Vue 3**：一个流行的前端框架，提供响应式数据和组件系统
- **TypeScript**：JavaScript 的超集，提供类型检查
- **Naive UI**：一套优雅的 UI 组件库
- **Vite**：一个快速的前端构建工具

不过，即使您不了解这些技术，也可以通过本文档学习如何使用框架开发应用。 