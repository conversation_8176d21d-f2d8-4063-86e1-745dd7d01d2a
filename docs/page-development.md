# 页面开发指南

## 页面文件结构

在本框架中，页面文件必须遵循特定的命名规则和目录结构才能被系统自动识别并注册。

### 命名规则

所有页面文件必须以 `.page.vue` 结尾，系统会自动识别并注册这些文件作为页面路由。

### 目录结构

页面文件应该按照以下结构组织：

```
src/
└── views/
    └── [系统模块]/        # 例如：wms、wcs、auth 等
        └── [功能模块]/    # 例如：dashboard、inventory、task 等
            └── [页面]/    # 具体页面
                └── index.page.vue  # 页面文件
```

例如，如果您要创建一个WMS系统中的库存查询页面，路径应为：`src/views/wms/inventory/query/index.page.vue`

## 创建新页面

### 步骤 1：创建页面文件

创建一个以 `.page.vue` 结尾的文件，例如：

```vue
<script setup lang="ts">
// 导入所需组件和API
import { NCard, NForm, NFormItem, NInput, NButton } from 'naive-ui'
import { ref } from 'vue'

// 定义页面数据
const formValue = ref({
  name: '',
  code: ''
})

// 定义页面方法
function handleSubmit() {
  // 处理表单提交逻辑
  console.log('提交数据:', formValue.value)
}
</script>

<template>
  <div class="p-4">
    <NCard title="库存查询" class="shadow-md">
      <NForm :model="formValue" label-placement="left" label-width="80">
        <NFormItem label="物料名称" path="name">
          <NInput v-model:value="formValue.name" placeholder="请输入物料名称" />
        </NFormItem>
        <NFormItem label="物料编码" path="code">
          <NInput v-model:value="formValue.code" placeholder="请输入物料编码" />
        </NFormItem>
        <div class="flex justify-end mt-4">
          <NButton type="primary" @click="handleSubmit">查询</NButton>
        </div>
      </NForm>
    </NCard>
  </div>
</template>
```

### 步骤 2：自动路由注册

框架会自动扫描并注册所有符合命名规则的页面文件，无需手动配置路由。

例如，上述文件会自动生成路由 `/wms/inventory/query`。

## 页面开发最佳实践

### 1. 使用组件

框架提供了丰富的 UI 组件，使用这些组件可以快速构建页面。主要使用的是 Naive UI 组件库：

```vue
<script setup>
import { NButton, NCard, NTable, NInput } from 'naive-ui'
</script>
```

### 2. 数据请求

使用框架提供的 `useRequest` 函数发送请求：

```vue
<script setup>
// 定义API请求
const getInventoryList = useRequest('/wms/inventory/list')

// 使用请求
async function fetchData() {
  const { data } = await getInventoryList.get({ 
    params: { page: 1, size: 10 } 
  })
  // 处理返回数据
  console.log(data.value)
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>
```

### 3. 国际化

使用 `t` 函数实现多语言：

```vue
<script setup>
import { t } from '@/expose/i18n'
</script>

<template>
  <h1>{{ t('system.wms.inventory.title') }}</h1>
</template>
```

### 4. 页面间跳转

使用 `router` 进行页面跳转：

```vue
<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

function goToDetail(id) {
  router.push(`/wms/inventory/detail/${id}`)
}
</script>
```

## 页面示例

以下是一个包含表格和搜索功能的完整页面示例：

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { NCard, NDataTable, NSpace, NInput, NButton } from 'naive-ui'
import { t } from '@/expose/i18n'

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0
})

// 搜索条件
const searchKeyword = ref('')

// 获取数据的API
const getDataList = useRequest('/api/data/list')

// 加载数据
async function loadData() {
  loading.value = true
  try {
    const { data } = await getDataList.get({
      params: {
        page: pagination.value.page,
        size: pagination.value.pageSize,
        keyword: searchKeyword.value
      }
    })
    
    tableData.value = data.value.items || []
    pagination.value.itemCount = data.value.total || 0
  } finally {
    loading.value = false
  }
}

// 处理分页变化
function handlePageChange(page) {
  pagination.value.page = page
  loadData()
}

// 处理搜索
function handleSearch() {
  pagination.value.page = 1
  loadData()
}

// 表格列定义
const columns = [
  {
    title: t('system.common.name'),
    key: 'name'
  },
  {
    title: t('system.common.code'),
    key: 'code'
  },
  {
    title: t('system.common.createTime'),
    key: 'createTime'
  }
]

// 页面加载时获取数据
onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="p-4">
    <NCard :title="t('system.wms.dataManagement')" class="shadow-md">
      <!-- 搜索区域 -->
      <div class="mb-4 flex items-center">
        <NInput v-model:value="searchKeyword" :placeholder="t('system.common.searchPlaceholder')" />
        <NButton class="ml-2" type="primary" @click="handleSearch">
          {{ t('system.common.search') }}
        </NButton>
      </div>
      
      <!-- 表格区域 -->
      <NDataTable
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
      />
    </NCard>
  </div>
</template>