---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: "音飞前端文档"
  text: "A VitePress Site"
  tagline: My great project tagline
  actions:
    - theme: brand
      text: Markdown Examples
      link: /markdown-examples
    - theme: alt
      text: API Examples
      link: /api-examples
---

# 内部系统前端框架使用文档

## 简介

欢迎使用内部系统前端框架！本文档将帮助您快速了解如何使用该框架进行内部系统的开发。无论您是否具备前端开发经验，都可以通过本文档学习如何使用框架的功能。

## 主要特点

- **简单易用**：约定式路由，自动注册页面
- **开箱即用**：集成了常用组件和功能
- **统一风格**：使用 Naive UI 组件库，保持界面风格统一
- **响应式设计**：适配不同屏幕尺寸
- **国际化支持**：内置多语言切换功能

## 框架能力

- 自动路由注册
- 组件按需引入
- 数据请求封装
- 国际化支持
- 主题配置
- 统一错误处理
- 权限控制
- 布局系统

## 快速开始

要开始开发，请参考[快速开始](./quick-start.md)指南。

## 内容目录

- [快速开始](./quick-start.md) - 如何安装和启动项目
- [基础概念](./basic-concepts.md) - 了解框架的基本概念
- [页面开发](./page-development.md) - 如何创建新页面
- [路由配置](./routing.md) - 如何设置页面路由
- [使用组件](./components.md) - 如何使用内置组件
- [数据请求](./data-requests.md) - 如何发送数据请求
- [常见问题](./faq.md) - 常见问题解答

