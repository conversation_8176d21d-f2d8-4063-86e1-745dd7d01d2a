# 常见问题解答

## 基础问题

### Q1: 如何启动开发服务器？

**答**：在项目根目录下执行以下命令：

```bash
pnpm dev
```

### Q2: 如何构建生产版本？

**答**：在项目根目录下执行以下命令：

```bash
pnpm build
```

### Q3: 我修改了代码，但页面没有更新，怎么办？

**答**：
- 检查文件是否保存
- 尝试刷新浏览器
- 检查控制台是否有错误信息
- 重启开发服务器

### Q4: 我的组件引入后显示未定义，怎么回事？

**答**：确保您已经正确导入了组件：

```vue
<script setup>
import { NButton, NInput } from 'naive-ui'
</script>
```

如果使用的是全局组件，可能需要检查组件名称是否正确。

## 页面开发

### Q5: 我新创建的页面为什么访问不到？

**答**：
- 确保页面文件命名正确，必须以 `.page.vue` 结尾
- 确保页面位于正确的目录结构下 (`src/views/[系统模块]/...`)
- 检查对应的系统模块是否在 `src/config/system.ts` 中配置
- 尝试重启开发服务器

### Q6: 如何在页面间传递参数？

**答**：可以通过路由参数、查询参数或状态管理来传递：

1. **路由参数**：
```js
// 跳转时
router.push(`/wms/inventory/detail/${id}`)

// 接收时
const route = useRoute()
const id = route.params.id
```

2. **查询参数**：
```js
// 跳转时
router.push({
  path: '/wms/inventory/list',
  query: { keyword: '测试' }
})

// 接收时
const route = useRoute()
const keyword = route.query.keyword
```

### Q7: 页面如何加载初始数据？

**答**：通常在 `onMounted` 生命周期钩子中加载数据：

```vue
<script setup>
import { onMounted, ref } from 'vue'

const data = ref(null)
const loading = ref(false)

async function fetchData() {
  loading.value = true
  try {
    const getDetail = useRequest('/api/detail')
    const res = await getDetail.get()
    data.value = res.data.value
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})
</script>
```

## 数据请求

### Q8: API 请求返回 404 错误怎么办？

**答**：
- 检查 API 路径是否正确
- 确认后端服务是否已启动
- 检查网络连接是否正常
- 查看网络请求的详细错误信息

### Q9: 如何处理文件上传？

**答**：使用 FormData 对象配合 `useRequest` 函数：

```js
const formData = new FormData()
formData.append('file', fileObject)

const uploadApi = useRequest('/api/upload')
const response = await uploadApi.post(formData, {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})
```

### Q10: 如何处理请求错误？

**答**：使用 try/catch 捕获错误：

```js
try {
  const response = await api.get()
  // 处理成功响应
} catch (error) {
  // 处理错误
  console.error('请求出错', error)
  window.$message.error('操作失败，请重试')
}
```

## 组件使用

### Q11: 表格数据如何进行排序和筛选？

**答**：使用 NDataTable 的排序和筛选属性：

```js
const columns = [
  {
    title: '姓名',
    key: 'name',
    sorter: true, // 启用排序
    filterOptions: [
      { label: '张三', value: '张三' },
      { label: '李四', value: '李四' }
    ],
    filter: (value, row) => row.name === value
  }
]
```

### Q12: 如何使用确认对话框？

**答**：使用 NModal 组件或 window.$dialog API：

```js
function showConfirm() {
  window.$dialog.warning({
    title: '确认操作',
    content: '确定要删除这条记录吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      // 执行确认操作
    }
  })
}
```

### Q13: 如何显示加载中状态？

**答**：可以使用 NSpin 组件包裹内容：

```vue
<template>
  <n-spin :show="loading">
    <!-- 内容 -->
  </n-spin>
</template>
```

或者使用 window.$loading API：

```js
async function fetchData() {
  const loading = window.$loading.start('加载中...')
  try {
    // 执行操作
    await someAsyncOperation()
  } finally {
    loading.finish()
  }
}
```

## 布局和样式

### Q14: 如何实现响应式布局？

**答**：使用框架提供的 UnoCSS 工具类：

```html
<div class="flex flex-col md:flex-row gap-4">
  <div class="w-full md:w-1/3">侧边栏</div>
  <div class="w-full md:w-2/3">主内容</div>
</div>
```

### Q15: 如何添加自定义样式？

**答**：可以在组件中使用 `<style>` 标签添加样式：

```vue
<style scoped>
.custom-class {
  color: red;
  font-size: 16px;
}
</style>
```

## 其他问题

### Q16: 如何配置国际化翻译？

**答**：在 `locales` 目录下的语言文件中添加翻译，然后使用 `t` 函数：

```vue
<script setup>
import { t } from '@/expose/i18n'
</script>

<template>
  <h1>{{ t('system.wms.title') }}</h1>
</template>
```

### Q17: 开发环境和生产环境有什么区别？

**答**：
- 开发环境 (`pnpm dev`) 提供热更新、调试工具和更详细的错误信息
- 生产环境 (`pnpm build`) 优化代码体积，移除调试信息，提高性能

### Q18: 如何添加新的系统模块？

**答**：
1. 在 `src/config/system.ts` 中添加新模块配置
2. 在 `src/views` 下创建对应的模块目录
3. 在新模块目录中创建页面文件

```ts
// src/config/system.ts
export const systemList = [
  // 现有模块...
  {
    key: "newModule",
    homePath: "/newModule/dashboard/workbench",
  },
];
``` 