# 快速开始

## 环境准备

开始使用本框架进行开发前，您需要先安装以下软件：

1. **Node.js** (建议版本 16.x 或更高)
   - 这是运行 JavaScript 的环境，您可以在[Node.js 官网](https://nodejs.org/)下载安装

2. **包管理器**：推荐使用 pnpm
   ```bash
   npm install -g pnpm
   ```

## 安装项目

1. 克隆项目模板
   ```bash
   git clone [项目仓库地址]
   cd [项目名称]
   ```

2. 安装依赖
   ```bash
   pnpm install
   ```

## 启动开发服务器

执行以下命令启动开发服务器：
```bash
pnpm dev
```

启动成功后，您可以在浏览器中访问 [http://localhost:5173](http://localhost:5173) 查看项目。

## 项目结构

项目的主要目录结构如下：

```
项目根目录
├── src/                # 源代码目录
│   ├── assets/         # 静态资源（图片等）
│   ├── components/     # 组件库
│   ├── config/         # 配置文件
│   ├── hooks/          # 可复用的功能逻辑
│   ├── layouts/        # 页面布局
│   ├── router/         # 路由配置
│   ├── styles/         # 全局样式
│   ├── utils/          # 工具函数
│   ├── views/          # 页面
│   ├── App.vue         # 应用主组件
│   └── index.ts        # 应用入口
├── public/             # 不需要处理的静态资源
└── package.json        # 项目配置和依赖
```

## 构建项目

当您完成开发，需要部署时，执行以下命令构建项目：

```bash
pnpm build
```

构建完成后，生成的文件会存放在 `dist` 目录中，您可以将该目录部署到服务器。 