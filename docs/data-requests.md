# 数据请求指南

## 请求工具介绍

本框架提供了一个简单易用的 `useRequest` 函数，用于发送各种数据请求（如获取数据、提交表单等）。该函数基于 Axios 封装，支持各种 HTTP 方法（GET、POST、PUT、DELETE 等）。

## 基本用法

### 创建请求实例

```js
// 创建一个指向特定API路径的请求实例
const getUserList = useRequest('/user/list')
```

### 发送 GET 请求

```js
// 基本用法
const response = await getUserList.get()

// 带参数的 GET 请求
const response = await getUserList.get({ 
  params: { 
    page: 1, 
    size: 10,
    keyword: '搜索关键词' 
  } 
})

// 获取响应数据
const data = response.data.value
```

### 发送 POST 请求

```js
// 创建请求实例
const createUser = useRequest('/user/create')

// 发送 POST 请求
const response = await createUser.post({
  name: '张三',
  age: 30,
  email: 'z<PERSON><PERSON>@example.com'
})

// 处理响应
if (response.code.value === 0) {
  // 请求成功
  console.log('创建成功', response.data.value)
} else {
  // 请求失败
  console.error('创建失败', response.msg.value)
}
```

### 发送 PUT 请求（更新数据）

```js
// 创建请求实例
const updateUser = useRequest('/user/update')

// 发送 PUT 请求
const response = await updateUser.put({
  id: 123,
  name: '张三（已更新）',
  age: 31
})
```

### 发送 DELETE 请求（删除数据）

```js
// 创建请求实例
const deleteUser = useRequest('/user/delete')

// 发送 DELETE 请求
const response = await deleteUser.delete({
  params: { id: 123 }
})
```

## 处理请求响应

请求响应通常包含以下几个字段：

```js
{
  code: { value: 0 },           // 响应状态码，0 表示成功
  msg: { value: '操作成功' },    // 响应消息
  data: { value: [...] }        // 响应数据
}
```

您可以这样使用：

```js
const response = await getUserList.get()

// 检查请求是否成功
if (response.code.value === 0) {
  // 获取数据
  const users = response.data.value
  // 处理数据
  console.log('用户列表', users)
} else {
  // 处理错误
  console.error('获取用户列表失败', response.msg.value)
}
```

## 在组件中使用

```vue
<script setup>
import { ref, onMounted } from 'vue'

// 定义组件数据
const tableData = ref([])
const loading = ref(false)

// 创建请求实例
const getUserList = useRequest('/user/list')

// 加载数据函数
async function loadData() {
  loading.value = true
  try {
    const res = await getUserList.get({
      params: { page: 1, size: 10 }
    })
    
    if (res.code.value === 0) {
      tableData.value = res.data.value.items || []
    } else {
      // 可以使用框架提供的消息提示组件
      window.$message.error(res.msg.value || '加载数据失败')
    }
  } catch (error) {
    console.error('请求出错', error)
    window.$message.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<template>
  <div>
    <n-spin :show="loading">
      <!-- 显示数据的表格 -->
      <n-data-table :columns="columns" :data="tableData" />
    </n-spin>
  </div>
</template>
```

## 请求配置

您可以在框架中配置全局请求设置，比如基础URL、超时时间等。

```js
// src/index.ts
await setupApp({
  // ...其他配置
  request: {
    baseUrl: "/api",  // API基础路径
    // 其他请求配置...
  },
  // ...
});
```

## 处理文件上传

```vue
<script setup>
const uploadFile = useRequest('/file/upload')

async function handleFileUpload(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  const response = await uploadFile.post(formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
  
  if (response.code.value === 0) {
    console.log('上传成功', response.data.value)
  }
}
</script>

<template>
  <n-upload 
    action="#"
    :custom-request="handleFileUpload"
    :default-upload="false"
  >
    <n-button>上传文件</n-button>
  </n-upload>
</template>
```

## 请求错误处理

框架已经内置了基本的错误处理机制，包括网络错误、服务器错误等常见问题。如果您需要自定义错误处理，可以使用 try/catch 来捕获异常：

```js
try {
  const response = await getUserList.get()
  // 处理响应
} catch (error) {
  // 处理错误
  console.error('请求失败', error)
  window.$message.error('获取数据失败，请稍后重试')
}
```

## 最佳实践

1. **将 API 请求集中管理**：可以在一个单独的文件中定义所有 API 请求，方便管理和复用。

2. **使用合适的请求方法**：
   - GET：获取数据
   - POST：创建新数据
   - PUT/PATCH：更新数据
   - DELETE：删除数据

3. **添加加载状态**：在发送请求前设置 loading 状态，请求完成后重置，提高用户体验。

4. **处理所有响应**：无论请求成功还是失败，都应该有相应的处理逻辑。 